<?php

namespace App\Observers;

use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionCacheService;
use Illuminate\Support\Facades\Log;

class PermissionCacheObserver
{
    protected PermissionCacheService $permissionCache;

    public function __construct(PermissionCacheService $permissionCache)
    {
        $this->permissionCache = $permissionCache;
    }

    /**
     * Handle the Permission "created" event.
     */
    public function created(Permission $permission): void
    {
        $this->invalidateRelatedCaches($permission);
        Log::info('Permission created, cache invalidated', [
            'permission_id' => $permission->id_hak_akses,
            'role_id' => $permission->id_peran,
            'module' => $permission->modul
        ]);
    }

    /**
     * Handle the Permission "updated" event.
     */
    public function updated(Permission $permission): void
    {
        $this->invalidateRelatedCaches($permission);
        Log::info('Permission updated, cache invalidated', [
            'permission_id' => $permission->id_hak_akses,
            'role_id' => $permission->id_peran,
            'module' => $permission->modul,
            'changes' => $permission->getChanges()
        ]);
    }

    /**
     * Handle the Permission "deleted" event.
     */
    public function deleted(Permission $permission): void
    {
        $this->invalidateRelatedCaches($permission);
        Log::info('Permission deleted, cache invalidated', [
            'permission_id' => $permission->id_hak_akses,
            'role_id' => $permission->id_peran,
            'module' => $permission->modul
        ]);
    }

    /**
     * Invalidate related caches when permission changes
     */
    protected function invalidateRelatedCaches(Permission $permission): void
    {
        // Invalidate role cache
        $this->permissionCache->invalidateRoleCache($permission->id_peran);
    }
}

class RoleCacheObserver
{
    protected PermissionCacheService $permissionCache;

    public function __construct(PermissionCacheService $permissionCache)
    {
        $this->permissionCache = $permissionCache;
    }

    /**
     * Handle the Role "updated" event.
     */
    public function updated(Role $role): void
    {
        $this->permissionCache->invalidateRoleCache($role->id_peran);
        Log::info('Role updated, cache invalidated', [
            'role_id' => $role->id_peran,
            'role_name' => $role->nama_peran,
            'changes' => $role->getChanges()
        ]);
    }

    /**
     * Handle the Role "deleted" event.
     */
    public function deleted(Role $role): void
    {
        $this->permissionCache->invalidateRoleCache($role->id_peran);
        Log::info('Role deleted, cache invalidated', [
            'role_id' => $role->id_peran,
            'role_name' => $role->nama_peran
        ]);
    }
}

class UserCacheObserver
{
    protected PermissionCacheService $permissionCache;

    public function __construct(PermissionCacheService $permissionCache)
    {
        $this->permissionCache = $permissionCache;
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void
    {
        // Only invalidate if role changed
        if ($user->wasChanged('id_peran')) {
            $this->permissionCache->invalidateUserCache($user->id);
            Log::info('User role changed, cache invalidated', [
                'user_id' => $user->id,
                'old_role' => $user->getOriginal('id_peran'),
                'new_role' => $user->id_peran
            ]);
        }
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void
    {
        $this->permissionCache->invalidateUserCache($user->id);
        Log::info('User deleted, cache invalidated', [
            'user_id' => $user->id
        ]);
    }
}
