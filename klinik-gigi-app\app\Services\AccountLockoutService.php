<?php

namespace App\Services;

use App\Models\AuditLog;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;

class AccountLockoutService
{
    // Lockout configuration
    const MAX_FAILED_ATTEMPTS = 5;
    const PROGRESSIVE_LOCKOUT_ATTEMPTS = [
        3 => 5,    // 3 attempts = 5 minutes
        5 => 15,   // 5 attempts = 15 minutes
        7 => 30,   // 7 attempts = 30 minutes
        10 => 60,  // 10 attempts = 1 hour
        15 => 120, // 15 attempts = 2 hours
        20 => 240, // 20 attempts = 4 hours
        25 => 480, // 25 attempts = 8 hours
    ];
    const PERMANENT_LOCKOUT_THRESHOLD = 30;

    /**
     * Handle failed login attempt
     */
    public static function handleFailedAttempt(User $user, string $ipAddress = null): array
    {
        $ipAddress = $ipAddress ?? request()->ip();
        
        // Increment user failed attempts
        $user->incrementFailedAttempts();
        
        // Also track IP-based attempts to prevent distributed attacks
        self::incrementIpFailedAttempts($ipAddress);
        
        $result = [
            'user_locked' => false,
            'ip_locked' => false,
            'lock_duration' => 0,
            'attempts_remaining' => 0,
            'message' => ''
        ];

        // Check if user should be locked
        if ($user->failed_login_attempts >= self::MAX_FAILED_ATTEMPTS) {
            $lockDuration = self::calculateLockDuration($user->failed_login_attempts);
            
            if ($user->failed_login_attempts >= self::PERMANENT_LOCKOUT_THRESHOLD) {
                // Permanent lockout - requires admin intervention
                self::permanentLockout($user);
                $result['user_locked'] = true;
                $result['message'] = 'Akun Anda telah dikunci secara permanen. Hubungi administrator.';
            } else {
                // Temporary lockout
                $user->lockAccount($lockDuration);
                $result['user_locked'] = true;
                $result['lock_duration'] = $lockDuration;
                $result['message'] = "Akun terkunci selama {$lockDuration} menit karena terlalu banyak percobaan login gagal.";
            }

            // Log account lockout
            AuditLog::logAuth(AuditLog::ACTION_ACCOUNT_LOCKED, $user, [
                'failed_attempts' => $user->failed_login_attempts,
                'lock_duration_minutes' => $lockDuration,
                'ip_address' => $ipAddress,
                'permanent' => $user->failed_login_attempts >= self::PERMANENT_LOCKOUT_THRESHOLD
            ]);

            // Send notification email
            self::sendLockoutNotification($user, $lockDuration);
        } else {
            $result['attempts_remaining'] = self::MAX_FAILED_ATTEMPTS - $user->failed_login_attempts;
            $result['message'] = "Login gagal. {$result['attempts_remaining']} percobaan tersisa sebelum akun dikunci.";
        }

        // Check IP lockout
        $ipAttempts = self::getIpFailedAttempts($ipAddress);
        if ($ipAttempts >= 10) { // Lock IP after 10 failed attempts from any users
            self::lockIpAddress($ipAddress, 60); // 1 hour IP lockout
            $result['ip_locked'] = true;
        }

        return $result;
    }

    /**
     * Calculate lock duration based on failed attempts
     */
    public static function calculateLockDuration(int $failedAttempts): int
    {
        foreach (self::PROGRESSIVE_LOCKOUT_ATTEMPTS as $threshold => $duration) {
            if ($failedAttempts <= $threshold) {
                return $duration;
            }
        }
        
        // Default to 24 hours for excessive attempts
        return 1440;
    }

    /**
     * Permanent lockout
     */
    public static function permanentLockout(User $user): void
    {
        $user->update([
            'locked_until' => now()->addYears(10), // Effectively permanent
            'aktif' => false // Also deactivate account
        ]);

        // Log permanent lockout
        AuditLog::log(
            AuditLog::ACTION_ACCOUNT_LOCKED,
            User::class,
            $user->getKey(),
            null,
            ['permanent' => true, 'failed_attempts' => $user->failed_login_attempts],
            ['reason' => 'excessive_failed_attempts'],
            AuditLog::SEVERITY_CRITICAL
        );

        // Send admin notification
        self::sendAdminNotification($user, 'permanent_lockout');
    }

    /**
     * Unlock user account
     */
    public static function unlockAccount(User $user, ?User $unlockedBy = null): bool
    {
        if (!$user->isLocked()) {
            return false;
        }

        $user->unlockAccount();

        // Log account unlock
        AuditLog::logAuth(AuditLog::ACTION_ACCOUNT_UNLOCKED, $user, [
            'unlocked_by' => $unlockedBy?->full_name ?? 'System',
            'previous_failed_attempts' => $user->failed_login_attempts
        ]);

        return true;
    }

    /**
     * Check if IP address is locked
     */
    public static function isIpLocked(string $ipAddress): bool
    {
        return Cache::has("ip_locked_{$ipAddress}");
    }

    /**
     * Lock IP address
     */
    public static function lockIpAddress(string $ipAddress, int $minutes): void
    {
        Cache::put("ip_locked_{$ipAddress}", true, now()->addMinutes($minutes));
        
        // Log IP lockout
        AuditLog::log(
            'ip_locked',
            null,
            null,
            null,
            ['ip_address' => $ipAddress, 'duration_minutes' => $minutes],
            null,
            AuditLog::SEVERITY_HIGH
        );
    }

    /**
     * Increment IP failed attempts
     */
    private static function incrementIpFailedAttempts(string $ipAddress): void
    {
        $key = "ip_failed_attempts_{$ipAddress}";
        $attempts = Cache::get($key, 0) + 1;
        Cache::put($key, $attempts, now()->addHours(1)); // Reset after 1 hour
    }

    /**
     * Get IP failed attempts
     */
    private static function getIpFailedAttempts(string $ipAddress): int
    {
        return Cache::get("ip_failed_attempts_{$ipAddress}", 0);
    }

    /**
     * Reset IP failed attempts
     */
    public static function resetIpFailedAttempts(string $ipAddress): void
    {
        Cache::forget("ip_failed_attempts_{$ipAddress}");
    }

    /**
     * Send lockout notification to user
     */
    private static function sendLockoutNotification(User $user, int $lockDuration): void
    {
        // TODO: Implement email notification
        // This would send an email to the user about the lockout
        /*
        Mail::to($user->email)->send(new AccountLockedMail([
            'user' => $user,
            'lock_duration' => $lockDuration,
            'unlock_time' => now()->addMinutes($lockDuration)
        ]));
        */
    }

    /**
     * Send admin notification for permanent lockouts
     */
    private static function sendAdminNotification(User $user, string $type): void
    {
        // TODO: Implement admin notification
        // This would notify administrators about permanent lockouts
        /*
        $admins = User::whereHas('role', function($query) {
            $query->whereIn('nama_peran', ['Super Admin', 'Admin']);
        })->get();

        foreach ($admins as $admin) {
            Mail::to($admin->email)->send(new AdminNotificationMail([
                'type' => $type,
                'user' => $user,
                'admin' => $admin
            ]));
        }
        */
    }

    /**
     * Get lockout statistics
     */
    public static function getLockoutStats(): array
    {
        $stats = [
            'currently_locked' => User::whereNotNull('locked_until')
                ->where('locked_until', '>', now())
                ->count(),
            'permanently_locked' => User::where('locked_until', '>', now()->addYears(1))
                ->count(),
            'failed_attempts_today' => AuditLog::where('action', AuditLog::ACTION_LOGIN_FAILED)
                ->where('created_at', '>=', now()->startOfDay())
                ->count(),
            'lockouts_today' => AuditLog::where('action', AuditLog::ACTION_ACCOUNT_LOCKED)
                ->where('created_at', '>=', now()->startOfDay())
                ->count()
        ];

        return $stats;
    }

    /**
     * Get users requiring admin attention
     */
    public static function getUsersRequiringAttention(): array
    {
        return [
            'permanently_locked' => User::where('locked_until', '>', now()->addYears(1))
                ->with('role')
                ->get(),
            'high_failed_attempts' => User::where('failed_login_attempts', '>=', 20)
                ->where('failed_login_attempts', '<', self::PERMANENT_LOCKOUT_THRESHOLD)
                ->with('role')
                ->get(),
            'recently_locked' => User::whereNotNull('locked_until')
                ->where('locked_until', '>', now())
                ->where('locked_until', '<=', now()->addHours(24))
                ->with('role')
                ->get()
        ];
    }

    /**
     * Cleanup expired lockouts
     */
    public static function cleanupExpiredLockouts(): int
    {
        return User::whereNotNull('locked_until')
            ->where('locked_until', '<=', now())
            ->update(['locked_until' => null]);
    }

    /**
     * Reset failed attempts for user (admin action)
     */
    public static function resetFailedAttempts(User $user, ?User $resetBy = null): void
    {
        $oldAttempts = $user->failed_login_attempts;
        
        $user->update(['failed_login_attempts' => 0]);

        // Log reset action
        AuditLog::logUserManagement(
            'failed_attempts_reset',
            $user,
            ['failed_attempts' => $oldAttempts],
            ['failed_attempts' => 0],
            ['reset_by' => $resetBy?->full_name ?? 'System']
        );
    }
}
