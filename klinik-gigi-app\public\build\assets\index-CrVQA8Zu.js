import{e as p,g as u,r as f,j as l}from"./app-EmUGGW4y.js";import{d}from"./button-B8QorGO4.js";var e=p();const h=u(e);var v=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],D=v.reduce((r,t)=>{const s=d(`Primitive.${t}`),i=f.forwardRef((o,a)=>{const{asChild:m,...n}=o,c=m?s:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),l.jsx(c,{...n,ref:a})});return i.displayName=`Primitive.${t}`,{...r,[t]:i}},{});function w(r,t){r&&e.flushSync(()=>r.dispatchEvent(t))}export{D as P,h as R,w as d,e as r};
