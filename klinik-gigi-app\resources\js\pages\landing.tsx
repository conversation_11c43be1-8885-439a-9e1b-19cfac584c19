import { Head, <PERSON> } from '@inertiajs/react';
import {
    Calendar,
    Users,
    FileText,
    CreditCard,
    Shield,
    Smartphone,
    CheckCircle,
    Star,
    ArrowRight,
    Phone,
    Mail,
    MapPin
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function Landing() {
    const features = [
        {
            icon: Users,
            title: "Manajemen Pasien",
            description: "Kelola data pasien lengkap dengan NIK, riwayat keluarga, dan integrasi BPJS"
        },
        {
            icon: Calendar,
            title: "<PERSON>adwal & Janji <PERSON>",
            description: "Sistem penjadwalan otomatis dengan reminder dan manajemen ruang perawatan"
        },
        {
            icon: FileText,
            title: "Rekam Medis Digital",
            description: "EMR lengkap dengan dental charting visual dan dokumentasi gambar medis"
        },
        {
            icon: CreditCard,
            title: "Sistem Pembayaran",
            description: "Multi-metode pembayaran dengan cicilan dan integrasi payment gateway"
        },
        {
            icon: Shield,
            title: "Keamanan Data",
            description: "Enkripsi tingkat enterprise dengan audit trail dan backup otomatis"
        },
        {
            icon: Smartphone,
            title: "Mobile Ready",
            description: "Akses dari mana saja dengan aplikasi mobile untuk dokter dan pasien"
        }
    ];

    const benefits = [
        "Tingkatkan efisiensi operasional hingga 60%",
        "Kurangi waktu administrasi hingga 40%",
        "Integrasi lengkap dengan BPJS dan asuransi",
        "Laporan keuangan real-time dan analytics",
        "Sistem reminder otomatis untuk pasien",
        "Backup data otomatis dan disaster recovery"
    ];

    const testimonials = [
        {
            name: "Dr. Sarah Wijaya",
            role: "Direktur Klinik Gigi Sehat",
            content: "Aplikasi ini benar-benar mengubah cara kami mengelola klinik. Efisiensi meningkat drastis dan pasien lebih puas dengan layanan kami.",
            rating: 5
        },
        {
            name: "Dr. Ahmad Rizki",
            role: "Dokter Gigi Spesialis",
            content: "Fitur dental charting dan rekam medis digital sangat membantu dalam dokumentasi perawatan. Highly recommended!",
            rating: 5
        },
        {
            name: "Siti Nurhaliza",
            role: "Manajer Klinik Dental Care",
            content: "Sistem pembayaran dan billing yang terintegrasi membuat pengelolaan keuangan menjadi sangat mudah dan transparan.",
            rating: 5
        }
    ];

    return (
        <>
            <Head title="Sistem Manajemen Klinik Gigi Terdepan di Indonesia" />

            {/* Navigation */}
            <nav className="fixed top-0 w-full bg-white/95 backdrop-blur-sm border-b border-gray-200 z-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <h1 className="text-2xl font-bold text-blue-600">DentalPro</h1>
                            </div>
                        </div>
                        <div className="hidden md:block">
                            <div className="ml-10 flex items-baseline space-x-4">
                                <a href="#features" className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">Fitur</a>
                                <a href="#benefits" className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">Keunggulan</a>
                                <a href="#testimonials" className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">Testimoni</a>
                                <a href="#pricing" className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">Harga</a>
                                <Link href="/login" className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                                    Login
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>

            {/* Hero Section */}
            <section className="pt-20 pb-16 bg-gradient-to-br from-blue-50 to-indigo-100">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                            Revolusi Digital untuk
                            <span className="text-blue-600"> Klinik Gigi</span> Anda
                        </h1>
                        <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                            Sistem manajemen klinik gigi terlengkap di Indonesia dengan fitur EMR, dental charting,
                            integrasi BPJS, dan payment gateway. Tingkatkan efisiensi dan kepuasan pasien Anda.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
                                Coba Gratis 30 Hari
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Button>
                            <Button variant="outline" size="lg" className="px-8 py-3">
                                Lihat Demo
                            </Button>
                        </div>
                        <div className="mt-8 text-sm text-gray-500">
                            ✓ Setup gratis ✓ Training included ✓ Support 24/7
                        </div>
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section id="features" className="py-16 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                            Fitur Lengkap untuk Klinik Modern
                        </h2>
                        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                            Semua yang Anda butuhkan untuk mengelola klinik gigi dalam satu platform terintegrasi
                        </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {features.map((feature, index) => (
                            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                                <CardHeader>
                                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                                        <feature.icon className="h-6 w-6 text-blue-600" />
                                    </div>
                                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <CardDescription className="text-gray-600">
                                        {feature.description}
                                    </CardDescription>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* Benefits Section */}
            <section id="benefits" className="py-16 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                        <div>
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                                Mengapa Memilih DentalPro?
                            </h2>
                            <p className="text-lg text-gray-600 mb-8">
                                Lebih dari 500+ klinik gigi di Indonesia telah mempercayai DentalPro
                                untuk meningkatkan efisiensi operasional dan kepuasan pasien.
                            </p>
                            <div className="space-y-4">
                                {benefits.map((benefit, index) => (
                                    <div key={index} className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                                        <span className="text-gray-700">{benefit}</span>
                                    </div>
                                ))}
                            </div>
                        </div>
                        <div className="relative">
                            <div className="bg-white rounded-lg shadow-2xl p-8">
                                <div className="text-center">
                                    <div className="text-4xl font-bold text-blue-600 mb-2">500+</div>
                                    <div className="text-gray-600 mb-6">Klinik Terdaftar</div>
                                    <div className="grid grid-cols-2 gap-4 text-center">
                                        <div>
                                            <div className="text-2xl font-bold text-gray-900">98%</div>
                                            <div className="text-sm text-gray-600">Kepuasan Pengguna</div>
                                        </div>
                                        <div>
                                            <div className="text-2xl font-bold text-gray-900">24/7</div>
                                            <div className="text-sm text-gray-600">Customer Support</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Testimonials Section */}
            <section id="testimonials" className="py-16 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                            Apa Kata Pengguna Kami
                        </h2>
                        <p className="text-xl text-gray-600">
                            Testimoni dari dokter dan manajer klinik yang telah merasakan manfaatnya
                        </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        {testimonials.map((testimonial, index) => (
                            <Card key={index} className="border-0 shadow-lg">
                                <CardContent className="pt-6">
                                    <div className="flex mb-4">
                                        {[...Array(testimonial.rating)].map((_, i) => (
                                            <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                                        ))}
                                    </div>
                                    <p className="text-gray-600 mb-6 italic">"{testimonial.content}"</p>
                                    <div>
                                        <div className="font-semibold text-gray-900">{testimonial.name}</div>
                                        <div className="text-sm text-gray-500">{testimonial.role}</div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* Pricing Section */}
            <section id="pricing" className="py-16 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                            Paket Berlangganan Fleksibel
                        </h2>
                        <p className="text-xl text-gray-600">
                            Pilih paket yang sesuai dengan kebutuhan klinik Anda
                        </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        {/* Starter Plan */}
                        <Card className="border-2 border-gray-200 shadow-lg">
                            <CardHeader className="text-center">
                                <CardTitle className="text-2xl">Starter</CardTitle>
                                <CardDescription>Untuk klinik kecil</CardDescription>
                                <div className="mt-4">
                                    <span className="text-4xl font-bold">Rp 299K</span>
                                    <span className="text-gray-600">/bulan</span>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <ul className="space-y-3">
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>Hingga 3 dokter</span>
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>1000 pasien aktif</span>
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>Manajemen janji temu</span>
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>Rekam medis digital</span>
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>Billing & payment</span>
                                    </li>
                                </ul>
                                <Button className="w-full mt-6" variant="outline">
                                    Mulai Gratis
                                </Button>
                            </CardContent>
                        </Card>

                        {/* Professional Plan */}
                        <Card className="border-2 border-blue-500 shadow-xl relative">
                            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                                    Paling Populer
                                </span>
                            </div>
                            <CardHeader className="text-center">
                                <CardTitle className="text-2xl">Professional</CardTitle>
                                <CardDescription>Untuk klinik menengah</CardDescription>
                                <div className="mt-4">
                                    <span className="text-4xl font-bold">Rp 599K</span>
                                    <span className="text-gray-600">/bulan</span>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <ul className="space-y-3">
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>Hingga 10 dokter</span>
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>5000 pasien aktif</span>
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>Semua fitur Starter</span>
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>Dental charting</span>
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>Integrasi BPJS</span>
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>Inventory management</span>
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>Advanced reporting</span>
                                    </li>
                                </ul>
                                <Button className="w-full mt-6 bg-blue-600 hover:bg-blue-700">
                                    Pilih Paket Ini
                                </Button>
                            </CardContent>
                        </Card>

                        {/* Enterprise Plan */}
                        <Card className="border-2 border-gray-200 shadow-lg">
                            <CardHeader className="text-center">
                                <CardTitle className="text-2xl">Enterprise</CardTitle>
                                <CardDescription>Untuk klinik besar</CardDescription>
                                <div className="mt-4">
                                    <span className="text-4xl font-bold">Rp 999K</span>
                                    <span className="text-gray-600">/bulan</span>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <ul className="space-y-3">
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>Unlimited dokter</span>
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>Unlimited pasien</span>
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>Semua fitur Professional</span>
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>Multi-location support</span>
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>API integration</span>
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>Dedicated support</span>
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                                        <span>Custom development</span>
                                    </li>
                                </ul>
                                <Button className="w-full mt-6" variant="outline">
                                    Hubungi Sales
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                    <div className="text-center mt-12">
                        <p className="text-gray-600 mb-4">
                            Semua paket termasuk setup gratis, training, dan support 24/7
                        </p>
                        <p className="text-sm text-gray-500">
                            Harga belum termasuk PPN 11%. Pembayaran tahunan mendapat diskon 20%.
                        </p>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-16 bg-blue-600">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                        Siap Mengembangkan Klinik Anda?
                    </h2>
                    <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                        Bergabunglah dengan 500+ klinik gigi yang telah merasakan manfaat DentalPro.
                        Mulai trial gratis 30 hari tanpa komitmen.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3">
                            Coba Gratis Sekarang
                            <ArrowRight className="ml-2 h-5 w-5" />
                        </Button>
                        <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3">
                            Jadwalkan Demo
                        </Button>
                    </div>
                </div>
            </section>

            {/* Footer */}
            <footer className="bg-gray-900 text-white py-12">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                        <div>
                            <h3 className="text-2xl font-bold text-blue-400 mb-4">DentalPro</h3>
                            <p className="text-gray-300 mb-4">
                                Sistem manajemen klinik gigi terdepan di Indonesia.
                                Membantu klinik gigi meningkatkan efisiensi dan kepuasan pasien.
                            </p>
                            <div className="flex space-x-4">
                                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                    <span className="text-sm font-bold">f</span>
                                </div>
                                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                    <span className="text-sm font-bold">t</span>
                                </div>
                                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                    <span className="text-sm font-bold">in</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 className="text-lg font-semibold mb-4">Produk</h4>
                            <ul className="space-y-2 text-gray-300">
                                <li><a href="#" className="hover:text-white">Fitur Lengkap</a></li>
                                <li><a href="#" className="hover:text-white">Harga</a></li>
                                <li><a href="#" className="hover:text-white">Demo</a></li>
                                <li><a href="#" className="hover:text-white">API Documentation</a></li>
                            </ul>
                        </div>
                        <div>
                            <h4 className="text-lg font-semibold mb-4">Support</h4>
                            <ul className="space-y-2 text-gray-300">
                                <li><a href="#" className="hover:text-white">Help Center</a></li>
                                <li><a href="#" className="hover:text-white">Training</a></li>
                                <li><a href="#" className="hover:text-white">Community</a></li>
                                <li><a href="#" className="hover:text-white">Status</a></li>
                            </ul>
                        </div>
                        <div>
                            <h4 className="text-lg font-semibold mb-4">Kontak</h4>
                            <div className="space-y-3 text-gray-300">
                                <div className="flex items-center">
                                    <Phone className="h-5 w-5 mr-3" />
                                    <span>+62 21 1234 5678</span>
                                </div>
                                <div className="flex items-center">
                                    <Mail className="h-5 w-5 mr-3" />
                                    <span><EMAIL></span>
                                </div>
                                <div className="flex items-center">
                                    <MapPin className="h-5 w-5 mr-3" />
                                    <span>Jakarta, Indonesia</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                        <p>&copy; 2024 DentalPro. All rights reserved. | Privacy Policy | Terms of Service</p>
                    </div>
                </div>
            </footer>
        </>
    );
}
