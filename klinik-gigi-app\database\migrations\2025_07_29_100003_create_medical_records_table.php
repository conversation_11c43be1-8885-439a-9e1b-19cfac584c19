<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('medical_records', function (Blueprint $table) {
            $table->id('id_rekam_medis');
            $table->unsignedBigInteger('id_pasien');
            $table->unsignedBigInteger('id_janji_temu')->nullable();
            $table->unsignedBigInteger('id_dokter');
            $table->datetime('tanggal_kunjungan');
            $table->text('keluhan_utama')->nullable();
            $table->text('pemeriksaan_objektif')->nullable();
            $table->text('diagnosis')->nullable();
            $table->text('rencana_perawatan')->nullable();
            $table->text('catatan_klinis')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Foreign key constraints
            $table->foreign('id_pasien')->references('id_pasien')->on('patients')->onDelete('cascade');
            $table->foreign('id_janji_temu')->references('id_janji_temu')->on('appointments')->onDelete('set null');
            $table->foreign('id_dokter')->references('id')->on('users')->onDelete('cascade');

            // Indexes untuk performance
            $table->index(['id_pasien', 'tanggal_kunjungan'], 'idx_medical_records_patient_date');
            $table->index(['id_dokter', 'tanggal_kunjungan'], 'idx_medical_records_doctor_date');
            $table->index(['id_janji_temu'], 'idx_medical_records_appointment');
            $table->index(['tanggal_kunjungan'], 'idx_medical_records_visit_date');
            $table->index(['created_at'], 'idx_medical_records_created');
            
            // Composite indexes untuk complex queries
            $table->index(['id_pasien', 'id_dokter', 'tanggal_kunjungan'], 'idx_medical_records_patient_doctor_date');
        });

        // Dental charting table
        Schema::create('dental_chartings', function (Blueprint $table) {
            $table->id('id_charting');
            $table->unsignedBigInteger('id_rekam_medis');
            $table->integer('nomor_gigi');
            $table->string('kondisi_gigi');
            $table->text('keterangan')->nullable();
            $table->string('warna_status')->nullable();
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('id_rekam_medis')->references('id_rekam_medis')->on('medical_records')->onDelete('cascade');

            // Indexes
            $table->index(['id_rekam_medis', 'nomor_gigi'], 'idx_dental_charting_record_tooth');
            $table->index(['nomor_gigi'], 'idx_dental_charting_tooth');
            $table->index(['kondisi_gigi'], 'idx_dental_charting_condition');
        });

        // Treatment rooms table
        Schema::create('treatment_rooms', function (Blueprint $table) {
            $table->id('id_ruang');
            $table->string('nama_ruang')->unique();
            $table->text('deskripsi')->nullable();
            $table->boolean('aktif')->default(true);
            $table->text('peralatan')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['nama_ruang']);
            $table->index(['aktif'], 'idx_treatment_rooms_active');
        });

        // Add foreign key for appointments table
        Schema::table('appointments', function (Blueprint $table) {
            $table->foreign('id_ruang_perawatan')->references('id_ruang')->on('treatment_rooms')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop foreign key first
        Schema::table('appointments', function (Blueprint $table) {
            $table->dropForeign(['id_ruang_perawatan']);
        });

        Schema::dropIfExists('dental_chartings');
        Schema::dropIfExists('treatment_rooms');
        Schema::dropIfExists('medical_records');
    }
};
