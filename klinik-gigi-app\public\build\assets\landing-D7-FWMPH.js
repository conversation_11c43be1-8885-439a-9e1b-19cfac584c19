import{j as e,H as p,L as N}from"./app-EmUGGW4y.js";import{a as s,B as i}from"./button-B8QorGO4.js";import{C as n,a as c,b as d,c as r,d as x}from"./card-iDZcqAij.js";/* empty css            *//**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],m=s("ArrowRight",u);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],y=s("Calendar",b);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],a=s("CircleCheckBig",k);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f=[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]],v=s("CreditCard",f);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],M=s("FileText",w);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]],P=s("Mail",S);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],_=s("MapPin",C);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]],A=s("Phone",D);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const K=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],H=s("Shield",K);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z=[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]],L=s("Smartphone",z);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],R=s("Star",I);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]],B=s("Users",T);function q(){const h=[{icon:B,title:"Manajemen Pasien",description:"Kelola data pasien lengkap dengan NIK, riwayat keluarga, dan integrasi BPJS"},{icon:y,title:"Jadwal & Janji Temu",description:"Sistem penjadwalan otomatis dengan reminder dan manajemen ruang perawatan"},{icon:M,title:"Rekam Medis Digital",description:"EMR lengkap dengan dental charting visual dan dokumentasi gambar medis"},{icon:v,title:"Sistem Pembayaran",description:"Multi-metode pembayaran dengan cicilan dan integrasi payment gateway"},{icon:H,title:"Keamanan Data",description:"Enkripsi tingkat enterprise dengan audit trail dan backup otomatis"},{icon:L,title:"Mobile Ready",description:"Akses dari mana saja dengan aplikasi mobile untuk dokter dan pasien"}],o=["Tingkatkan efisiensi operasional hingga 60%","Kurangi waktu administrasi hingga 40%","Integrasi lengkap dengan BPJS dan asuransi","Laporan keuangan real-time dan analytics","Sistem reminder otomatis untuk pasien","Backup data otomatis dan disaster recovery"],g=[{name:"Dr. Sarah Wijaya",role:"Direktur Klinik Gigi Sehat",content:"Aplikasi ini benar-benar mengubah cara kami mengelola klinik. Efisiensi meningkat drastis dan pasien lebih puas dengan layanan kami.",rating:5},{name:"Dr. Ahmad Rizki",role:"Dokter Gigi Spesialis",content:"Fitur dental charting dan rekam medis digital sangat membantu dalam dokumentasi perawatan. Highly recommended!",rating:5},{name:"Siti Nurhaliza",role:"Manajer Klinik Dental Care",content:"Sistem pembayaran dan billing yang terintegrasi membuat pengelolaan keuangan menjadi sangat mudah dan transparan.",rating:5}];return e.jsxs(e.Fragment,{children:[e.jsx(p,{title:"Sistem Manajemen Klinik Gigi Terdepan di Indonesia"}),e.jsx("nav",{className:"fixed top-0 w-full bg-white/95 backdrop-blur-sm border-b border-gray-200 z-50",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsx("div",{className:"flex items-center",children:e.jsx("div",{className:"flex-shrink-0",children:e.jsx("h1",{className:"text-2xl font-bold text-blue-600",children:"DentalPro"})})}),e.jsx("div",{className:"hidden md:block",children:e.jsxs("div",{className:"ml-10 flex items-baseline space-x-4",children:[e.jsx("a",{href:"#features",className:"text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium",children:"Fitur"}),e.jsx("a",{href:"#benefits",className:"text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium",children:"Keunggulan"}),e.jsx("a",{href:"#testimonials",className:"text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium",children:"Testimoni"}),e.jsx("a",{href:"#pricing",className:"text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium",children:"Harga"}),e.jsx(N,{href:"/login",className:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700",children:"Login"})]})})]})})}),e.jsx("section",{className:"pt-20 pb-16 bg-gradient-to-br from-blue-50 to-indigo-100",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:["Revolusi Digital untuk",e.jsx("span",{className:"text-blue-600",children:" Klinik Gigi"})," Anda"]}),e.jsx("p",{className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:"Sistem manajemen klinik gigi terlengkap di Indonesia dengan fitur EMR, dental charting, integrasi BPJS, dan payment gateway. Tingkatkan efisiensi dan kepuasan pasien Anda."}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsxs(i,{size:"lg",className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3",children:["Coba Gratis 30 Hari",e.jsx(m,{className:"ml-2 h-5 w-5"})]}),e.jsx(i,{variant:"outline",size:"lg",className:"px-8 py-3",children:"Lihat Demo"})]}),e.jsx("div",{className:"mt-8 text-sm text-gray-500",children:"✓ Setup gratis ✓ Training included ✓ Support 24/7"})]})})}),e.jsx("section",{id:"features",className:"py-16 bg-white",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Fitur Lengkap untuk Klinik Modern"}),e.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Semua yang Anda butuhkan untuk mengelola klinik gigi dalam satu platform terintegrasi"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:h.map((t,l)=>e.jsxs(n,{className:"border-0 shadow-lg hover:shadow-xl transition-shadow",children:[e.jsxs(c,{children:[e.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4",children:e.jsx(t.icon,{className:"h-6 w-6 text-blue-600"})}),e.jsx(d,{className:"text-xl",children:t.title})]}),e.jsx(r,{children:e.jsx(x,{className:"text-gray-600",children:t.description})})]},l))})]})}),e.jsx("section",{id:"benefits",className:"py-16 bg-gray-50",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Mengapa Memilih DentalPro?"}),e.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"Lebih dari 500+ klinik gigi di Indonesia telah mempercayai DentalPro untuk meningkatkan efisiensi operasional dan kepuasan pasien."}),e.jsx("div",{className:"space-y-4",children:o.map((t,l)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3 flex-shrink-0"}),e.jsx("span",{className:"text-gray-700",children:t})]},l))})]}),e.jsx("div",{className:"relative",children:e.jsx("div",{className:"bg-white rounded-lg shadow-2xl p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-4xl font-bold text-blue-600 mb-2",children:"500+"}),e.jsx("div",{className:"text-gray-600 mb-6",children:"Klinik Terdaftar"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-center",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:"98%"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Kepuasan Pengguna"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:"24/7"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Customer Support"})]})]})]})})})]})})}),e.jsx("section",{id:"testimonials",className:"py-16 bg-white",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Apa Kata Pengguna Kami"}),e.jsx("p",{className:"text-xl text-gray-600",children:"Testimoni dari dokter dan manajer klinik yang telah merasakan manfaatnya"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:g.map((t,l)=>e.jsx(n,{className:"border-0 shadow-lg",children:e.jsxs(r,{className:"pt-6",children:[e.jsx("div",{className:"flex mb-4",children:[...Array(t.rating)].map(($,j)=>e.jsx(R,{className:"h-5 w-5 text-yellow-400 fill-current"},j))}),e.jsxs("p",{className:"text-gray-600 mb-6 italic",children:['"',t.content,'"']}),e.jsxs("div",{children:[e.jsx("div",{className:"font-semibold text-gray-900",children:t.name}),e.jsx("div",{className:"text-sm text-gray-500",children:t.role})]})]})},l))})]})}),e.jsx("section",{id:"pricing",className:"py-16 bg-gray-50",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Paket Berlangganan Fleksibel"}),e.jsx("p",{className:"text-xl text-gray-600",children:"Pilih paket yang sesuai dengan kebutuhan klinik Anda"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[e.jsxs(n,{className:"border-2 border-gray-200 shadow-lg",children:[e.jsxs(c,{className:"text-center",children:[e.jsx(d,{className:"text-2xl",children:"Starter"}),e.jsx(x,{children:"Untuk klinik kecil"}),e.jsxs("div",{className:"mt-4",children:[e.jsx("span",{className:"text-4xl font-bold",children:"Rp 299K"}),e.jsx("span",{className:"text-gray-600",children:"/bulan"})]})]}),e.jsxs(r,{children:[e.jsxs("ul",{className:"space-y-3",children:[e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"Hingga 3 dokter"})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"1000 pasien aktif"})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"Manajemen janji temu"})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"Rekam medis digital"})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"Billing & payment"})]})]}),e.jsx(i,{className:"w-full mt-6",variant:"outline",children:"Mulai Gratis"})]})]}),e.jsxs(n,{className:"border-2 border-blue-500 shadow-xl relative",children:[e.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:e.jsx("span",{className:"bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium",children:"Paling Populer"})}),e.jsxs(c,{className:"text-center",children:[e.jsx(d,{className:"text-2xl",children:"Professional"}),e.jsx(x,{children:"Untuk klinik menengah"}),e.jsxs("div",{className:"mt-4",children:[e.jsx("span",{className:"text-4xl font-bold",children:"Rp 599K"}),e.jsx("span",{className:"text-gray-600",children:"/bulan"})]})]}),e.jsxs(r,{children:[e.jsxs("ul",{className:"space-y-3",children:[e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"Hingga 10 dokter"})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"5000 pasien aktif"})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"Semua fitur Starter"})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"Dental charting"})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"Integrasi BPJS"})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"Inventory management"})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"Advanced reporting"})]})]}),e.jsx(i,{className:"w-full mt-6 bg-blue-600 hover:bg-blue-700",children:"Pilih Paket Ini"})]})]}),e.jsxs(n,{className:"border-2 border-gray-200 shadow-lg",children:[e.jsxs(c,{className:"text-center",children:[e.jsx(d,{className:"text-2xl",children:"Enterprise"}),e.jsx(x,{children:"Untuk klinik besar"}),e.jsxs("div",{className:"mt-4",children:[e.jsx("span",{className:"text-4xl font-bold",children:"Rp 999K"}),e.jsx("span",{className:"text-gray-600",children:"/bulan"})]})]}),e.jsxs(r,{children:[e.jsxs("ul",{className:"space-y-3",children:[e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"Unlimited dokter"})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"Unlimited pasien"})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"Semua fitur Professional"})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"Multi-location support"})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"API integration"})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"Dedicated support"})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx(a,{className:"h-5 w-5 text-green-500 mr-3"}),e.jsx("span",{children:"Custom development"})]})]}),e.jsx(i,{className:"w-full mt-6",variant:"outline",children:"Hubungi Sales"})]})]})]}),e.jsxs("div",{className:"text-center mt-12",children:[e.jsx("p",{className:"text-gray-600 mb-4",children:"Semua paket termasuk setup gratis, training, dan support 24/7"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Harga belum termasuk PPN 11%. Pembayaran tahunan mendapat diskon 20%."})]})]})}),e.jsx("section",{className:"py-16 bg-blue-600",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"Siap Mengembangkan Klinik Anda?"}),e.jsx("p",{className:"text-xl text-blue-100 mb-8 max-w-2xl mx-auto",children:"Bergabunglah dengan 500+ klinik gigi yang telah merasakan manfaat DentalPro. Mulai trial gratis 30 hari tanpa komitmen."}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsxs(i,{size:"lg",className:"bg-white text-blue-600 hover:bg-gray-100 px-8 py-3",children:["Coba Gratis Sekarang",e.jsx(m,{className:"ml-2 h-5 w-5"})]}),e.jsx(i,{variant:"outline",size:"lg",className:"border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3",children:"Jadwalkan Demo"})]})]})}),e.jsx("footer",{className:"bg-gray-900 text-white py-12",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-2xl font-bold text-blue-400 mb-4",children:"DentalPro"}),e.jsx("p",{className:"text-gray-300 mb-4",children:"Sistem manajemen klinik gigi terdepan di Indonesia. Membantu klinik gigi meningkatkan efisiensi dan kepuasan pasien."}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-sm font-bold",children:"f"})}),e.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-sm font-bold",children:"t"})}),e.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-sm font-bold",children:"in"})})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Produk"}),e.jsxs("ul",{className:"space-y-2 text-gray-300",children:[e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-white",children:"Fitur Lengkap"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-white",children:"Harga"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-white",children:"Demo"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-white",children:"API Documentation"})})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Support"}),e.jsxs("ul",{className:"space-y-2 text-gray-300",children:[e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-white",children:"Help Center"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-white",children:"Training"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-white",children:"Community"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-white",children:"Status"})})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Kontak"}),e.jsxs("div",{className:"space-y-3 text-gray-300",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(A,{className:"h-5 w-5 mr-3"}),e.jsx("span",{children:"+62 21 1234 5678"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(P,{className:"h-5 w-5 mr-3"}),e.jsx("span",{children:"<EMAIL>"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(_,{className:"h-5 w-5 mr-3"}),e.jsx("span",{children:"Jakarta, Indonesia"})]})]})]})]}),e.jsx("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:e.jsx("p",{children:"© 2024 DentalPro. All rights reserved. | Privacy Policy | Terms of Service"})})]})})]})}export{q as default};
