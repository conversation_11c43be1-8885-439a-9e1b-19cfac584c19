import{u as n,j as e,H as d}from"./app-EmUGGW4y.js";import{L as c,I as p,a as u}from"./label-BIgw4gz0.js";import{T as x}from"./text-link-Z88TWqmf.js";import{B as f}from"./button-B8QorGO4.js";import{A as j,L as h}from"./auth-layout-CTfIlSls.js";/* empty css            */import"./index-CrVQA8Zu.js";import"./app-logo-icon-BW5sZeJe.js";function E({status:t}){const{data:r,setData:i,post:o,processing:a,errors:l}=n({email:""}),m=s=>{s.preventDefault(),o(route("password.email"))};return e.jsxs(j,{title:"Forgot password",description:"Enter your email to receive a password reset link",children:[e.jsx(d,{title:"Forgot password"}),t&&e.jsx("div",{className:"mb-4 text-center text-sm font-medium text-green-600",children:t}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("form",{onSubmit:m,children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(c,{htmlFor:"email",children:"Email address"}),e.jsx(p,{id:"email",type:"email",name:"email",autoComplete:"off",value:r.email,autoFocus:!0,onChange:s=>i("email",s.target.value),placeholder:"<EMAIL>"}),e.jsx(u,{message:l.email})]}),e.jsx("div",{className:"my-6 flex items-center justify-start",children:e.jsxs(f,{className:"w-full",disabled:a,children:[a&&e.jsx(h,{className:"h-4 w-4 animate-spin"}),"Email password reset link"]})})]}),e.jsxs("div",{className:"space-x-1 text-center text-sm text-muted-foreground",children:[e.jsx("span",{children:"Or, return to"}),e.jsx(x,{href:route("login"),children:"log in"})]})]})]})}export{E as default};
