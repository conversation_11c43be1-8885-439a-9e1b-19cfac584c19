# 🚨 Troubleshooting Guide - Error 500 Fix

## Status Aplikasi
**Current Status**: Error 500 saat mengakses aplikasi
**Last Working State**: Aplikasi berfungsi normal sebelum implementasi Priority 2 features

## Masalah yang Teridentifikasi

### 1. Konflik Property `$defaultWith` ✅ FIXED
**Problem**: Model User dan Trait OptimizedQueries memiliki property yang sama
**Solution**: Menghapus trait OptimizedQueries dari User model sementara

### 2. Middleware Performance Issues ⚠️ ONGOING
**Problem**: QueryPerformanceMiddleware dan N1DetectionMiddleware menyebabkan error 500
**Current Status**: Middleware di-disable sementara di bootstrap/app.php

### 3. Method `toISOString()` Issues ✅ FIXED
**Problem**: Carbon tidak memiliki method toISOString()
**Solution**: Diganti dengan toDateTimeString()

## Quick Fix untuk Menjalankan Aplikasi

### Step 1: Disable Performance Middleware
File: `bootstrap/app.php`
```php
$middleware->web(append: [
    // QueryPerformanceMiddleware::class, // Temporarily disabled - causing 500 errors
    // N1DetectionMiddleware::class, // Temporarily disabled
    HandleAppearance::class,
    HandleInertiaRequests::class,
    AddLinkHeadersForPreloadedAssets::class,
]);
```

### Step 2: Remove OptimizedQueries Trait dari User Model
File: `app/Models/User.php`
```php
// BEFORE
use HasFactory, Notifiable, OptimizedQueries;

// AFTER
use HasFactory, Notifiable;
```

### Step 3: Clear All Caches
```bash
php artisan optimize:clear
php artisan cache:clear
php artisan config:clear
php artisan route:clear
```

### Step 4: Restart Server
```bash
php artisan serve --host=0.0.0.0 --port=8000
```

## Features yang Masih Berfungsi

### ✅ Working Features:
1. **Permission Caching System** - Service dan commands berfungsi
2. **Database Indexing** - Migrations dan analyzer commands berfungsi  
3. **Soft Delete Implementation** - Models dan commands berfungsi
4. **Query Optimization Service** - Core functionality berfungsi

### ⚠️ Temporarily Disabled:
1. **Real-time Query Performance Monitoring** - Middleware disabled
2. **N+1 Problem Detection Middleware** - Middleware disabled
3. **OptimizedQueries Trait** - Removed from User model

## Commands yang Masih Bisa Digunakan

```bash
# Permission caching
php artisan permission:cache stats
php artisan permission:cache clear

# Database analysis
php artisan db:analyze-indexes stats
php artisan db:analyze-indexes missing

# Query optimization
php artisan query:optimize demo
php artisan query:performance report

# Soft delete management
php artisan soft-delete:manage stats
php artisan soft-delete:manage restore
```

## Next Steps untuk Full Recovery

1. **Debug Middleware Issues**
   - Investigate QueryPerformanceService::startMonitoring() method
   - Add more granular error handling
   - Test middleware in isolation

2. **Fix OptimizedQueries Trait**
   - Resolve property conflicts
   - Implement proper trait composition
   - Re-enable for User model

3. **Performance Monitoring**
   - Implement alternative monitoring approach
   - Consider using Laravel Telescope for development
   - Add configuration flags for production

## Emergency Rollback

If needed, revert to basic Laravel setup:
```bash
git checkout HEAD~10 -- bootstrap/app.php
git checkout HEAD~10 -- app/Models/User.php
php artisan optimize:clear
```

## Contact Information

For immediate assistance:
- Check Laravel logs: `storage/logs/laravel.log`
- Enable debug mode: Set `APP_DEBUG=true` in `.env`
- Monitor server logs during requests
