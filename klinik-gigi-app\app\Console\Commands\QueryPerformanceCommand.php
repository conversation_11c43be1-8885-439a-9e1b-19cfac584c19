<?php

namespace App\Console\Commands;

use App\Services\QueryPerformanceService;
use Illuminate\Console\Command;

class QueryPerformanceCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'query:performance {action : Action to perform (report|slow|clear|suggestions)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor and analyze query performance';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'report':
                return $this->showReport();
            case 'slow':
                return $this->showSlowQueries();
            case 'clear':
                return $this->clearStats();
            case 'suggestions':
                return $this->showSuggestions();
            default:
                $this->error("Invalid action: {$action}");
                $this->info('Available actions: report, slow, clear, suggestions');
                return 1;
        }
    }

    /**
     * Show performance report
     */
    protected function showReport(): int
    {
        $report = QueryPerformanceService::generateReport();
        
        $this->info('Query Performance Report');
        $this->line('Generated at: ' . $report['generated_at']);
        
        // Summary
        $this->line("\n<comment>Summary:</comment>");
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Queries', $report['summary']['total_queries']],
                ['Average Query Time', $report['summary']['avg_query_time']],
                ['Slow Queries Count', $report['summary']['slow_queries_count']],
                ['Slow Query Percentage', $report['summary']['slow_query_percentage']],
            ]
        );
        
        // Query types
        if (!empty($report['query_types'])) {
            $this->line("\n<comment>Query Types:</comment>");
            $typeData = [];
            foreach ($report['query_types'] as $type => $count) {
                $typeData[] = [$type, $count];
            }
            $this->table(['Type', 'Count'], $typeData);
        }
        
        // Recommendations
        $this->line("\n<comment>Recommendations:</comment>");
        $this->line("- Optimization suggestions available: {$report['optimization_suggestions']}");
        $this->line("- Index recommendations available: {$report['index_recommendations']}");
        
        if ($report['optimization_suggestions'] > 0) {
            $this->line("\nRun 'php artisan query:performance suggestions' for detailed suggestions");
        }
        
        return 0;
    }

    /**
     * Show slow queries
     */
    protected function showSlowQueries(): int
    {
        $slowQueries = QueryPerformanceService::getSlowQueries();
        
        if (empty($slowQueries)) {
            $this->info('No slow queries recorded.');
            return 0;
        }
        
        $this->info('Slow Queries (>' . QueryPerformanceService::SLOW_QUERY_THRESHOLD . 'ms):');
        
        $tableData = [];
        foreach (array_slice($slowQueries, 0, 20) as $hash => $query) {
            $tableData[] = [
                substr($hash, 0, 8),
                number_format($query['time'], 2) . 'ms',
                $query['count'],
                number_format($query['avg_time'], 2) . 'ms',
                substr($query['sql'], 0, 80) . (strlen($query['sql']) > 80 ? '...' : ''),
            ];
        }
        
        $this->table(
            ['Hash', 'Time', 'Count', 'Avg Time', 'SQL'],
            $tableData
        );
        
        return 0;
    }

    /**
     * Clear performance statistics
     */
    protected function clearStats(): int
    {
        if ($this->confirm('Are you sure you want to clear all query performance statistics?')) {
            QueryPerformanceService::clearStats();
            $this->info('Query performance statistics cleared.');
        } else {
            $this->info('Operation cancelled.');
        }
        
        return 0;
    }

    /**
     * Show optimization suggestions
     */
    protected function showSuggestions(): int
    {
        $suggestions = QueryPerformanceService::analyzeQueries();
        $indexRecommendations = QueryPerformanceService::getIndexRecommendations();
        
        // Query optimization suggestions
        if (!empty($suggestions)) {
            $this->info('Query Optimization Suggestions:');
            
            foreach ($suggestions as $hash => $suggestion) {
                $this->line("\n<comment>Query Hash: " . substr($hash, 0, 8) . "</comment>");
                $this->line("Time: {$suggestion['time']}ms | Count: {$suggestion['count']}");
                $this->line("SQL: " . substr($suggestion['sql'], 0, 100) . '...');
                
                $this->line("<info>Suggestions:</info>");
                foreach ($suggestion['suggestions'] as $s) {
                    $this->line("  - {$s}");
                }
            }
        } else {
            $this->info('No query optimization suggestions available.');
        }
        
        // Index recommendations
        if (!empty($indexRecommendations)) {
            $this->line("\n<comment>Index Recommendations:</comment>");
            
            $tableData = [];
            foreach ($indexRecommendations as $key => $recommendation) {
                $tableData[] = [
                    $recommendation['table'],
                    $recommendation['column'],
                    $recommendation['reason'],
                    count($recommendation['queries'])
                ];
            }
            
            $this->table(
                ['Table', 'Column', 'Reason', 'Query Count'],
                $tableData
            );
        } else {
            $this->line("\nNo index recommendations available.");
        }
        
        return 0;
    }
}
