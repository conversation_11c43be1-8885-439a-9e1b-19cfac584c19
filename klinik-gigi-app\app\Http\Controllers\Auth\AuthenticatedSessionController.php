<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\AuditLog;
use App\Services\SessionSecurityService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Inertia\Response;

class AuthenticatedSessionController extends Controller
{
    /**
     * Show the login page.
     */
    public function create(Request $request): Response
    {
        return Inertia::render('auth/login', [
            'canResetPassword' => Route::has('password.request'),
            'status' => $request->session()->get('status'),
        ]);
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        $request->authenticate();

        $request->session()->regenerate();

        // Update last login timestamp and log successful login
        if (Auth::user()) {
            $user = Auth::user();
            $user->updateLastLogin();

            // Initialize session activity FIRST
            SessionSecurityService::updateSessionActivity();

            // Check concurrent session limit
            if (!SessionSecurityService::checkConcurrentSessions($user)) {
                // Terminate oldest session if limit exceeded
                $activeSessions = SessionSecurityService::getActiveSessions($user);
                if (count($activeSessions) >= SessionSecurityService::CONCURRENT_SESSION_LIMIT) {
                    $oldestSession = collect($activeSessions)->sortBy('last_activity')->first();
                    if ($oldestSession && !$oldestSession['is_current']) {
                        SessionSecurityService::terminateSpecificSession($oldestSession['id'], $user);
                    }
                }
            }

            // Store session security metadata
            SessionSecurityService::storeSessionMetadata($user);

            // Log successful login
            AuditLog::logAuth(AuditLog::ACTION_LOGIN, $user, [
                'login_method' => 'web',
                'remember' => $request->boolean('remember'),
                'concurrent_sessions' => SessionSecurityService::getActiveSessionsCount($user)
            ]);
        }

        return redirect()->intended(route('dashboard', absolute: false));
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $user = Auth::user();

        // Log logout before destroying session
        if ($user) {
            AuditLog::logAuth(AuditLog::ACTION_LOGOUT, $user);
        }

        Auth::guard('web')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/');
    }
}
