<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PasswordHistory extends Model
{
    protected $table = 'password_histories';

    protected $fillable = [
        'user_id',
        'password_hash',
        'created_at'
    ];

    protected $casts = [
        'created_at' => 'datetime'
    ];

    public $timestamps = false;

    /**
     * Relationship to User
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Clean up old password histories beyond the limit
     */
    public static function cleanupOldPasswords(int $userId, int $keepCount = 5): void
    {
        $oldPasswords = self::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->skip($keepCount)
            ->pluck('id');

        if ($oldPasswords->isNotEmpty()) {
            self::whereIn('id', $oldPasswords)->delete();
        }
    }
}
