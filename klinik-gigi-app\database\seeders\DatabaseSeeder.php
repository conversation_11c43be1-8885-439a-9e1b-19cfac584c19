<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Run role and permission seeder first
        $this->call([
            RolePermissionSeeder::class,
        ]);

        // User::factory(10)->create();

        // Create default admin user
        User::factory()->create([
            'username' => 'admin',
            'nama_depan' => 'Super',
            'nama_belakang' => 'Admin',
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'id_peran' => 1, // Super Admin role
            'aktif' => true,
        ]);
    }
}
