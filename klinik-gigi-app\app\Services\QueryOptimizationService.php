<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class QueryOptimizationService
{
    const N_PLUS_ONE_THRESHOLD = 5; // Minimum queries to consider N+1
    const CACHE_KEY_PREFIX = 'query_optimization:';
    const CACHE_TTL = 3600; // 1 hour

    protected static array $queryLog = [];
    protected static bool $monitoring = false;

    /**
     * Start monitoring for N+1 queries
     */
    public static function startN1Monitoring(): void
    {
        if (self::$monitoring) {
            return;
        }

        self::$monitoring = true;
        self::$queryLog = [];

        DB::listen(function ($query) {
            self::logQuery($query);
        });
    }

    /**
     * Stop monitoring and analyze results
     */
    public static function stopN1Monitoring(): array
    {
        self::$monitoring = false;
        return self::analyzeN1Problems();
    }

    /**
     * Log query for N+1 analysis
     */
    protected static function logQuery($query): void
    {
        if (!self::$monitoring) {
            return;
        }

        $sql = $query->sql;
        $bindings = $query->bindings;
        $time = $query->time;

        // Normalize query for pattern matching
        $normalizedSql = self::normalizeQuery($sql);

        if (!isset(self::$queryLog[$normalizedSql])) {
            self::$queryLog[$normalizedSql] = [
                'sql' => $sql,
                'normalized' => $normalizedSql,
                'count' => 0,
                'total_time' => 0,
                'bindings_samples' => [],
                'first_seen' => microtime(true),
                'last_seen' => microtime(true)
            ];
        }

        self::$queryLog[$normalizedSql]['count']++;
        self::$queryLog[$normalizedSql]['total_time'] += $time;
        self::$queryLog[$normalizedSql]['last_seen'] = microtime(true);

        // Store sample bindings (max 5)
        if (count(self::$queryLog[$normalizedSql]['bindings_samples']) < 5) {
            self::$queryLog[$normalizedSql]['bindings_samples'][] = $bindings;
        }
    }

    /**
     * Normalize query for pattern matching
     */
    protected static function normalizeQuery(string $sql): string
    {
        // Replace parameter placeholders with generic placeholder
        $normalized = preg_replace('/\?/', '?', $sql);

        // Remove extra whitespace
        $normalized = preg_replace('/\s+/', ' ', $normalized);

        // Convert to lowercase for consistency
        return strtolower(trim($normalized));
    }

    /**
     * Analyze logged queries for N+1 problems
     */
    protected static function analyzeN1Problems(): array
    {
        $problems = [];

        foreach (self::$queryLog as $normalizedSql => $queryData) {
            if ($queryData['count'] >= self::N_PLUS_ONE_THRESHOLD) {
                // Check if this looks like an N+1 pattern
                if (self::isLikelyN1Pattern($queryData)) {
                    $problems[] = [
                        'type' => 'n_plus_one',
                        'sql' => $queryData['sql'],
                        'normalized' => $normalizedSql,
                        'count' => $queryData['count'],
                        'total_time' => $queryData['total_time'],
                        'avg_time' => $queryData['total_time'] / $queryData['count'],
                        'duration' => $queryData['last_seen'] - $queryData['first_seen'],
                        'bindings_samples' => $queryData['bindings_samples'],
                        'severity' => self::calculateSeverity($queryData),
                        'suggestions' => self::generateN1Suggestions($queryData)
                    ];
                }
            }
        }

        // Sort by severity
        usort($problems, function ($a, $b) {
            return $b['severity'] <=> $a['severity'];
        });

        return $problems;
    }

    /**
     * Check if query pattern looks like N+1
     */
    protected static function isLikelyN1Pattern(array $queryData): bool
    {
        $sql = strtolower($queryData['sql']);

        // Look for SELECT queries with WHERE clauses that use single parameters
        if (strpos($sql, 'select') === 0 && strpos($sql, 'where') !== false) {
            // Check if executed many times in short duration
            $duration = $queryData['last_seen'] - $queryData['first_seen'];
            $frequency = $queryData['count'] / max($duration, 0.1);

            // High frequency execution suggests N+1
            return $frequency > 2; // More than 2 queries per second
        }

        return false;
    }

    /**
     * Calculate severity score for N+1 problem
     */
    protected static function calculateSeverity(array $queryData): int
    {
        $score = 0;

        // Count factor (more queries = higher severity)
        $score += min($queryData['count'] * 2, 50);

        // Time factor (slower queries = higher severity)
        $avgTime = $queryData['total_time'] / $queryData['count'];
        $score += min($avgTime / 10, 30);

        // Frequency factor
        $duration = max($queryData['last_seen'] - $queryData['first_seen'], 0.1);
        $frequency = $queryData['count'] / $duration;
        $score += min($frequency * 5, 20);

        return min($score, 100);
    }

    /**
     * Generate suggestions for N+1 problems
     */
    protected static function generateN1Suggestions(array $queryData): array
    {
        $suggestions = [];
        $sql = strtolower($queryData['sql']);

        // Detect table and suggest eager loading
        if (preg_match('/from\s+`?(\w+)`?/i', $sql, $matches)) {
            $table = $matches[1];
            $suggestions[] = "Consider using eager loading with 'with()' method for {$table} relationships";
        }

        // Suggest batch loading
        $suggestions[] = "Use whereIn() to batch load related records instead of individual queries";

        // Suggest caching if appropriate
        if ($queryData['count'] > 20) {
            $suggestions[] = "Consider caching results if data doesn't change frequently";
        }

        // Suggest query optimization
        $suggestions[] = "Review if all selected columns are necessary";

        return $suggestions;
    }

    /**
     * Cache query results with automatic invalidation
     */
    public static function cacheQuery(string $key, callable $callback, ?int $ttl = null): mixed
    {
        $ttl = $ttl ?? self::CACHE_TTL;
        $cacheKey = self::CACHE_KEY_PREFIX . $key;

        return Cache::remember($cacheKey, $ttl, $callback);
    }

    /**
     * Invalidate cached query results
     */
    public static function invalidateCache(string $pattern): void
    {
        // For non-Redis cache drivers, we'll use a more generic approach
        // This is less efficient but works with all cache drivers
        Cache::flush(); // Simplified approach - clears all cache
    }

    /**
     * Get query optimization recommendations
     */
    public static function getOptimizationRecommendations(): array
    {
        return [
            'eager_loading' => [
                'title' => 'Eager Loading Relationships',
                'description' => 'Load related models in advance to avoid N+1 queries',
                'examples' => [
                    'User::with("role")->get()' => 'Load users with their roles',
                    'Appointment::with("patient", "doctor")->get()' => 'Load appointments with related data',
                    'Patient::with("appointments.doctor")->get()' => 'Load nested relationships'
                ]
            ],
            'batch_loading' => [
                'title' => 'Batch Loading',
                'description' => 'Load multiple records at once using whereIn',
                'examples' => [
                    'User::whereIn("id", $userIds)->get()' => 'Load multiple users by IDs',
                    'Permission::whereIn("id_peran", $roleIds)->get()' => 'Load permissions for multiple roles'
                ]
            ],
            'query_caching' => [
                'title' => 'Query Result Caching',
                'description' => 'Cache expensive query results',
                'examples' => [
                    'Cache::remember("users.active", 3600, fn() => User::where("aktif", true)->get())' => 'Cache active users',
                    'QueryOptimizationService::cacheQuery("roles.all", fn() => Role::all())' => 'Cache all roles'
                ]
            ],
            'selective_loading' => [
                'title' => 'Selective Column Loading',
                'description' => 'Load only necessary columns',
                'examples' => [
                    'User::select("id", "username", "email")->get()' => 'Load specific columns only',
                    'Patient::select("id_pasien", "nama_depan", "nama_belakang")->get()' => 'Load patient names only'
                ]
            ]
        ];
    }

    /**
     * Generate optimization report
     */
    public static function generateOptimizationReport(): array
    {
        $problems = self::stopN1Monitoring();
        $recommendations = self::getOptimizationRecommendations();

        return [
            'summary' => [
                'n_plus_one_problems' => count($problems),
                'high_severity_problems' => count(array_filter($problems, fn($p) => $p['severity'] > 70)),
                'total_wasted_queries' => array_sum(array_column($problems, 'count')),
                'total_wasted_time' => array_sum(array_column($problems, 'total_time'))
            ],
            'problems' => $problems,
            'recommendations' => $recommendations,
            'generated_at' => now()->toDateTimeString()
        ];
    }

    /**
     * Clear optimization cache
     */
    public static function clearOptimizationCache(): void
    {
        // For non-Redis cache drivers, we'll clear all cache
        Cache::flush();
    }
}
