<?php

namespace App\Http\Middleware;

use App\Services\QueryPerformanceService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class QueryPerformanceMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Start query monitoring for this request with error handling
        try {
            if (config('app.debug') || config('database.monitor_queries', false)) {
                QueryPerformanceService::startMonitoring();
            }
        } catch (\Exception $e) {
            // Log error but don't break the request
            Log::warning('QueryPerformanceMiddleware error: ' . $e->getMessage());
        }

        return $next($request);
    }
}
