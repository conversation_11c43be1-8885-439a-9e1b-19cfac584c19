import{u as h,j as e,H as g}from"./app-EmUGGW4y.js";import{L as t,I as n,a as l}from"./label-BIgw4gz0.js";import{T as d}from"./text-link-Z88TWqmf.js";import{B as j}from"./button-B8QorGO4.js";import{C as f}from"./checkbox-DE7EQxxq.js";import{A as b,L as w}from"./auth-layout-CTfIlSls.js";/* empty css            */import"./index-CrVQA8Zu.js";import"./index-sju6-yWZ.js";import"./app-logo-icon-BW5sZeJe.js";function B({status:o,canResetPassword:u}){const{data:a,setData:r,post:c,processing:m,errors:i,reset:x}=h({login:"",password:"",remember:!1}),p=s=>{s.preventDefault(),c(route("login"),{onFinish:()=>x("password")})};return e.jsxs(b,{title:"Masuk ke Akun Anda",description:"Masukkan username/email dan password untuk masuk",children:[e.jsx(g,{title:"Login"}),e.jsxs("form",{className:"flex flex-col gap-6",onSubmit:p,children:[e.jsxs("div",{className:"grid gap-6",children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(t,{htmlFor:"login",children:"Username atau Email"}),e.jsx(n,{id:"login",type:"text",required:!0,autoFocus:!0,tabIndex:1,autoComplete:"username",value:a.login,onChange:s=>r("login",s.target.value),placeholder:"<NAME_EMAIL>"}),e.jsx(l,{message:i.login})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(t,{htmlFor:"password",children:"Password"}),u&&e.jsx(d,{href:route("password.request"),className:"ml-auto text-sm",tabIndex:5,children:"Lupa password?"})]}),e.jsx(n,{id:"password",type:"password",required:!0,tabIndex:2,autoComplete:"current-password",value:a.password,onChange:s=>r("password",s.target.value),placeholder:"Password"}),e.jsx(l,{message:i.password})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(f,{id:"remember",name:"remember",checked:a.remember,onClick:()=>r("remember",!a.remember),tabIndex:3}),e.jsx(t,{htmlFor:"remember",children:"Ingat saya"})]}),e.jsxs(j,{type:"submit",className:"mt-4 w-full",tabIndex:4,disabled:m,children:[m&&e.jsx(w,{className:"h-4 w-4 animate-spin"}),"Masuk"]})]}),e.jsxs("div",{className:"text-center text-sm text-muted-foreground",children:["Belum punya akun?"," ",e.jsx(d,{href:route("register"),tabIndex:5,children:"Daftar"})]})]}),o&&e.jsx("div",{className:"mb-4 text-center text-sm font-medium text-green-600",children:o})]})}export{B as default};
