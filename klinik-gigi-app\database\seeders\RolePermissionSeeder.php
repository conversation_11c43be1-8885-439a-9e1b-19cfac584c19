<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\Permission;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define modules in the system
        $modules = [
            'dashboard',
            'pasien',
            'janji_temu',
            'rekam_medis',
            'tagihan',
            'pembayaran',
            'inventori',
            'laporan',
            'pengguna',
            'pengaturan'
        ];

        // Create roles
        $roles = [
            [
                'nama_peran' => 'Super Admin',
                'deskripsi' => 'Administrator sistem dengan akses penuh',
                'permissions' => $this->getAllPermissions($modules)
            ],
            [
                'nama_peran' => 'Admin',
                'deskripsi' => 'Administrator klinik dengan akses manajemen',
                'permissions' => $this->getAdminPermissions($modules)
            ],
            [
                'nama_peran' => 'Dokter',
                'deskripsi' => 'Dokter gigi dengan akses klinis',
                'permissions' => $this->getDoctorPermissions($modules)
            ],
            [
                'nama_peran' => 'Perawat',
                'deskripsi' => 'Perawat dengan akses terbatas',
                'permissions' => $this->getNursePermissions($modules)
            ],
            [
                'nama_peran' => 'Resepsionis',
                'deskripsi' => 'Staff front office',
                'permissions' => $this->getReceptionistPermissions($modules)
            ]
        ];

        foreach ($roles as $roleData) {
            $role = Role::create([
                'nama_peran' => $roleData['nama_peran'],
                'deskripsi' => $roleData['deskripsi']
            ]);

            // Create permissions for this role
            foreach ($roleData['permissions'] as $module => $actions) {
                Permission::create([
                    'id_peran' => $role->id_peran,
                    'modul' => $module,
                    'baca' => $actions['read'] ?? false,
                    'tulis' => $actions['create'] ?? false,
                    'ubah' => $actions['update'] ?? false,
                    'hapus' => $actions['delete'] ?? false
                ]);
            }
        }
    }

    private function getAllPermissions(array $modules): array
    {
        $permissions = [];
        foreach ($modules as $module) {
            $permissions[$module] = [
                'read' => true,
                'create' => true,
                'update' => true,
                'delete' => true
            ];
        }
        return $permissions;
    }

    private function getAdminPermissions(array $modules): array
    {
        $permissions = [];
        foreach ($modules as $module) {
            if ($module === 'pengaturan') {
                $permissions[$module] = ['read' => true, 'create' => false, 'update' => true, 'delete' => false];
            } else {
                $permissions[$module] = [
                    'read' => true,
                    'create' => true,
                    'update' => true,
                    'delete' => true
                ];
            }
        }
        return $permissions;
    }

    private function getDoctorPermissions(array $modules): array
    {
        return [
            'dashboard' => ['read' => true, 'create' => false, 'update' => false, 'delete' => false],
            'pasien' => ['read' => true, 'create' => true, 'update' => true, 'delete' => false],
            'janji_temu' => ['read' => true, 'create' => true, 'update' => true, 'delete' => false],
            'rekam_medis' => ['read' => true, 'create' => true, 'update' => true, 'delete' => false],
            'tagihan' => ['read' => true, 'create' => true, 'update' => false, 'delete' => false],
            'pembayaran' => ['read' => true, 'create' => false, 'update' => false, 'delete' => false],
            'inventori' => ['read' => true, 'create' => false, 'update' => false, 'delete' => false],
            'laporan' => ['read' => true, 'create' => false, 'update' => false, 'delete' => false],
            'pengguna' => ['read' => false, 'create' => false, 'update' => false, 'delete' => false],
            'pengaturan' => ['read' => false, 'create' => false, 'update' => false, 'delete' => false]
        ];
    }

    private function getNursePermissions(array $modules): array
    {
        return [
            'dashboard' => ['read' => true, 'create' => false, 'update' => false, 'delete' => false],
            'pasien' => ['read' => true, 'create' => true, 'update' => true, 'delete' => false],
            'janji_temu' => ['read' => true, 'create' => true, 'update' => true, 'delete' => false],
            'rekam_medis' => ['read' => true, 'create' => false, 'update' => false, 'delete' => false],
            'tagihan' => ['read' => true, 'create' => false, 'update' => false, 'delete' => false],
            'pembayaran' => ['read' => true, 'create' => false, 'update' => false, 'delete' => false],
            'inventori' => ['read' => true, 'create' => false, 'update' => true, 'delete' => false],
            'laporan' => ['read' => false, 'create' => false, 'update' => false, 'delete' => false],
            'pengguna' => ['read' => false, 'create' => false, 'update' => false, 'delete' => false],
            'pengaturan' => ['read' => false, 'create' => false, 'update' => false, 'delete' => false]
        ];
    }

    private function getReceptionistPermissions(array $modules): array
    {
        return [
            'dashboard' => ['read' => true, 'create' => false, 'update' => false, 'delete' => false],
            'pasien' => ['read' => true, 'create' => true, 'update' => true, 'delete' => false],
            'janji_temu' => ['read' => true, 'create' => true, 'update' => true, 'delete' => false],
            'rekam_medis' => ['read' => false, 'create' => false, 'update' => false, 'delete' => false],
            'tagihan' => ['read' => true, 'create' => true, 'update' => true, 'delete' => false],
            'pembayaran' => ['read' => true, 'create' => true, 'update' => true, 'delete' => false],
            'inventori' => ['read' => true, 'create' => false, 'update' => false, 'delete' => false],
            'laporan' => ['read' => true, 'create' => false, 'update' => false, 'delete' => false],
            'pengguna' => ['read' => false, 'create' => false, 'update' => false, 'delete' => false],
            'pengaturan' => ['read' => false, 'create' => false, 'update' => false, 'delete' => false]
        ];
    }
}
