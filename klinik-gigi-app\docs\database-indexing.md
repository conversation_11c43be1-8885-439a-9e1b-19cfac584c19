# Database Indexing Optimization

## Deskripsi

Sistem optimasi database indexing untuk meningkatkan performa query dengan menganalisis pola query dan memberikan rekomendasi index yang tepat.

## Komponen Utama

### 1. Database Index Analyzer

Command untuk menganalisis index yang ada dan memberikan rekomendasi:

```bash
# Analisis semua index
php artisan db:analyze-indexes analyze

# Cari index yang hilang
php artisan db:analyze-indexes missing

# Cari index yang tidak terpakai
php artisan db:analyze-indexes unused

# Statistik index
php artisan db:analyze-indexes stats
```

### 2. Query Performance Service

Service untuk monitoring performa query:

```php
use App\Services\QueryPerformanceService;

// Start monitoring
QueryPerformanceService::startMonitoring();

// Get slow queries
$slowQueries = QueryPerformanceService::getSlowQueries();

// Get statistics
$stats = QueryPerformanceService::getQueryStats();

// Generate report
$report = QueryPerformanceService::generateReport();
```

### 3. Query Performance Commands

```bash
# Lihat laporan performa
php artisan query:performance report

# Lihat slow queries
php artisan query:performance slow

# Lihat saran optimasi
php artisan query:performance suggestions

# Clear statistik
php artisan query:performance clear
```

## Index Strategy

### 1. Primary Indexes

Setiap tabel memiliki primary key yang otomatis ter-index:

```sql
-- Users table
PRIMARY KEY (id)

-- Patients table  
PRIMARY KEY (id_pasien)

-- Appointments table
PRIMARY KEY (id_janji_temu)
```

### 2. Foreign Key Indexes

Semua foreign key memiliki index untuk JOIN performance:

```sql
-- Users table
INDEX idx_users_role (id_peran)

-- Appointments table
INDEX idx_appointments_patient (id_pasien)
INDEX idx_appointments_doctor (id_dokter)
INDEX idx_appointments_room (id_ruang_perawatan)
```

### 3. Search Indexes

Index untuk kolom yang sering dicari:

```sql
-- Users table
INDEX idx_users_username (username)
INDEX idx_users_email (email)
INDEX idx_users_fullname (nama_depan, nama_belakang)

-- Patients table
INDEX idx_patients_nik (nik)
INDEX idx_patients_phone (no_telepon)
INDEX idx_patients_fullname (nama_depan, nama_belakang)
```

### 4. Filter Indexes

Index untuk kolom yang sering digunakan dalam WHERE clause:

```sql
-- Users table
INDEX idx_users_active (aktif)
INDEX idx_users_expired_str (expired_str)
INDEX idx_users_locked_until (locked_until)

-- Patients table
INDEX idx_patients_active (aktif)
INDEX idx_patients_gender (jenis_kelamin)
INDEX idx_patients_bpjs_status (status_kepesertaan_bpjs)

-- Appointments table
INDEX idx_appointments_status (status)
INDEX idx_appointments_type (jenis_janji)
```

### 5. Composite Indexes

Index gabungan untuk query kompleks:

```sql
-- Users table
INDEX idx_users_email_active (email, aktif)
INDEX idx_users_role_active (id_peran, aktif)
INDEX idx_users_active_last_login (aktif, login_terakhir)

-- Appointments table
INDEX idx_appointments_doctor_date_status (id_dokter, tanggal_janji, status)
INDEX idx_appointments_patient_date (id_pasien, tanggal_janji)
INDEX idx_appointments_date_status (tanggal_janji, status)

-- Audit logs
INDEX idx_audit_user_date (user_id, created_at)
INDEX idx_audit_action_date (action, created_at)
INDEX idx_audit_model (model_type, model_id)
```

### 6. Date/Time Indexes

Index untuk kolom tanggal yang sering digunakan untuk filtering:

```sql
-- Appointments table
INDEX idx_appointments_date (tanggal_janji)

-- Medical records
INDEX idx_medical_records_visit_date (tanggal_kunjungan)

-- Audit logs
INDEX idx_audit_created (created_at)
```

## Query Optimization Patterns

### 1. Efficient WHERE Clauses

```sql
-- Good: Uses index
SELECT * FROM users WHERE aktif = 1 AND id_peran = 2;

-- Bad: Function on column prevents index usage
SELECT * FROM users WHERE UPPER(username) = 'ADMIN';

-- Good: Use index-friendly patterns
SELECT * FROM users WHERE username = 'admin';
```

### 2. JOIN Optimization

```sql
-- Good: Uses foreign key indexes
SELECT u.*, r.nama_peran 
FROM users u 
JOIN roles r ON u.id_peran = r.id_peran 
WHERE u.aktif = 1;

-- Bad: No index on join condition
SELECT u.*, p.* 
FROM users u 
JOIN patients p ON u.email = p.email;
```

### 3. ORDER BY Optimization

```sql
-- Good: Uses composite index
SELECT * FROM appointments 
WHERE id_dokter = 1 
ORDER BY tanggal_janji DESC;

-- Index: (id_dokter, tanggal_janji)
```

### 4. LIMIT with ORDER BY

```sql
-- Good: Efficient pagination
SELECT * FROM patients 
WHERE aktif = 1 
ORDER BY created_at DESC 
LIMIT 20 OFFSET 0;

-- Index: (aktif, created_at)
```

## Performance Monitoring

### 1. Slow Query Detection

Sistem otomatis mendeteksi query yang lambat (>100ms):

```php
// Konfigurasi di config/database.php
'monitor_queries' => env('DB_MONITOR_QUERIES', true),
'slow_query_threshold' => env('DB_SLOW_QUERY_THRESHOLD', 100),
```

### 2. Query Analysis

```bash
# Lihat slow queries
php artisan query:performance slow

# Output:
# Hash     | Time    | Count | Avg Time | SQL
# a1b2c3d4 | 250.5ms | 15    | 180.2ms  | SELECT * FROM appointments WHERE...
```

### 3. Index Recommendations

Sistem memberikan rekomendasi index berdasarkan pola query:

```bash
php artisan query:performance suggestions

# Output:
# Table: appointments
# Column: id_dokter, tanggal_janji
# Reason: Frequently used in WHERE and ORDER BY
# Query Count: 25
```

## Index Maintenance

### 1. Regular Analysis

Jalankan analisis index secara berkala:

```bash
# Cron job mingguan
0 2 * * 0 cd /path/to/app && php artisan db:analyze-indexes analyze
```

### 2. Index Statistics

Monitor statistik index:

```bash
php artisan db:analyze-indexes stats

# Output:
# Total Tables: 15
# Total Indexes: 45
# Average Indexes per Table: 3.0
```

### 3. Unused Index Detection

Identifikasi index yang tidak terpakai:

```bash
php artisan db:analyze-indexes unused
```

## Best Practices

### 1. Index Design

- **Selectivity**: Index kolom dengan nilai unik tinggi
- **Cardinality**: Hindari index pada kolom dengan nilai terbatas (boolean)
- **Composite Order**: Kolom paling selektif di depan
- **Length**: Batasi panjang index untuk VARCHAR

### 2. Query Design

- **WHERE First**: Gunakan WHERE sebelum JOIN
- **Limit Results**: Selalu gunakan LIMIT untuk pagination
- **Avoid Functions**: Jangan gunakan function pada kolom dalam WHERE
- **Use EXISTS**: Gunakan EXISTS daripada IN untuk subquery

### 3. Monitoring

- **Regular Review**: Review slow queries mingguan
- **Index Usage**: Monitor penggunaan index
- **Performance Trends**: Track performa dari waktu ke waktu

### 4. Testing

- **Load Testing**: Test dengan data volume tinggi
- **Query Plans**: Analyze EXPLAIN output
- **Benchmark**: Ukur performa sebelum dan sesudah optimasi

## Common Index Patterns

### 1. Authentication Queries

```sql
-- Login query
INDEX idx_users_email_active (email, aktif)
INDEX idx_users_username_active (username, aktif)
```

### 2. Scheduling Queries

```sql
-- Doctor schedule
INDEX idx_appointments_doctor_date_status (id_dokter, tanggal_janji, status)

-- Room availability
INDEX idx_appointments_room_date_status (id_ruang_perawatan, tanggal_janji, status)
```

### 3. Patient Search

```sql
-- Name search
INDEX idx_patients_fullname (nama_depan, nama_belakang)

-- NIK lookup
INDEX idx_patients_nik (nik)

-- Phone search
INDEX idx_patients_phone (no_telepon)
```

### 4. Audit Queries

```sql
-- User activity
INDEX idx_audit_user_date (user_id, created_at)

-- Action tracking
INDEX idx_audit_action_date (action, created_at)

-- Model changes
INDEX idx_audit_model_action_date (model_type, action, created_at)
```

## Troubleshooting

### 1. Slow Queries

```bash
# Identifikasi slow queries
php artisan query:performance slow

# Analisis dan optimasi
php artisan query:performance suggestions
```

### 2. Missing Indexes

```bash
# Cari index yang hilang
php artisan db:analyze-indexes missing

# Implementasi rekomendasi
php artisan migrate
```

### 3. Index Bloat

```bash
# Monitor ukuran index
php artisan db:analyze-indexes stats

# Rebuild index jika perlu (MySQL)
OPTIMIZE TABLE table_name;
```

### 4. Lock Contention

- Monitor deadlock dan lock wait
- Optimasi transaction scope
- Gunakan appropriate isolation level
