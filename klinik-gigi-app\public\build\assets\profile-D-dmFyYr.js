import{r as g,u as f,j as e,b as v,H as y,L as b}from"./app-EmUGGW4y.js";import{L as p,I as h,a as x}from"./label-BIgw4gz0.js";import{B as d}from"./button-B8QorGO4.js";import{H as j,S as N}from"./layout-DIqrfVrS.js";import{D as w,a as D,b as C,c as k,d as S,e as P,f as F}from"./dialog-BANbMz3C.js";import{A as E}from"./app-layout-BYy77iyt.js";import{z as L}from"./transition-DMi4RfFS.js";/* empty css            */import"./index-CrVQA8Zu.js";import"./index-sju6-yWZ.js";import"./app-logo-icon-BW5sZeJe.js";function A(){const r=g.useRef(null),{data:c,setData:a,delete:t,processing:o,reset:i,errors:l,clearErrors:m}=f({password:""}),u=s=>{s.preventDefault(),t(route("profile.destroy"),{preserveScroll:!0,onSuccess:()=>n(),onError:()=>r.current?.focus(),onFinish:()=>i()})},n=()=>{m(),i()};return e.jsxs("div",{className:"space-y-6",children:[e.jsx(j,{title:"Delete account",description:"Delete your account and all of its resources"}),e.jsxs("div",{className:"space-y-4 rounded-lg border border-red-100 bg-red-50 p-4 dark:border-red-200/10 dark:bg-red-700/10",children:[e.jsxs("div",{className:"relative space-y-0.5 text-red-600 dark:text-red-100",children:[e.jsx("p",{className:"font-medium",children:"Warning"}),e.jsx("p",{className:"text-sm",children:"Please proceed with caution, this cannot be undone."})]}),e.jsxs(w,{children:[e.jsx(D,{asChild:!0,children:e.jsx(d,{variant:"destructive",children:"Delete account"})}),e.jsxs(C,{children:[e.jsx(k,{children:"Are you sure you want to delete your account?"}),e.jsx(S,{children:"Once your account is deleted, all of its resources and data will also be permanently deleted. Please enter your password to confirm you would like to permanently delete your account."}),e.jsxs("form",{className:"space-y-6",onSubmit:u,children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(p,{htmlFor:"password",className:"sr-only",children:"Password"}),e.jsx(h,{id:"password",type:"password",name:"password",ref:r,value:c.password,onChange:s=>a("password",s.target.value),placeholder:"Password",autoComplete:"current-password"}),e.jsx(x,{message:l.password})]}),e.jsxs(P,{className:"gap-2",children:[e.jsx(F,{asChild:!0,children:e.jsx(d,{variant:"secondary",onClick:n,children:"Cancel"})}),e.jsx(d,{variant:"destructive",disabled:o,asChild:!0,children:e.jsx("button",{type:"submit",children:"Delete account"})})]})]})]})]})]})]})}const H=[{title:"Profile settings",href:"/settings/profile"}];function Y({mustVerifyEmail:r,status:c}){const{auth:a}=v().props,{data:t,setData:o,patch:i,errors:l,processing:m,recentlySuccessful:u}=f({name:a.user.name,email:a.user.email}),n=s=>{s.preventDefault(),i(route("profile.update"),{preserveScroll:!0})};return e.jsxs(E,{breadcrumbs:H,children:[e.jsx(y,{title:"Profile settings"}),e.jsxs(N,{children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx(j,{title:"Profile information",description:"Update your name and email address"}),e.jsxs("form",{onSubmit:n,className:"space-y-6",children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(p,{htmlFor:"name",children:"Name"}),e.jsx(h,{id:"name",className:"mt-1 block w-full",value:t.name,onChange:s=>o("name",s.target.value),required:!0,autoComplete:"name",placeholder:"Full name"}),e.jsx(x,{className:"mt-2",message:l.name})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(p,{htmlFor:"email",children:"Email address"}),e.jsx(h,{id:"email",type:"email",className:"mt-1 block w-full",value:t.email,onChange:s=>o("email",s.target.value),required:!0,autoComplete:"username",placeholder:"Email address"}),e.jsx(x,{className:"mt-2",message:l.email})]}),r&&a.user.email_verified_at===null&&e.jsxs("div",{children:[e.jsxs("p",{className:"-mt-4 text-sm text-muted-foreground",children:["Your email address is unverified."," ",e.jsx(b,{href:route("verification.send"),method:"post",as:"button",className:"text-foreground underline decoration-neutral-300 underline-offset-4 transition-colors duration-300 ease-out hover:decoration-current! dark:decoration-neutral-500",children:"Click here to resend the verification email."})]}),c==="verification-link-sent"&&e.jsx("div",{className:"mt-2 text-sm font-medium text-green-600",children:"A new verification link has been sent to your email address."})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(d,{disabled:m,children:"Save"}),e.jsx(L,{show:u,enter:"transition ease-in-out",enterFrom:"opacity-0",leave:"transition ease-in-out",leaveTo:"opacity-0",children:e.jsx("p",{className:"text-sm text-neutral-600",children:"Saved"})})]})]})]}),e.jsx(A,{})]})]})}export{Y as default};
