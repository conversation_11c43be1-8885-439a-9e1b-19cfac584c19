<?php

namespace App\Http\Controllers;

use App\Services\SessionSecurityService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SessionController extends Controller
{
    /**
     * Check session status
     */
    public function check(): JsonResponse
    {
        if (!Auth::check()) {
            return response()->json([
                'authenticated' => false,
                'redirect' => route('login')
            ]);
        }

        $user = Auth::user();
        $sessionTimeout = SessionSecurityService::getSessionTimeout($user);
        $lastActivity = session('last_activity', time());
        $timeRemaining = max(0, ($sessionTimeout * 60) - (time() - $lastActivity));

        return response()->json([
            'authenticated' => true,
            'user' => [
                'id' => $user->getKey(),
                'name' => $user->full_name,
                'role' => $user->role?->nama_peran
            ],
            'session' => [
                'timeout_minutes' => $sessionTimeout,
                'time_remaining_seconds' => $timeRemaining,
                'should_warn' => SessionSecurityService::shouldShowSessionWarning(),
                'warning_threshold' => SessionSecurityService::getSessionWarningTime() * 60
            ]
        ]);
    }

    /**
     * Extend session
     */
    public function extend(): JsonResponse
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Not authenticated'
            ], 401);
        }

        // Validate session security before extending
        if (!SessionSecurityService::validateSessionSecurity()) {
            return response()->json([
                'success' => false,
                'message' => 'Session invalid',
                'redirect' => route('login')
            ], 401);
        }

        // Update session activity
        SessionSecurityService::updateSessionActivity();

        $user = Auth::user();
        $sessionTimeout = SessionSecurityService::getSessionTimeout($user);

        return response()->json([
            'success' => true,
            'message' => 'Session extended',
            'session' => [
                'timeout_minutes' => $sessionTimeout,
                'time_remaining_seconds' => $sessionTimeout * 60
            ]
        ]);
    }

    /**
     * Get active sessions for current user
     */
    public function activeSessions(): JsonResponse
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Not authenticated'], 401);
        }

        $user = Auth::user();
        $sessions = SessionSecurityService::getActiveSessions($user);

        return response()->json([
            'sessions' => $sessions,
            'current_session_id' => session()->getId(),
            'concurrent_limit' => SessionSecurityService::CONCURRENT_SESSION_LIMIT
        ]);
    }

    /**
     * Terminate specific session
     */
    public function terminateSession(Request $request): JsonResponse
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Not authenticated'], 401);
        }

        $request->validate([
            'session_id' => 'required|string'
        ]);

        $user = Auth::user();
        $sessionId = $request->input('session_id');

        // Prevent terminating current session via this endpoint
        if ($sessionId === session()->getId()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot terminate current session'
            ], 400);
        }

        $success = SessionSecurityService::terminateSpecificSession($sessionId, $user);

        return response()->json([
            'success' => $success,
            'message' => $success ? 'Session terminated successfully' : 'Session not found or already terminated'
        ]);
    }

    /**
     * Terminate all other sessions
     */
    public function terminateOtherSessions(): JsonResponse
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Not authenticated'], 401);
        }

        $user = Auth::user();
        $terminatedCount = SessionSecurityService::terminateOtherSessions($user);

        return response()->json([
            'success' => true,
            'message' => "Terminated {$terminatedCount} other sessions",
            'terminated_count' => $terminatedCount
        ]);
    }

    /**
     * Get session security info
     */
    public function securityInfo(): JsonResponse
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Not authenticated'], 401);
        }

        $user = Auth::user();
        $metadata = session('security_metadata', []);

        return response()->json([
            'session_timeout' => SessionSecurityService::getSessionTimeout($user),
            'concurrent_limit' => SessionSecurityService::CONCURRENT_SESSION_LIMIT,
            'current_session' => [
                'id' => session()->getId(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'login_time' => $metadata['login_time'] ?? null,
                'last_activity' => session('last_activity')
            ],
            'security_features' => [
                'session_timeout' => true,
                'concurrent_session_limit' => true,
                'ip_validation' => config('session.strict_ip_check', false),
                'user_agent_validation' => true
            ]
        ]);
    }

    /**
     * Heartbeat endpoint to keep session alive
     */
    public function heartbeat(): JsonResponse
    {
        if (!Auth::check()) {
            return response()->json(['authenticated' => false], 401);
        }

        // Light session validation without full security check
        $lastActivity = session('last_activity');
        if (!$lastActivity || SessionSecurityService::isSessionExpired()) {
            return response()->json(['authenticated' => false], 401);
        }

        // Update activity timestamp
        SessionSecurityService::updateSessionActivity();

        return response()->json([
            'authenticated' => true,
            'timestamp' => time()
        ]);
    }
}
