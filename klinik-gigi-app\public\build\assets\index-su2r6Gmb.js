import{r as o,j as e,u as Be,H as _t,c as Me}from"./app-EmUGGW4y.js";import{a as le,c as k,S as Ct,b as He,u as V,d as Nt,B as J}from"./button-B8QorGO4.js";import{C as Tt,a as kt,b as It,d as Pt,c as Et}from"./card-iDZcqAij.js";import{c as Ve,u as Rt,R as Mt,a as Ee,b as Dt,d as At,P as Ot,V as Lt,e as Bt,h as Ht,f as Vt,g as Ft,F as Ut,D as zt,C as Kt,i as Wt,A as $t}from"./app-layout-BYy77iyt.js";import{L as E,I as A,a as M}from"./label-BIgw4gz0.js";import{P as D,r as Fe}from"./index-CrVQA8Zu.js";import{u as De,c as qt,a as T,b as Q}from"./index-sju6-yWZ.js";import{u as Gt,a as Yt,C as Xt}from"./checkbox-DE7EQxxq.js";import{D as Ue,b as ze,g as Ke,c as We,d as $e,e as qe}from"./dialog-BANbMz3C.js";/* empty css            */import"./app-logo-icon-BW5sZeJe.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zt=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Ge=le("ChevronDown",Zt);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jt=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],Qt=le("ChevronUp",Jt);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const es=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],ts=le("Eye",es);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ss=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],ns=le("Plus",ss);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const as=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]],os=le("SquarePen",as);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rs=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],ls=le("Trash2",rs);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const is=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],Ae=le("TriangleAlert",is),Ye=o.forwardRef(({className:s,...n},t)=>e.jsx("div",{className:"relative w-full overflow-auto",children:e.jsx("table",{ref:t,className:k("w-full caption-bottom text-sm",s),...n})}));Ye.displayName="Table";const Xe=o.forwardRef(({className:s,...n},t)=>e.jsx("thead",{ref:t,className:k("[&_tr]:border-b",s),...n}));Xe.displayName="TableHeader";const Ze=o.forwardRef(({className:s,...n},t)=>e.jsx("tbody",{ref:t,className:k("[&_tr:last-child]:border-0",s),...n}));Ze.displayName="TableBody";const cs=o.forwardRef(({className:s,...n},t)=>e.jsx("tfoot",{ref:t,className:k("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...n}));cs.displayName="TableFooter";const Ce=o.forwardRef(({className:s,...n},t)=>e.jsx("tr",{ref:t,className:k("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...n}));Ce.displayName="TableRow";const X=o.forwardRef(({className:s,...n},t)=>e.jsx("th",{ref:t,className:k("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),...n}));X.displayName="TableHead";const Z=o.forwardRef(({className:s,...n},t)=>e.jsx("td",{ref:t,className:k("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),...n}));Z.displayName="TableCell";const ds=o.forwardRef(({className:s,...n},t)=>e.jsx("caption",{ref:t,className:k("mt-4 text-sm text-muted-foreground",s),...n}));ds.displayName="TableCaption";const ps=He("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-auto",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function Oe({className:s,variant:n,asChild:t=!1,...c}){const l=t?Ct:"span";return e.jsx(l,{"data-slot":"badge",className:k(ps({variant:n}),s),...c})}function Le(s,[n,t]){return Math.min(t,Math.max(n,s))}var us=[" ","Enter","ArrowUp","ArrowDown"],ms=[" ","Enter"],oe="Select",[xe,ge,hs]=Dt(oe),[ce,gn]=qt(oe,[hs,Ve]),ve=Ve(),[fs,ee]=ce(oe),[xs,gs]=ce(oe),Je=s=>{const{__scopeSelect:n,children:t,open:c,defaultOpen:l,onOpenChange:p,value:a,defaultValue:r,onValueChange:i,dir:h,name:m,autoComplete:g,disabled:y,required:f,form:S}=s,u=ve(n),[d,j]=o.useState(null),[v,w]=o.useState(null),[$,O]=o.useState(!1),de=Rt(h),[I,B]=De({prop:c,defaultProp:l??!1,onChange:p,caller:oe}),[q,se]=De({prop:a,defaultProp:r,onChange:i,caller:oe}),F=o.useRef(null),U=d?S||!!d.closest("form"):!0,[G,z]=o.useState(new Set),K=Array.from(G).map(P=>P.props.value).join(";");return e.jsx(Mt,{...u,children:e.jsxs(fs,{required:f,scope:n,trigger:d,onTriggerChange:j,valueNode:v,onValueNodeChange:w,valueNodeHasChildren:$,onValueNodeHasChildrenChange:O,contentId:Ee(),value:q,onValueChange:se,open:I,onOpenChange:B,dir:de,triggerPointerDownPosRef:F,disabled:y,children:[e.jsx(xe.Provider,{scope:n,children:e.jsx(xs,{scope:s.__scopeSelect,onNativeOptionAdd:o.useCallback(P=>{z(H=>new Set(H).add(P))},[]),onNativeOptionRemove:o.useCallback(P=>{z(H=>{const W=new Set(H);return W.delete(P),W})},[]),children:t})}),U?e.jsxs(St,{"aria-hidden":!0,required:f,tabIndex:-1,name:m,autoComplete:g,value:q,onChange:P=>se(P.target.value),disabled:y,form:S,children:[q===void 0?e.jsx("option",{value:""}):null,Array.from(G)]},K):null]})})};Je.displayName=oe;var Qe="SelectTrigger",et=o.forwardRef((s,n)=>{const{__scopeSelect:t,disabled:c=!1,...l}=s,p=ve(t),a=ee(Qe,t),r=a.disabled||c,i=V(n,a.onTriggerChange),h=ge(t),m=o.useRef("touch"),[g,y,f]=yt(u=>{const d=h().filter(w=>!w.disabled),j=d.find(w=>w.value===a.value),v=bt(d,u,j);v!==void 0&&a.onValueChange(v.value)}),S=u=>{r||(a.onOpenChange(!0),f()),u&&(a.triggerPointerDownPosRef.current={x:Math.round(u.pageX),y:Math.round(u.pageY)})};return e.jsx(At,{asChild:!0,...p,children:e.jsx(D.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:r,"data-disabled":r?"":void 0,"data-placeholder":jt(a.value)?"":void 0,...l,ref:i,onClick:T(l.onClick,u=>{u.currentTarget.focus(),m.current!=="mouse"&&S(u)}),onPointerDown:T(l.onPointerDown,u=>{m.current=u.pointerType;const d=u.target;d.hasPointerCapture(u.pointerId)&&d.releasePointerCapture(u.pointerId),u.button===0&&u.ctrlKey===!1&&u.pointerType==="mouse"&&(S(u),u.preventDefault())}),onKeyDown:T(l.onKeyDown,u=>{const d=g.current!=="";!(u.ctrlKey||u.altKey||u.metaKey)&&u.key.length===1&&y(u.key),!(d&&u.key===" ")&&us.includes(u.key)&&(S(),u.preventDefault())})})})});et.displayName=Qe;var tt="SelectValue",st=o.forwardRef((s,n)=>{const{__scopeSelect:t,className:c,style:l,children:p,placeholder:a="",...r}=s,i=ee(tt,t),{onValueNodeHasChildrenChange:h}=i,m=p!==void 0,g=V(n,i.onValueNodeChange);return Q(()=>{h(m)},[h,m]),e.jsx(D.span,{...r,ref:g,style:{pointerEvents:"none"},children:jt(i.value)?e.jsx(e.Fragment,{children:a}):p})});st.displayName=tt;var vs="SelectIcon",nt=o.forwardRef((s,n)=>{const{__scopeSelect:t,children:c,...l}=s;return e.jsx(D.span,{"aria-hidden":!0,...l,ref:n,children:c||"▼"})});nt.displayName=vs;var ws="SelectPortal",at=s=>e.jsx(Ot,{asChild:!0,...s});at.displayName=ws;var re="SelectContent",ot=o.forwardRef((s,n)=>{const t=ee(re,s.__scopeSelect),[c,l]=o.useState();if(Q(()=>{l(new DocumentFragment)},[]),!t.open){const p=c;return p?Fe.createPortal(e.jsx(rt,{scope:s.__scopeSelect,children:e.jsx(xe.Slot,{scope:s.__scopeSelect,children:e.jsx("div",{children:s.children})})}),p):null}return e.jsx(lt,{...s,ref:n})});ot.displayName=re;var L=10,[rt,te]=ce(re),Ss="SelectContentImpl",js=Nt("SelectContent.RemoveScroll"),lt=o.forwardRef((s,n)=>{const{__scopeSelect:t,position:c="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:p,onPointerDownOutside:a,side:r,sideOffset:i,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:f,sticky:S,hideWhenDetached:u,avoidCollisions:d,...j}=s,v=ee(re,t),[w,$]=o.useState(null),[O,de]=o.useState(null),I=V(n,x=>$(x)),[B,q]=o.useState(null),[se,F]=o.useState(null),U=ge(t),[G,z]=o.useState(!1),K=o.useRef(!1);o.useEffect(()=>{if(w)return Ht(w)},[w]),Vt();const P=o.useCallback(x=>{const[C,...R]=U().map(N=>N.ref.current),[b]=R.slice(-1),_=document.activeElement;for(const N of x)if(N===_||(N?.scrollIntoView({block:"nearest"}),N===C&&O&&(O.scrollTop=0),N===b&&O&&(O.scrollTop=O.scrollHeight),N?.focus(),document.activeElement!==_))return},[U,O]),H=o.useCallback(()=>P([B,w]),[P,B,w]);o.useEffect(()=>{G&&H()},[G,H]);const{onOpenChange:W,triggerPointerDownPosRef:Y}=v;o.useEffect(()=>{if(w){let x={x:0,y:0};const C=b=>{x={x:Math.abs(Math.round(b.pageX)-(Y.current?.x??0)),y:Math.abs(Math.round(b.pageY)-(Y.current?.y??0))}},R=b=>{x.x<=10&&x.y<=10?b.preventDefault():w.contains(b.target)||W(!1),document.removeEventListener("pointermove",C),Y.current=null};return Y.current!==null&&(document.addEventListener("pointermove",C),document.addEventListener("pointerup",R,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",C),document.removeEventListener("pointerup",R,{capture:!0})}}},[w,W,Y]),o.useEffect(()=>{const x=()=>W(!1);return window.addEventListener("blur",x),window.addEventListener("resize",x),()=>{window.removeEventListener("blur",x),window.removeEventListener("resize",x)}},[W]);const[we,me]=yt(x=>{const C=U().filter(_=>!_.disabled),R=C.find(_=>_.ref.current===document.activeElement),b=bt(C,x,R);b&&setTimeout(()=>b.ref.current.focus())}),Se=o.useCallback((x,C,R)=>{const b=!K.current&&!R;(v.value!==void 0&&v.value===C||b)&&(q(x),b&&(K.current=!0))},[v.value]),je=o.useCallback(()=>w?.focus(),[w]),ie=o.useCallback((x,C,R)=>{const b=!K.current&&!R;(v.value!==void 0&&v.value===C||b)&&F(x)},[v.value]),he=c==="popper"?Ne:it,pe=he===Ne?{side:r,sideOffset:i,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:f,sticky:S,hideWhenDetached:u,avoidCollisions:d}:{};return e.jsx(rt,{scope:t,content:w,viewport:O,onViewportChange:de,itemRefCallback:Se,selectedItem:B,onItemLeave:je,itemTextRefCallback:ie,focusSelectedItem:H,selectedItemText:se,position:c,isPositioned:G,searchRef:we,children:e.jsx(Ft,{as:js,allowPinchZoom:!0,children:e.jsx(Ut,{asChild:!0,trapped:v.open,onMountAutoFocus:x=>{x.preventDefault()},onUnmountAutoFocus:T(l,x=>{v.trigger?.focus({preventScroll:!0}),x.preventDefault()}),children:e.jsx(zt,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:p,onPointerDownOutside:a,onFocusOutside:x=>x.preventDefault(),onDismiss:()=>v.onOpenChange(!1),children:e.jsx(he,{role:"listbox",id:v.contentId,"data-state":v.open?"open":"closed",dir:v.dir,onContextMenu:x=>x.preventDefault(),...j,...pe,onPlaced:()=>z(!0),ref:I,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:T(j.onKeyDown,x=>{const C=x.ctrlKey||x.altKey||x.metaKey;if(x.key==="Tab"&&x.preventDefault(),!C&&x.key.length===1&&me(x.key),["ArrowUp","ArrowDown","Home","End"].includes(x.key)){let b=U().filter(_=>!_.disabled).map(_=>_.ref.current);if(["ArrowUp","End"].includes(x.key)&&(b=b.slice().reverse()),["ArrowUp","ArrowDown"].includes(x.key)){const _=x.target,N=b.indexOf(_);b=b.slice(N+1)}setTimeout(()=>P(b)),x.preventDefault()}})})})})})})});lt.displayName=Ss;var ys="SelectItemAlignedPosition",it=o.forwardRef((s,n)=>{const{__scopeSelect:t,onPlaced:c,...l}=s,p=ee(re,t),a=te(re,t),[r,i]=o.useState(null),[h,m]=o.useState(null),g=V(n,I=>m(I)),y=ge(t),f=o.useRef(!1),S=o.useRef(!0),{viewport:u,selectedItem:d,selectedItemText:j,focusSelectedItem:v}=a,w=o.useCallback(()=>{if(p.trigger&&p.valueNode&&r&&h&&u&&d&&j){const I=p.trigger.getBoundingClientRect(),B=h.getBoundingClientRect(),q=p.valueNode.getBoundingClientRect(),se=j.getBoundingClientRect();if(p.dir!=="rtl"){const _=se.left-B.left,N=q.left-_,ne=I.left-N,ae=I.width+ne,ye=Math.max(ae,B.width),be=window.innerWidth-L,_e=Le(N,[L,Math.max(L,be-ye)]);r.style.minWidth=ae+"px",r.style.left=_e+"px"}else{const _=B.right-se.right,N=window.innerWidth-q.right-_,ne=window.innerWidth-I.right-N,ae=I.width+ne,ye=Math.max(ae,B.width),be=window.innerWidth-L,_e=Le(N,[L,Math.max(L,be-ye)]);r.style.minWidth=ae+"px",r.style.right=_e+"px"}const F=y(),U=window.innerHeight-L*2,G=u.scrollHeight,z=window.getComputedStyle(h),K=parseInt(z.borderTopWidth,10),P=parseInt(z.paddingTop,10),H=parseInt(z.borderBottomWidth,10),W=parseInt(z.paddingBottom,10),Y=K+P+G+W+H,we=Math.min(d.offsetHeight*5,Y),me=window.getComputedStyle(u),Se=parseInt(me.paddingTop,10),je=parseInt(me.paddingBottom,10),ie=I.top+I.height/2-L,he=U-ie,pe=d.offsetHeight/2,x=d.offsetTop+pe,C=K+P+x,R=Y-C;if(C<=ie){const _=F.length>0&&d===F[F.length-1].ref.current;r.style.bottom="0px";const N=h.clientHeight-u.offsetTop-u.offsetHeight,ne=Math.max(he,pe+(_?je:0)+N+H),ae=C+ne;r.style.height=ae+"px"}else{const _=F.length>0&&d===F[0].ref.current;r.style.top="0px";const ne=Math.max(ie,K+u.offsetTop+(_?Se:0)+pe)+R;r.style.height=ne+"px",u.scrollTop=C-ie+u.offsetTop}r.style.margin=`${L}px 0`,r.style.minHeight=we+"px",r.style.maxHeight=U+"px",c?.(),requestAnimationFrame(()=>f.current=!0)}},[y,p.trigger,p.valueNode,r,h,u,d,j,p.dir,c]);Q(()=>w(),[w]);const[$,O]=o.useState();Q(()=>{h&&O(window.getComputedStyle(h).zIndex)},[h]);const de=o.useCallback(I=>{I&&S.current===!0&&(w(),v?.(),S.current=!1)},[w,v]);return e.jsx(_s,{scope:t,contentWrapper:r,shouldExpandOnScrollRef:f,onScrollButtonChange:de,children:e.jsx("div",{ref:i,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:$},children:e.jsx(D.div,{...l,ref:g,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});it.displayName=ys;var bs="SelectPopperPosition",Ne=o.forwardRef((s,n)=>{const{__scopeSelect:t,align:c="start",collisionPadding:l=L,...p}=s,a=ve(t);return e.jsx(Kt,{...a,...p,ref:n,align:c,collisionPadding:l,style:{boxSizing:"border-box",...p.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Ne.displayName=bs;var[_s,Re]=ce(re,{}),Te="SelectViewport",ct=o.forwardRef((s,n)=>{const{__scopeSelect:t,nonce:c,...l}=s,p=te(Te,t),a=Re(Te,t),r=V(n,p.onViewportChange),i=o.useRef(0);return e.jsxs(e.Fragment,{children:[e.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:c}),e.jsx(xe.Slot,{scope:t,children:e.jsx(D.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:r,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:T(l.onScroll,h=>{const m=h.currentTarget,{contentWrapper:g,shouldExpandOnScrollRef:y}=a;if(y?.current&&g){const f=Math.abs(i.current-m.scrollTop);if(f>0){const S=window.innerHeight-L*2,u=parseFloat(g.style.minHeight),d=parseFloat(g.style.height),j=Math.max(u,d);if(j<S){const v=j+f,w=Math.min(S,v),$=v-w;g.style.height=w+"px",g.style.bottom==="0px"&&(m.scrollTop=$>0?$:0,g.style.justifyContent="flex-end")}}}i.current=m.scrollTop})})})]})});ct.displayName=Te;var dt="SelectGroup",[Cs,Ns]=ce(dt),Ts=o.forwardRef((s,n)=>{const{__scopeSelect:t,...c}=s,l=Ee();return e.jsx(Cs,{scope:t,id:l,children:e.jsx(D.div,{role:"group","aria-labelledby":l,...c,ref:n})})});Ts.displayName=dt;var pt="SelectLabel",ks=o.forwardRef((s,n)=>{const{__scopeSelect:t,...c}=s,l=Ns(pt,t);return e.jsx(D.div,{id:l.id,...c,ref:n})});ks.displayName=pt;var fe="SelectItem",[Is,ut]=ce(fe),mt=o.forwardRef((s,n)=>{const{__scopeSelect:t,value:c,disabled:l=!1,textValue:p,...a}=s,r=ee(fe,t),i=te(fe,t),h=r.value===c,[m,g]=o.useState(p??""),[y,f]=o.useState(!1),S=V(n,v=>i.itemRefCallback?.(v,c,l)),u=Ee(),d=o.useRef("touch"),j=()=>{l||(r.onValueChange(c),r.onOpenChange(!1))};if(c==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return e.jsx(Is,{scope:t,value:c,disabled:l,textId:u,isSelected:h,onItemTextChange:o.useCallback(v=>{g(w=>w||(v?.textContent??"").trim())},[]),children:e.jsx(xe.ItemSlot,{scope:t,value:c,disabled:l,textValue:m,children:e.jsx(D.div,{role:"option","aria-labelledby":u,"data-highlighted":y?"":void 0,"aria-selected":h&&y,"data-state":h?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...a,ref:S,onFocus:T(a.onFocus,()=>f(!0)),onBlur:T(a.onBlur,()=>f(!1)),onClick:T(a.onClick,()=>{d.current!=="mouse"&&j()}),onPointerUp:T(a.onPointerUp,()=>{d.current==="mouse"&&j()}),onPointerDown:T(a.onPointerDown,v=>{d.current=v.pointerType}),onPointerMove:T(a.onPointerMove,v=>{d.current=v.pointerType,l?i.onItemLeave?.():d.current==="mouse"&&v.currentTarget.focus({preventScroll:!0})}),onPointerLeave:T(a.onPointerLeave,v=>{v.currentTarget===document.activeElement&&i.onItemLeave?.()}),onKeyDown:T(a.onKeyDown,v=>{i.searchRef?.current!==""&&v.key===" "||(ms.includes(v.key)&&j(),v.key===" "&&v.preventDefault())})})})})});mt.displayName=fe;var ue="SelectItemText",ht=o.forwardRef((s,n)=>{const{__scopeSelect:t,className:c,style:l,...p}=s,a=ee(ue,t),r=te(ue,t),i=ut(ue,t),h=gs(ue,t),[m,g]=o.useState(null),y=V(n,j=>g(j),i.onItemTextChange,j=>r.itemTextRefCallback?.(j,i.value,i.disabled)),f=m?.textContent,S=o.useMemo(()=>e.jsx("option",{value:i.value,disabled:i.disabled,children:f},i.value),[i.disabled,i.value,f]),{onNativeOptionAdd:u,onNativeOptionRemove:d}=h;return Q(()=>(u(S),()=>d(S)),[u,d,S]),e.jsxs(e.Fragment,{children:[e.jsx(D.span,{id:i.textId,...p,ref:y}),i.isSelected&&a.valueNode&&!a.valueNodeHasChildren?Fe.createPortal(p.children,a.valueNode):null]})});ht.displayName=ue;var ft="SelectItemIndicator",xt=o.forwardRef((s,n)=>{const{__scopeSelect:t,...c}=s;return ut(ft,t).isSelected?e.jsx(D.span,{"aria-hidden":!0,...c,ref:n}):null});xt.displayName=ft;var ke="SelectScrollUpButton",gt=o.forwardRef((s,n)=>{const t=te(ke,s.__scopeSelect),c=Re(ke,s.__scopeSelect),[l,p]=o.useState(!1),a=V(n,c.onScrollButtonChange);return Q(()=>{if(t.viewport&&t.isPositioned){let r=function(){const h=i.scrollTop>0;p(h)};const i=t.viewport;return r(),i.addEventListener("scroll",r),()=>i.removeEventListener("scroll",r)}},[t.viewport,t.isPositioned]),l?e.jsx(wt,{...s,ref:a,onAutoScroll:()=>{const{viewport:r,selectedItem:i}=t;r&&i&&(r.scrollTop=r.scrollTop-i.offsetHeight)}}):null});gt.displayName=ke;var Ie="SelectScrollDownButton",vt=o.forwardRef((s,n)=>{const t=te(Ie,s.__scopeSelect),c=Re(Ie,s.__scopeSelect),[l,p]=o.useState(!1),a=V(n,c.onScrollButtonChange);return Q(()=>{if(t.viewport&&t.isPositioned){let r=function(){const h=i.scrollHeight-i.clientHeight,m=Math.ceil(i.scrollTop)<h;p(m)};const i=t.viewport;return r(),i.addEventListener("scroll",r),()=>i.removeEventListener("scroll",r)}},[t.viewport,t.isPositioned]),l?e.jsx(wt,{...s,ref:a,onAutoScroll:()=>{const{viewport:r,selectedItem:i}=t;r&&i&&(r.scrollTop=r.scrollTop+i.offsetHeight)}}):null});vt.displayName=Ie;var wt=o.forwardRef((s,n)=>{const{__scopeSelect:t,onAutoScroll:c,...l}=s,p=te("SelectScrollButton",t),a=o.useRef(null),r=ge(t),i=o.useCallback(()=>{a.current!==null&&(window.clearInterval(a.current),a.current=null)},[]);return o.useEffect(()=>()=>i(),[i]),Q(()=>{r().find(m=>m.ref.current===document.activeElement)?.ref.current?.scrollIntoView({block:"nearest"})},[r]),e.jsx(D.div,{"aria-hidden":!0,...l,ref:n,style:{flexShrink:0,...l.style},onPointerDown:T(l.onPointerDown,()=>{a.current===null&&(a.current=window.setInterval(c,50))}),onPointerMove:T(l.onPointerMove,()=>{p.onItemLeave?.(),a.current===null&&(a.current=window.setInterval(c,50))}),onPointerLeave:T(l.onPointerLeave,()=>{i()})})}),Ps="SelectSeparator",Es=o.forwardRef((s,n)=>{const{__scopeSelect:t,...c}=s;return e.jsx(D.div,{"aria-hidden":!0,...c,ref:n})});Es.displayName=Ps;var Pe="SelectArrow",Rs=o.forwardRef((s,n)=>{const{__scopeSelect:t,...c}=s,l=ve(t),p=ee(Pe,t),a=te(Pe,t);return p.open&&a.position==="popper"?e.jsx(Wt,{...l,...c,ref:n}):null});Rs.displayName=Pe;var Ms="SelectBubbleInput",St=o.forwardRef(({__scopeSelect:s,value:n,...t},c)=>{const l=o.useRef(null),p=V(c,l),a=Gt(n);return o.useEffect(()=>{const r=l.current;if(!r)return;const i=window.HTMLSelectElement.prototype,m=Object.getOwnPropertyDescriptor(i,"value").set;if(a!==n&&m){const g=new Event("change",{bubbles:!0});m.call(r,n),r.dispatchEvent(g)}},[a,n]),e.jsx(D.select,{...t,style:{...Lt,...t.style},ref:p,defaultValue:n})});St.displayName=Ms;function jt(s){return s===""||s===void 0}function yt(s){const n=Bt(s),t=o.useRef(""),c=o.useRef(0),l=o.useCallback(a=>{const r=t.current+a;n(r),function i(h){t.current=h,window.clearTimeout(c.current),h!==""&&(c.current=window.setTimeout(()=>i(""),1e3))}(r)},[n]),p=o.useCallback(()=>{t.current="",window.clearTimeout(c.current)},[]);return o.useEffect(()=>()=>window.clearTimeout(c.current),[]),[t,l,p]}function bt(s,n,t){const l=n.length>1&&Array.from(n).every(h=>h===n[0])?n[0]:n,p=t?s.indexOf(t):-1;let a=Ds(s,Math.max(p,0));l.length===1&&(a=a.filter(h=>h!==t));const i=a.find(h=>h.textValue.toLowerCase().startsWith(l.toLowerCase()));return i!==t?i:void 0}function Ds(s,n){return s.map((t,c)=>s[(n+c)%s.length])}var As=Je,Os=et,Ls=st,Bs=nt,Hs=at,Vs=ot,Fs=ct,Us=mt,zs=ht,Ks=xt,Ws=gt,$s=vt;function qs({...s}){return e.jsx(As,{"data-slot":"select",...s})}function Gs({...s}){return e.jsx(Ls,{"data-slot":"select-value",...s})}function Ys({className:s,children:n,...t}){return e.jsxs(Os,{"data-slot":"select-trigger",className:k("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-full items-center justify-between rounded-md border bg-transparent px-3 py-2 text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 [&>span]:line-clamp-1",s),...t,children:[n,e.jsx(Bs,{asChild:!0,children:e.jsx(Ge,{className:"size-4 opacity-50"})})]})}function Xs({className:s,children:n,position:t="popper",...c}){return e.jsx(Hs,{children:e.jsxs(Vs,{"data-slot":"select-content",className:k("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md",t==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:t,...c,children:[e.jsx(Js,{}),e.jsx(Fs,{className:k("p-1",t==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:n}),e.jsx(Qs,{})]})})}function Zs({className:s,children:n,...t}){return e.jsxs(Us,{"data-slot":"select-item",className:k("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",s),...t,children:[e.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:e.jsx(Ks,{children:e.jsx(Yt,{className:"size-4"})})}),e.jsx(zs,{children:n})]})}function Js({className:s,...n}){return e.jsx(Ws,{"data-slot":"select-scroll-up-button",className:k("flex cursor-default items-center justify-center py-1",s),...n,children:e.jsx(Qt,{className:"size-4"})})}function Qs({className:s,...n}){return e.jsx($s,{"data-slot":"select-scroll-down-button",className:k("flex cursor-default items-center justify-center py-1",s),...n,children:e.jsx(Ge,{className:"size-4"})})}function en({isOpen:s,onClose:n,user:t,roles:c,onSuccess:l}){const p=!!t?.id,{data:a,setData:r,post:i,put:h,processing:m,errors:g,reset:y,clearErrors:f}=Be({username:"",nama_depan:"",nama_belakang:"",email:"",no_telepon:"",id_peran:"",nomor_str:"",nomor_sip:"",expired_str:"",expired_sip:"",aktif:!0,password:"",password_confirmation:""});o.useEffect(()=>{s&&(t?r({username:t.username||"",nama_depan:t.nama_depan||"",nama_belakang:t.nama_belakang||"",email:t.email||"",no_telepon:t.no_telepon||"",id_peran:t.id_peran?.toString()||"",nomor_str:t.nomor_str||"",nomor_sip:t.nomor_sip||"",expired_str:t.expired_str||"",expired_sip:t.expired_sip||"",aktif:t.aktif??!0,password:"",password_confirmation:""}):(y(),r("aktif",!0)),f())},[s,t]);const S=d=>{d.preventDefault(),{...a},parseInt(a.id_peran),p&&a.password;const j={preserveScroll:!0,onSuccess:()=>{n(),l?.(),y()},onError:()=>{}};p?h(route("users.update",t.id),j):i(route("users.store"),j)},u=()=>{n(),y(),f()};return e.jsx(Ue,{open:s,onOpenChange:u,children:e.jsxs(ze,{className:"sm:max-w-[600px] max-h-[90vh] overflow-y-auto",children:[e.jsxs(Ke,{children:[e.jsx(We,{children:p?"Edit Pengguna":"Tambah Pengguna Baru"}),e.jsx($e,{children:p?"Perbarui informasi pengguna. Kosongkan password jika tidak ingin mengubahnya.":"Masukkan informasi pengguna baru. Semua field yang bertanda * wajib diisi."})]}),e.jsxs("form",{onSubmit:S,className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(E,{htmlFor:"username",children:"Username *"}),e.jsx(A,{id:"username",type:"text",value:a.username,onChange:d=>r("username",d.target.value),placeholder:"Masukkan username",disabled:m,required:!0}),e.jsx(M,{message:g.username})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(E,{htmlFor:"id_peran",children:"Role *"}),e.jsxs(qs,{value:a.id_peran,onValueChange:d=>r("id_peran",d),disabled:m,children:[e.jsx(Ys,{children:e.jsx(Gs,{placeholder:"Pilih role"})}),e.jsx(Xs,{children:c.map(d=>e.jsx(Zs,{value:d.id_peran.toString(),children:d.nama_peran},d.id_peran))})]}),e.jsx(M,{message:g.id_peran})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(E,{htmlFor:"nama_depan",children:"Nama Depan *"}),e.jsx(A,{id:"nama_depan",type:"text",value:a.nama_depan,onChange:d=>r("nama_depan",d.target.value),placeholder:"Masukkan nama depan",disabled:m,required:!0}),e.jsx(M,{message:g.nama_depan})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(E,{htmlFor:"nama_belakang",children:"Nama Belakang *"}),e.jsx(A,{id:"nama_belakang",type:"text",value:a.nama_belakang,onChange:d=>r("nama_belakang",d.target.value),placeholder:"Masukkan nama belakang",disabled:m,required:!0}),e.jsx(M,{message:g.nama_belakang})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(E,{htmlFor:"email",children:"Email *"}),e.jsx(A,{id:"email",type:"email",value:a.email,onChange:d=>r("email",d.target.value),placeholder:"<EMAIL>",disabled:m,required:!0}),e.jsx(M,{message:g.email})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(E,{htmlFor:"no_telepon",children:"No. Telepon"}),e.jsx(A,{id:"no_telepon",type:"tel",value:a.no_telepon,onChange:d=>r("no_telepon",d.target.value),placeholder:"08xxxxxxxxxx",disabled:m}),e.jsx(M,{message:g.no_telepon})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(E,{htmlFor:"nomor_str",children:"Nomor STR"}),e.jsx(A,{id:"nomor_str",type:"text",value:a.nomor_str,onChange:d=>r("nomor_str",d.target.value),placeholder:"Nomor STR",disabled:m}),e.jsx(M,{message:g.nomor_str})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(E,{htmlFor:"nomor_sip",children:"Nomor SIP"}),e.jsx(A,{id:"nomor_sip",type:"text",value:a.nomor_sip,onChange:d=>r("nomor_sip",d.target.value),placeholder:"Nomor SIP",disabled:m}),e.jsx(M,{message:g.nomor_sip})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(E,{htmlFor:"expired_str",children:"Tanggal Expired STR"}),e.jsx(A,{id:"expired_str",type:"date",value:a.expired_str,onChange:d=>r("expired_str",d.target.value),disabled:m}),e.jsx(M,{message:g.expired_str})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(E,{htmlFor:"expired_sip",children:"Tanggal Expired SIP"}),e.jsx(A,{id:"expired_sip",type:"date",value:a.expired_sip,onChange:d=>r("expired_sip",d.target.value),disabled:m}),e.jsx(M,{message:g.expired_sip})]})]}),e.jsxs("div",{className:"space-y-4 border-t pt-4",children:[e.jsx("h4",{className:"font-medium",children:p?"Ubah Password (Opsional)":"Password *"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(E,{htmlFor:"password",children:p?"Password Baru":"Password *"}),e.jsx(A,{id:"password",type:"password",value:a.password,onChange:d=>r("password",d.target.value),placeholder:"Masukkan password",disabled:m,required:!p}),e.jsx(M,{message:g.password})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(E,{htmlFor:"password_confirmation",children:p?"Konfirmasi Password Baru":"Konfirmasi Password *"}),e.jsx(A,{id:"password_confirmation",type:"password",value:a.password_confirmation,onChange:d=>r("password_confirmation",d.target.value),placeholder:"Konfirmasi password",disabled:m,required:!p&&!!a.password}),e.jsx(M,{message:g.password_confirmation})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Xt,{id:"aktif",checked:a.aktif,onCheckedChange:d=>r("aktif",!!d),disabled:m}),e.jsx(E,{htmlFor:"aktif",children:"Pengguna Aktif"})]}),e.jsxs(qe,{children:[e.jsx(J,{type:"button",variant:"outline",onClick:u,disabled:m,children:"Batal"}),e.jsx(J,{type:"submit",disabled:m,children:m?"Menyimpan...":p?"Perbarui":"Simpan"})]})]})]})})}const tn=He("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-background text-foreground",destructive:"text-destructive-foreground [&>svg]:text-current *:data-[slot=alert-description]:text-destructive-foreground/80"}},defaultVariants:{variant:"default"}});function sn({className:s,variant:n,...t}){return e.jsx("div",{"data-slot":"alert",role:"alert",className:k(tn({variant:n}),s),...t})}function nn({className:s,...n}){return e.jsx("div",{"data-slot":"alert-description",className:k("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",s),...n})}function an({isOpen:s,onClose:n,user:t,onSuccess:c}){const l=o.useRef(null),{data:p,setData:a,delete:r,processing:i,errors:h,reset:m,clearErrors:g}=Be({password:""});o.useEffect(()=>{s&&(m(),g(),setTimeout(()=>{l.current?.focus()},100))},[s]);const y=S=>{S.preventDefault(),t?.id&&r(route("users.destroy",t.id),{preserveScroll:!0,data:{password:p.password},onSuccess:()=>{n(),c?.(),m()},onError:()=>{l.current?.focus()}})},f=()=>{n(),m(),g()};return t?e.jsx(Ue,{open:s,onOpenChange:f,children:e.jsxs(ze,{className:"sm:max-w-[500px]",children:[e.jsxs(Ke,{children:[e.jsxs(We,{className:"flex items-center gap-2 text-destructive",children:[e.jsx(Ae,{className:"h-5 w-5"}),"Hapus Pengguna"]}),e.jsx($e,{children:"Tindakan ini tidak dapat dibatalkan. Pengguna akan dihapus secara permanen dari sistem."})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs(sn,{children:[e.jsx(Ae,{className:"h-4 w-4"}),e.jsxs(nn,{children:[e.jsx("strong",{children:"Peringatan:"})," Anda akan menghapus pengguna berikut:"]})]}),e.jsx("div",{className:"bg-muted p-4 rounded-lg space-y-2",children:e.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Username:"}),e.jsx("p",{className:"text-muted-foreground",children:t.username})]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Email:"}),e.jsx("p",{className:"text-muted-foreground",children:t.email})]}),e.jsxs("div",{className:"col-span-2",children:[e.jsx("span",{className:"font-medium",children:"Nama Lengkap:"}),e.jsxs("p",{className:"text-muted-foreground",children:[t.nama_depan," ",t.nama_belakang]})]})]})}),e.jsxs("form",{onSubmit:y,className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(E,{htmlFor:"password",children:"Konfirmasi dengan Password Anda *"}),e.jsx(A,{ref:l,id:"password",type:"password",value:p.password,onChange:S=>a("password",S.target.value),placeholder:"Masukkan password Anda",disabled:i,required:!0,autoComplete:"current-password"}),e.jsx(M,{message:h.password}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Masukkan password akun Anda untuk mengkonfirmasi penghapusan."})]}),e.jsxs(qe,{children:[e.jsx(J,{type:"button",variant:"outline",onClick:f,disabled:i,children:"Batal"}),e.jsx(J,{type:"submit",variant:"destructive",disabled:i||!p.password.trim(),children:i?"Menghapus...":"Hapus Pengguna"})]})]})]})]})}):null}function vn({users:s,roles:n}){const[t,c]=o.useState(!1),[l,p]=o.useState(!1),[a,r]=o.useState(null),i=()=>{r(null),c(!0)},h=f=>{r(f),c(!0)},m=f=>{r(f),p(!0)},g=f=>{Me.visit(route("users.show",f.id))},y=()=>{Me.reload({only:["users"]})};return e.jsxs($t,{children:[e.jsx(_t,{title:"Manajemen Pengguna"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Manajemen Pengguna"}),e.jsx("p",{className:"text-muted-foreground",children:"Kelola pengguna sistem dan hak akses mereka"})]}),e.jsxs(J,{onClick:i,children:[e.jsx(ns,{className:"mr-2 h-4 w-4"}),"Tambah Pengguna"]})]}),e.jsxs(Tt,{children:[e.jsxs(kt,{children:[e.jsx(It,{children:"Daftar Pengguna"}),e.jsxs(Pt,{children:["Total ",s.total," pengguna terdaftar"]})]}),e.jsxs(Et,{children:[e.jsxs(Ye,{children:[e.jsx(Xe,{children:e.jsxs(Ce,{children:[e.jsx(X,{children:"Username"}),e.jsx(X,{children:"Nama Lengkap"}),e.jsx(X,{children:"Email"}),e.jsx(X,{children:"Role"}),e.jsx(X,{children:"Status"}),e.jsx(X,{children:"Login Terakhir"}),e.jsx(X,{className:"text-right",children:"Aksi"})]})}),e.jsx(Ze,{children:s.data.map(f=>e.jsxs(Ce,{children:[e.jsx(Z,{className:"font-medium",children:f.username}),e.jsxs(Z,{children:[f.nama_depan," ",f.nama_belakang]}),e.jsx(Z,{children:f.email}),e.jsx(Z,{children:e.jsx(Oe,{variant:"secondary",children:f.role?.nama_peran||"Tidak ada role"})}),e.jsx(Z,{children:e.jsx(Oe,{variant:f.aktif?"default":"destructive",children:f.aktif?"Aktif":"Tidak Aktif"})}),e.jsx(Z,{children:f.login_terakhir?new Date(f.login_terakhir).toLocaleDateString("id-ID"):"Belum pernah login"}),e.jsx(Z,{className:"text-right",children:e.jsxs("div",{className:"flex items-center justify-end gap-2",children:[e.jsx(J,{variant:"ghost",size:"sm",onClick:()=>g(f),title:"Lihat Detail",children:e.jsx(ts,{className:"h-4 w-4"})}),e.jsx(J,{variant:"ghost",size:"sm",onClick:()=>h(f),title:"Edit Pengguna",children:e.jsx(os,{className:"h-4 w-4"})}),e.jsx(J,{variant:"ghost",size:"sm",className:"text-destructive hover:text-destructive",onClick:()=>m(f),title:"Hapus Pengguna",children:e.jsx(ls,{className:"h-4 w-4"})})]})})]},f.id))})]}),s.data.length===0&&e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-muted-foreground",children:"Tidak ada pengguna ditemukan"})})]})]})]}),e.jsx(en,{isOpen:t,onClose:()=>c(!1),user:a,roles:n,onSuccess:y}),e.jsx(an,{isOpen:l,onClose:()=>p(!1),user:a,onSuccess:y})]})}export{vn as default};
