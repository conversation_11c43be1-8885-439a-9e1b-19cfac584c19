<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Menambahkan field baru sesuai ERD
            $table->string('username')->unique()->after('id');
            $table->string('nama_depan')->after('username');
            $table->string('nama_belakang')->after('nama_depan');
            $table->string('no_telepon')->nullable()->after('email');
            $table->unsignedBigInteger('id_peran')->nullable()->after('no_telepon');
            $table->string('nomor_str')->nullable()->after('id_peran');
            $table->string('nomor_sip')->nullable()->after('nomor_str');
            $table->date('expired_str')->nullable()->after('nomor_sip');
            $table->date('expired_sip')->nullable()->after('expired_str');
            $table->boolean('aktif')->default(true)->after('expired_sip');
            $table->timestamp('login_terakhir')->nullable()->after('aktif');

            // Foreign key constraint
            $table->foreign('id_peran')->references('id_peran')->on('roles')->onDelete('set null');

            // Index untuk performance
            $table->index('username');
            $table->index('aktif');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['id_peran']);

            // Drop columns
            $table->dropColumn([
                'username',
                'nama_depan',
                'nama_belakang',
                'no_telepon',
                'id_peran',
                'nomor_str',
                'nomor_sip',
                'expired_str',
                'expired_sip',
                'aktif',
                'login_terakhir'
            ]);
        });
    }
};
