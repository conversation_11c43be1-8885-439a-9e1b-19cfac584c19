{"version": 1, "defects": {"Tests\\Unit\\PermissionCacheServiceTest::test_get_user_permissions_from_database": 8, "Tests\\Unit\\PermissionCacheServiceTest::test_get_user_permissions_from_cache": 8, "Tests\\Unit\\PermissionCacheServiceTest::test_has_permission": 8, "Tests\\Unit\\PermissionCacheServiceTest::test_get_user_role": 8, "Tests\\Unit\\PermissionCacheServiceTest::test_get_user_modules": 8, "Tests\\Unit\\PermissionCacheServiceTest::test_invalidate_user_cache": 8, "Tests\\Unit\\PermissionCacheServiceTest::test_invalidate_role_cache": 8, "Tests\\Unit\\PermissionCacheServiceTest::test_clear_all_cache": 8, "Tests\\Unit\\PermissionCacheServiceTest::test_user_without_role": 8, "Tests\\Unit\\PermissionCacheServiceTest::test_cache_stats": 8}, "times": {"Tests\\Unit\\PermissionCacheServiceTest::test_get_user_permissions_from_database": 0.228, "Tests\\Unit\\QueryOptimizationServiceTest::test_start_n1_monitoring": 0.01, "Tests\\Unit\\QueryOptimizationServiceTest::test_cache_query": 0.037, "Tests\\Unit\\QueryOptimizationServiceTest::test_invalidate_cache": 0.001, "Tests\\Unit\\QueryOptimizationServiceTest::test_get_optimization_recommendations": 0, "Tests\\Unit\\QueryOptimizationServiceTest::test_generate_optimization_report": 0.003, "Tests\\Unit\\QueryOptimizationServiceTest::test_clear_optimization_cache": 0, "Tests\\Unit\\QueryOptimizationServiceTest::test_n1_problem_detection_threshold": 0.085, "Tests\\Unit\\QueryOptimizationServiceTest::test_query_normalization": 0.017, "Tests\\Unit\\QueryOptimizationServiceTest::test_severity_calculation": 0.1, "Tests\\Unit\\QueryOptimizationServiceTest::test_suggestions_generation": 0.001, "Tests\\Unit\\SoftDeleteWithAuditTest::test_soft_delete_creates_audit_log": 0.097}}