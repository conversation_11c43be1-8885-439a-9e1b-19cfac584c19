<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Services\AccountLockoutService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class AccountSecurityController extends Controller
{
    /**
     * Show account security dashboard
     */
    public function index(): Response
    {
        $stats = AccountLockoutService::getLockoutStats();
        $usersRequiringAttention = AccountLockoutService::getUsersRequiringAttention();

        return Inertia::render('security/account-security', [
            'stats' => $stats,
            'users_requiring_attention' => $usersRequiringAttention
        ]);
    }

    /**
     * Get lockout statistics
     */
    public function stats(): JsonResponse
    {
        $stats = AccountLockoutService::getLockoutStats();
        
        return response()->json($stats);
    }

    /**
     * Unlock user account
     */
    public function unlockAccount(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id'
        ]);

        $user = User::findOrFail($request->user_id);
        $currentUser = Auth::user();

        // Check if current user has permission to unlock accounts
        if (!$currentUser->hasRole('Super Admin') && !$currentUser->hasRole('Admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Anda tidak memiliki izin untuk membuka kunci akun.'
            ], 403);
        }

        $success = AccountLockoutService::unlockAccount($user, $currentUser);

        return response()->json([
            'success' => $success,
            'message' => $success 
                ? "Akun {$user->full_name} berhasil dibuka." 
                : "Akun {$user->full_name} tidak dalam keadaan terkunci."
        ]);
    }

    /**
     * Reset failed attempts for user
     */
    public function resetFailedAttempts(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id'
        ]);

        $user = User::findOrFail($request->user_id);
        $currentUser = Auth::user();

        // Check permissions
        if (!$currentUser->hasRole('Super Admin') && !$currentUser->hasRole('Admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Anda tidak memiliki izin untuk mereset percobaan login.'
            ], 403);
        }

        AccountLockoutService::resetFailedAttempts($user, $currentUser);

        return response()->json([
            'success' => true,
            'message' => "Percobaan login gagal untuk {$user->full_name} berhasil direset."
        ]);
    }

    /**
     * Get locked users list
     */
    public function lockedUsers(): JsonResponse
    {
        $lockedUsers = User::whereNotNull('locked_until')
            ->where('locked_until', '>', now())
            ->with('role')
            ->orderBy('locked_until', 'desc')
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->getKey(),
                    'username' => $user->username,
                    'full_name' => $user->full_name,
                    'email' => $user->email,
                    'role' => $user->role?->nama_peran,
                    'failed_attempts' => $user->failed_login_attempts,
                    'locked_until' => $user->locked_until,
                    'is_permanent' => $user->locked_until->gt(now()->addYears(1)),
                    'time_remaining' => $user->locked_until->diffForHumans()
                ];
            });

        return response()->json($lockedUsers);
    }

    /**
     * Get users with high failed attempts
     */
    public function highRiskUsers(): JsonResponse
    {
        $highRiskUsers = User::where('failed_login_attempts', '>=', 3)
            ->where('failed_login_attempts', '<', AccountLockoutService::PERMANENT_LOCKOUT_THRESHOLD)
            ->with('role')
            ->orderBy('failed_login_attempts', 'desc')
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->getKey(),
                    'username' => $user->username,
                    'full_name' => $user->full_name,
                    'email' => $user->email,
                    'role' => $user->role?->nama_peran,
                    'failed_attempts' => $user->failed_login_attempts,
                    'attempts_until_lockout' => AccountLockoutService::MAX_FAILED_ATTEMPTS - $user->failed_login_attempts,
                    'is_locked' => $user->isLocked(),
                    'last_login' => $user->login_terakhir
                ];
            });

        return response()->json($highRiskUsers);
    }

    /**
     * Bulk unlock accounts
     */
    public function bulkUnlock(Request $request): JsonResponse
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id'
        ]);

        $currentUser = Auth::user();

        // Check permissions
        if (!$currentUser->hasRole('Super Admin') && !$currentUser->hasRole('Admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Anda tidak memiliki izin untuk membuka kunci akun.'
            ], 403);
        }

        $userIds = $request->user_ids;
        $users = User::whereIn('id', $userIds)->get();
        
        $unlockedCount = 0;
        $results = [];

        foreach ($users as $user) {
            $success = AccountLockoutService::unlockAccount($user, $currentUser);
            if ($success) {
                $unlockedCount++;
            }
            
            $results[] = [
                'user_id' => $user->getKey(),
                'username' => $user->username,
                'success' => $success
            ];
        }

        return response()->json([
            'success' => true,
            'message' => "Berhasil membuka {$unlockedCount} dari " . count($users) . " akun.",
            'unlocked_count' => $unlockedCount,
            'total_count' => count($users),
            'results' => $results
        ]);
    }

    /**
     * Get account security settings
     */
    public function securitySettings(): JsonResponse
    {
        return response()->json([
            'max_failed_attempts' => AccountLockoutService::MAX_FAILED_ATTEMPTS,
            'permanent_lockout_threshold' => AccountLockoutService::PERMANENT_LOCKOUT_THRESHOLD,
            'progressive_lockout_attempts' => AccountLockoutService::PROGRESSIVE_LOCKOUT_ATTEMPTS,
            'ip_lockout_enabled' => true,
            'ip_lockout_threshold' => 10,
            'ip_lockout_duration' => 60
        ]);
    }

    /**
     * Cleanup expired lockouts
     */
    public function cleanupExpiredLockouts(): JsonResponse
    {
        $currentUser = Auth::user();

        // Check permissions
        if (!$currentUser->hasRole('Super Admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Hanya Super Admin yang dapat melakukan cleanup.'
            ], 403);
        }

        $cleanedCount = AccountLockoutService::cleanupExpiredLockouts();

        return response()->json([
            'success' => true,
            'message' => "Berhasil membersihkan {$cleanedCount} lockout yang sudah expired.",
            'cleaned_count' => $cleanedCount
        ]);
    }

    /**
     * Export security report
     */
    public function exportSecurityReport(): JsonResponse
    {
        $currentUser = Auth::user();

        // Check permissions
        if (!$currentUser->hasRole('Super Admin') && !$currentUser->hasRole('Admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Anda tidak memiliki izin untuk mengekspor laporan keamanan.'
            ], 403);
        }

        $report = [
            'generated_at' => now()->toISOString(),
            'generated_by' => $currentUser->full_name,
            'stats' => AccountLockoutService::getLockoutStats(),
            'users_requiring_attention' => AccountLockoutService::getUsersRequiringAttention(),
            'security_settings' => [
                'max_failed_attempts' => AccountLockoutService::MAX_FAILED_ATTEMPTS,
                'permanent_lockout_threshold' => AccountLockoutService::PERMANENT_LOCKOUT_THRESHOLD,
                'progressive_lockout_attempts' => AccountLockoutService::PROGRESSIVE_LOCKOUT_ATTEMPTS
            ]
        ];

        return response()->json([
            'success' => true,
            'report' => $report
        ]);
    }
}
