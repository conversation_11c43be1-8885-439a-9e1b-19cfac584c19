<?php

namespace App\Console\Commands;

use App\Models\Patient;
use App\Models\Appointment;
use App\Models\MedicalRecord;
use Illuminate\Console\Command;

class SoftDeleteManagementCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'soft-delete:manage {action : Action to perform (stats|cleanup|restore)} {model? : Model to operate on (patient|appointment|medical-record)} {--days=90 : Days for cleanup} {--id= : Specific ID to restore}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage soft deleted records (statistics, cleanup, restore)';

    protected array $models = [
        'patient' => Patient::class,
        'appointment' => Appointment::class,
        'medical-record' => MedicalRecord::class,
    ];

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');
        $model = $this->argument('model');

        switch ($action) {
            case 'stats':
                return $this->showStats($model);
            case 'cleanup':
                return $this->cleanupOldRecords($model);
            case 'restore':
                return $this->restoreRecord($model);
            default:
                $this->error("Invalid action: {$action}");
                $this->info('Available actions: stats, cleanup, restore');
                return 1;
        }
    }

    /**
     * Show soft delete statistics
     */
    protected function showStats(?string $model): int
    {
        if ($model) {
            if (!isset($this->models[$model])) {
                $this->error("Invalid model: {$model}");
                return 1;
            }

            $this->showModelStats($model, $this->models[$model]);
        } else {
            $this->info('Soft Delete Statistics for All Models:');
            
            foreach ($this->models as $modelName => $modelClass) {
                $this->showModelStats($modelName, $modelClass);
            }
        }

        return 0;
    }

    /**
     * Show statistics for a specific model
     */
    protected function showModelStats(string $modelName, string $modelClass): void
    {
        $stats = $modelClass::getSoftDeleteStats();
        
        $this->line("\n<comment>" . ucfirst(str_replace('-', ' ', $modelName)) . " Statistics:</comment>");
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Records', $stats['total']],
                ['Active Records', $stats['active']],
                ['Soft Deleted', $stats['deleted']],
                ['Recently Deleted (30 days)', $stats['recently_deleted']],
                ['Old Deleted (90+ days)', $stats['old_deleted']],
            ]
        );

        // Show deletion percentage
        $deletionRate = $stats['total'] > 0 ? ($stats['deleted'] / $stats['total']) * 100 : 0;
        $this->line("Deletion Rate: " . number_format($deletionRate, 2) . "%");
    }

    /**
     * Cleanup old soft deleted records
     */
    protected function cleanupOldRecords(?string $model): int
    {
        $days = (int) $this->option('days');
        
        if ($model) {
            if (!isset($this->models[$model])) {
                $this->error("Invalid model: {$model}");
                return 1;
            }

            return $this->cleanupModel($model, $this->models[$model], $days);
        } else {
            $totalCleaned = 0;
            
            foreach ($this->models as $modelName => $modelClass) {
                $cleaned = $this->cleanupModel($modelName, $modelClass, $days);
                $totalCleaned += $cleaned;
            }
            
            $this->info("Total records permanently deleted: {$totalCleaned}");
        }

        return 0;
    }

    /**
     * Cleanup old records for a specific model
     */
    protected function cleanupModel(string $modelName, string $modelClass, int $days): int
    {
        $this->line("\n<comment>Cleaning up old " . str_replace('-', ' ', $modelName) . " records...</comment>");
        
        // Get count first
        $count = $modelClass::oldDeleted($days)->count();
        
        if ($count === 0) {
            $this->info("No old deleted records found (older than {$days} days)");
            return 0;
        }

        // Confirm before cleanup
        if (!$this->confirm("Found {$count} old deleted records. Permanently delete them?")) {
            $this->info('Cleanup cancelled.');
            return 0;
        }

        // Perform cleanup
        $cleaned = $modelClass::cleanupOldDeleted($days);
        
        $this->info("Permanently deleted {$cleaned} old {$modelName} records");
        
        return $cleaned;
    }

    /**
     * Restore a soft deleted record
     */
    protected function restoreRecord(?string $model): int
    {
        if (!$model) {
            $this->error('Model is required for restore action');
            $this->info('Available models: ' . implode(', ', array_keys($this->models)));
            return 1;
        }

        if (!isset($this->models[$model])) {
            $this->error("Invalid model: {$model}");
            return 1;
        }

        $id = $this->option('id');
        
        if (!$id) {
            return $this->showDeletedRecords($model, $this->models[$model]);
        }

        return $this->performRestore($model, $this->models[$model], $id);
    }

    /**
     * Show deleted records for selection
     */
    protected function showDeletedRecords(string $modelName, string $modelClass): int
    {
        $deletedRecords = $modelClass::onlyTrashed()
                                   ->orderBy('deleted_at', 'desc')
                                   ->limit(20)
                                   ->get();

        if ($deletedRecords->isEmpty()) {
            $this->info("No soft deleted {$modelName} records found");
            return 0;
        }

        $this->line("\n<comment>Recently Deleted " . ucfirst(str_replace('-', ' ', $modelName)) . " Records:</comment>");
        
        $tableData = [];
        foreach ($deletedRecords as $record) {
            $tableData[] = [
                $record->getKey(),
                $this->getRecordDisplayName($record),
                $record->deleted_at->format('Y-m-d H:i:s'),
                $record->deleted_at->diffForHumans(),
            ];
        }
        
        $this->table(
            ['ID', 'Name/Description', 'Deleted At', 'Deleted'],
            $tableData
        );

        $this->line("\nTo restore a record, use: --id=<ID>");
        
        return 0;
    }

    /**
     * Get display name for a record
     */
    protected function getRecordDisplayName($record): string
    {
        if (method_exists($record, 'getFullNameAttribute')) {
            return $record->full_name;
        }
        
        if (isset($record->nama_depan) && isset($record->nama_belakang)) {
            return $record->nama_depan . ' ' . $record->nama_belakang;
        }
        
        if (isset($record->nomor_janji)) {
            return $record->nomor_janji;
        }
        
        if (isset($record->keluhan_utama)) {
            return substr($record->keluhan_utama, 0, 50) . '...';
        }
        
        return 'Record #' . $record->getKey();
    }

    /**
     * Perform restore operation
     */
    protected function performRestore(string $modelName, string $modelClass, string $id): int
    {
        $record = $modelClass::withTrashed()->find($id);
        
        if (!$record) {
            $this->error("Record with ID {$id} not found");
            return 1;
        }

        if (!$record->trashed()) {
            $this->error("Record with ID {$id} is not deleted");
            return 1;
        }

        $displayName = $this->getRecordDisplayName($record);
        
        if (!$this->confirm("Restore {$modelName} record: {$displayName}?")) {
            $this->info('Restore cancelled.');
            return 0;
        }

        $reason = $this->ask('Enter reason for restore (optional)') ?? 'Restored via command';
        
        try {
            $record->restoreWithReason($reason);
            $this->info("Successfully restored {$modelName} record: {$displayName}");
            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to restore record: " . $e->getMessage());
            return 1;
        }
    }
}
