<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('patients', function (Blueprint $table) {
            $table->id('id_pasien');
            $table->string('nomor_pasien')->unique();
            $table->string('nama_depan');
            $table->string('nama_belakang');
            $table->date('tanggal_lahir');
            $table->enum('jenis_kelamin', ['L', 'P']);
            $table->string('no_telepon')->nullable();
            $table->string('email')->nullable();
            $table->text('alamat')->nullable();
            $table->string('kota')->nullable();
            $table->string('kode_pos', 10)->nullable();
            $table->string('provinsi')->nullable();
            $table->string('kabupaten_kota')->nullable();
            $table->string('kecamatan')->nullable();
            $table->string('kelurahan_desa')->nullable();
            $table->string('rt_rw', 20)->nullable();
            $table->string('nik', 16)->unique()->nullable();
            $table->string('nama_kontak_darurat')->nullable();
            $table->string('telepon_kontak_darurat')->nullable();
            $table->string('penyedia_asuransi')->nullable();
            $table->string('nomor_asuransi')->nullable();
            $table->string('nomor_bpjs')->nullable();
            $table->enum('kelas_bpjs', ['1', '2', '3'])->nullable();
            $table->enum('status_kepesertaan_bpjs', ['aktif', 'tidak_aktif', 'pending'])->nullable();
            $table->string('faskes_tingkat_1')->nullable();
            $table->text('alergi')->nullable();
            $table->text('riwayat_medis')->nullable();
            $table->text('catatan')->nullable();
            $table->boolean('aktif')->default(true);
            $table->timestamps();
            $table->softDeletes();

            // Indexes untuk performance
            $table->index(['nomor_pasien']);
            $table->index(['nik']);
            $table->index(['aktif', 'created_at'], 'idx_patients_active_created');
            $table->index(['nama_depan', 'nama_belakang'], 'idx_patients_fullname');
            $table->index(['tanggal_lahir'], 'idx_patients_birthdate');
            $table->index(['jenis_kelamin'], 'idx_patients_gender');
            $table->index(['no_telepon'], 'idx_patients_phone');
            $table->index(['email'], 'idx_patients_email');
            $table->index(['nomor_bpjs'], 'idx_patients_bpjs');
            $table->index(['kelas_bpjs'], 'idx_patients_bpjs_class');
            $table->index(['status_kepesertaan_bpjs'], 'idx_patients_bpjs_status');
            $table->index(['kota', 'provinsi'], 'idx_patients_location');
            
            // Composite indexes untuk searching dan filtering
            $table->index(['aktif', 'nama_depan', 'nama_belakang'], 'idx_patients_active_name');
            $table->index(['jenis_kelamin', 'aktif'], 'idx_patients_gender_active');
            $table->index(['created_at', 'aktif'], 'idx_patients_created_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('patients');
    }
};
