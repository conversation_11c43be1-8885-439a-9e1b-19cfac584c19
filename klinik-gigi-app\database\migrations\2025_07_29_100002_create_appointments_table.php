<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('appointments', function (Blueprint $table) {
            $table->id('id_janji_temu');
            $table->string('nomor_janji')->unique();
            $table->unsignedBigInteger('id_pasien');
            $table->unsignedBigInteger('id_dokter');
            $table->unsignedBigInteger('id_ruang_perawatan')->nullable();
            $table->datetime('tanggal_janji');
            $table->integer('durasi_menit')->default(30);
            $table->enum('status', ['dijadwalkan', 'dikonfirmasi', 'sedang_berlangsung', 'selesai', 'dibatalkan', 'tidak_hadir']);
            $table->enum('jenis_janji', ['konsultasi', 'perawatan', 'kontrol', 'emergency']);
            $table->text('catatan')->nullable();
            $table->text('keluhan_utama')->nullable();
            $table->decimal('estimasi_biaya', 12, 2)->nullable();
            $table->unsignedBigInteger('dibuat_oleh');
            $table->timestamps();
            $table->softDeletes();

            // Foreign key constraints
            $table->foreign('id_pasien')->references('id_pasien')->on('patients')->onDelete('cascade');
            $table->foreign('id_dokter')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('dibuat_oleh')->references('id')->on('users')->onDelete('cascade');

            // Indexes untuk performance
            $table->index(['nomor_janji']);
            $table->index(['id_pasien', 'tanggal_janji'], 'idx_appointments_patient_date');
            $table->index(['id_dokter', 'tanggal_janji'], 'idx_appointments_doctor_date');
            $table->index(['tanggal_janji', 'status'], 'idx_appointments_date_status');
            $table->index(['status'], 'idx_appointments_status');
            $table->index(['jenis_janji'], 'idx_appointments_type');
            $table->index(['created_at'], 'idx_appointments_created');
            
            // Composite indexes untuk scheduling queries
            $table->index(['id_dokter', 'tanggal_janji', 'status'], 'idx_appointments_doctor_date_status');
            $table->index(['id_ruang_perawatan', 'tanggal_janji', 'status'], 'idx_appointments_room_date_status');
            $table->index(['tanggal_janji', 'id_dokter', 'durasi_menit'], 'idx_appointments_schedule');
            
            // Index untuk reporting
            $table->index(['status', 'created_at'], 'idx_appointments_status_created');
            $table->index(['jenis_janji', 'status', 'tanggal_janji'], 'idx_appointments_type_status_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('appointments');
    }
};
