<?php

namespace App\Http\Middleware;

use App\Models\AuditLog;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AuditMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only audit authenticated requests
        if (Auth::check()) {
            $this->logRequest($request, $response);
        }

        return $response;
    }

    /**
     * Log the request for audit purposes
     */
    private function logRequest(Request $request, Response $response): void
    {
        // Skip logging for certain routes to avoid noise
        if ($this->shouldSkipLogging($request)) {
            return;
        }

        $action = $this->determineAction($request);
        $severity = $this->determineSeverity($request, $response);

        // Extract relevant data
        $additionalData = [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'route' => $request->route()?->getName(),
            'status_code' => $response->getStatusCode(),
            'response_time' => microtime(true) - LARAVEL_START,
        ];

        // Add request data for certain actions
        if ($this->shouldLogRequestData($request)) {
            $additionalData['request_data'] = $this->sanitizeRequestData($request->all());
        }

        AuditLog::log(
            action: $action,
            additionalData: $additionalData,
            severity: $severity
        );
    }

    /**
     * Determine if we should skip logging this request
     */
    private function shouldSkipLogging(Request $request): bool
    {
        $skipRoutes = [
            'debugbar.*',
            'horizon.*',
            'telescope.*',
            '_ignition.*',
            'livewire.*'
        ];

        $routeName = $request->route()?->getName();
        
        if ($routeName) {
            foreach ($skipRoutes as $pattern) {
                if (fnmatch($pattern, $routeName)) {
                    return true;
                }
            }
        }

        // Skip asset requests
        if ($request->is('css/*', 'js/*', 'images/*', 'fonts/*')) {
            return true;
        }

        // Skip AJAX requests for certain endpoints
        if ($request->ajax() && $request->is('api/heartbeat', 'api/status')) {
            return true;
        }

        return false;
    }

    /**
     * Determine the action based on the request
     */
    private function determineAction(Request $request): string
    {
        $method = $request->method();
        $routeName = $request->route()?->getName();
        
        // Map common route patterns to actions
        if ($routeName) {
            if (str_contains($routeName, 'users.')) {
                return match(true) {
                    str_contains($routeName, 'create') || str_contains($routeName, 'store') => 'user_create_attempt',
                    str_contains($routeName, 'edit') || str_contains($routeName, 'update') => 'user_update_attempt',
                    str_contains($routeName, 'destroy') => 'user_delete_attempt',
                    str_contains($routeName, 'show') => 'user_view',
                    str_contains($routeName, 'index') => 'users_list_view',
                    default => 'user_action'
                };
            }

            if (str_contains($routeName, 'roles.')) {
                return 'role_management';
            }

            if (str_contains($routeName, 'permissions.')) {
                return 'permission_management';
            }
        }

        // Fallback to HTTP method-based actions
        return match($method) {
            'GET' => 'page_view',
            'POST' => 'data_create',
            'PUT', 'PATCH' => 'data_update',
            'DELETE' => 'data_delete',
            default => 'request'
        };
    }

    /**
     * Determine severity based on request and response
     */
    private function determineSeverity(Request $request, Response $response): string
    {
        $statusCode = $response->getStatusCode();
        $method = $request->method();
        $routeName = $request->route()?->getName();

        // Critical severity for errors and sensitive operations
        if ($statusCode >= 500) {
            return AuditLog::SEVERITY_CRITICAL;
        }

        if ($statusCode >= 400) {
            return AuditLog::SEVERITY_HIGH;
        }

        // High severity for destructive operations
        if ($method === 'DELETE' || str_contains($routeName ?? '', 'destroy')) {
            return AuditLog::SEVERITY_HIGH;
        }

        // Medium severity for data modifications
        if (in_array($method, ['POST', 'PUT', 'PATCH'])) {
            return AuditLog::SEVERITY_MEDIUM;
        }

        // Low severity for read operations
        return AuditLog::SEVERITY_LOW;
    }

    /**
     * Determine if we should log request data
     */
    private function shouldLogRequestData(Request $request): bool
    {
        $method = $request->method();
        
        // Log request data for modifications
        return in_array($method, ['POST', 'PUT', 'PATCH', 'DELETE']);
    }

    /**
     * Sanitize request data to remove sensitive information
     */
    private function sanitizeRequestData(array $data): array
    {
        $sensitiveFields = [
            'password',
            'password_confirmation',
            'current_password',
            'token',
            'api_key',
            'secret',
            '_token',
            'remember_token'
        ];

        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }

        return $data;
    }
}
