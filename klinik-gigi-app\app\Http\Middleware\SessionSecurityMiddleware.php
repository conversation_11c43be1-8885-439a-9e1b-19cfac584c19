<?php

namespace App\Http\Middleware;

use App\Services\SessionSecurityService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class SessionSecurityMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip for non-authenticated requests
        if (!Auth::check()) {
            return $next($request);
        }

        // Skip for AJAX requests that shouldn't trigger session validation
        if ($this->shouldSkipSessionValidation($request)) {
            return $next($request);
        }

        // Validate session security
        if (!SessionSecurityService::validateSessionSecurity()) {
            return $this->handleSessionSecurityFailure($request);
        }

        // Update session activity
        SessionSecurityService::updateSessionActivity();

        $response = $next($request);

        // Add session security headers
        $this->addSecurityHeaders($response);

        return $response;
    }

    /**
     * Determine if session validation should be skipped
     */
    private function shouldSkipSessionValidation(Request $request): bool
    {
        // Skip for certain AJAX endpoints
        $skipRoutes = [
            'api/session/check',
            'api/session/extend',
            'api/heartbeat'
        ];

        foreach ($skipRoutes as $route) {
            if ($request->is($route)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Handle session security failure
     */
    private function handleSessionSecurityFailure(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Session expired or invalid',
                'redirect' => route('login')
            ], 401);
        }

        return redirect()->route('login')
            ->with('error', 'Sesi Anda telah berakhir atau tidak valid. Silakan login kembali.');
    }

    /**
     * Add security headers to response
     */
    private function addSecurityHeaders(Response $response): void
    {
        $headers = [
            'X-Frame-Options' => 'DENY',
            'X-Content-Type-Options' => 'nosniff',
            'X-XSS-Protection' => '1; mode=block',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Permissions-Policy' => 'geolocation=(), microphone=(), camera=()',
        ];

        foreach ($headers as $header => $value) {
            $response->headers->set($header, $value);
        }
    }
}
