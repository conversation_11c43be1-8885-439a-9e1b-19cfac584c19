<?php

namespace App\Traits;

use App\Models\AuditLog;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

trait SoftDeleteWithAudit
{
    use SoftDeletes;

    /**
     * Boot the trait
     */
    public static function bootSoftDeleteWithAudit(): void
    {
        // Log when model is soft deleted
        static::deleting(function ($model) {
            if (!$model->isForceDeleting()) {
                AuditLog::log(
                    action: 'soft_delete',
                    modelType: get_class($model),
                    modelId: $model->getKey(),
                    oldValues: $model->getOriginal(),
                    additionalData: [
                        'deleted_by' => Auth::id(),
                        'deleted_at' => now(),
                        'reason' => request()->input('delete_reason', 'No reason provided')
                    ],
                    severity: AuditLog::SEVERITY_MEDIUM
                );
            }
        });

        // Log when model is restored
        static::restored(function ($model) {
            AuditLog::log(
                action: 'restore',
                modelType: get_class($model),
                modelId: $model->getKey(),
                newValues: $model->getAttributes(),
                additionalData: [
                    'restored_by' => Auth::id(),
                    'restored_at' => now(),
                    'reason' => request()->input('restore_reason', 'No reason provided')
                ],
                severity: AuditLog::SEVERITY_MEDIUM
            );
        });

        // Log when model is force deleted
        static::forceDeleted(function ($model) {
            AuditLog::log(
                action: 'force_delete',
                modelType: get_class($model),
                modelId: $model->getKey(),
                oldValues: $model->getOriginal(),
                additionalData: [
                    'force_deleted_by' => Auth::id(),
                    'force_deleted_at' => now(),
                    'reason' => request()->input('force_delete_reason', 'No reason provided')
                ],
                severity: AuditLog::SEVERITY_HIGH
            );
        });
    }

    /**
     * Soft delete with reason
     */
    public function softDeleteWithReason(string $reason = null): bool
    {
        if ($reason) {
            request()->merge(['delete_reason' => $reason]);
        }

        return $this->delete();
    }

    /**
     * Restore with reason
     */
    public function restoreWithReason(string $reason = null): bool
    {
        if ($reason) {
            request()->merge(['restore_reason' => $reason]);
        }

        return $this->restore();
    }

    /**
     * Force delete with reason
     */
    public function forceDeleteWithReason(string $reason = null): bool
    {
        if ($reason) {
            request()->merge(['force_delete_reason' => $reason]);
        }

        return $this->forceDelete();
    }

    /**
     * Check if model can be deleted
     */
    public function canBeDeleted(): array
    {
        $result = [
            'can_delete' => true,
            'reasons' => [],
            'dependencies' => []
        ];

        // Check for dependencies (override in child models)
        $dependencies = $this->getDependencies();
        
        foreach ($dependencies as $relation => $config) {
            $count = $this->{$relation}()->count();
            
            if ($count > 0) {
                $result['can_delete'] = false;
                $result['reasons'][] = $config['message'] ?? "Has {$count} related {$relation}";
                $result['dependencies'][$relation] = $count;
            }
        }

        return $result;
    }

    /**
     * Get model dependencies (override in child models)
     */
    protected function getDependencies(): array
    {
        return [];
    }

    /**
     * Safe delete - checks dependencies first
     */
    public function safeDelete(string $reason = null): array
    {
        $canDelete = $this->canBeDeleted();
        
        if (!$canDelete['can_delete']) {
            return [
                'success' => false,
                'message' => 'Cannot delete due to dependencies',
                'details' => $canDelete
            ];
        }

        try {
            $this->softDeleteWithReason($reason);
            
            return [
                'success' => true,
                'message' => 'Successfully deleted',
                'deleted_at' => $this->deleted_at
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to delete: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Batch soft delete
     */
    public static function batchSoftDelete(array $ids, string $reason = null): array
    {
        $deleted = 0;
        $failed = [];

        foreach ($ids as $id) {
            $model = static::find($id);
            
            if (!$model) {
                $failed[] = ['id' => $id, 'reason' => 'Model not found'];
                continue;
            }

            $result = $model->safeDelete($reason);
            
            if ($result['success']) {
                $deleted++;
            } else {
                $failed[] = ['id' => $id, 'reason' => $result['message']];
            }
        }

        return [
            'deleted' => $deleted,
            'failed' => $failed,
            'total' => count($ids)
        ];
    }

    /**
     * Batch restore
     */
    public static function batchRestore(array $ids, string $reason = null): array
    {
        $restored = 0;
        $failed = [];

        foreach ($ids as $id) {
            $model = static::withTrashed()->find($id);
            
            if (!$model) {
                $failed[] = ['id' => $id, 'reason' => 'Model not found'];
                continue;
            }

            if (!$model->trashed()) {
                $failed[] = ['id' => $id, 'reason' => 'Model is not deleted'];
                continue;
            }

            try {
                $model->restoreWithReason($reason);
                $restored++;
            } catch (\Exception $e) {
                $failed[] = ['id' => $id, 'reason' => $e->getMessage()];
            }
        }

        return [
            'restored' => $restored,
            'failed' => $failed,
            'total' => count($ids)
        ];
    }

    /**
     * Get deletion history for this model
     */
    public function getDeletionHistory(): \Illuminate\Database\Eloquent\Collection
    {
        return AuditLog::where('model_type', get_class($this))
                      ->where('model_id', $this->getKey())
                      ->whereIn('action', ['soft_delete', 'restore', 'force_delete'])
                      ->orderBy('created_at', 'desc')
                      ->get();
    }

    /**
     * Scope for recently deleted items
     */
    public function scopeRecentlyDeleted($query, int $days = 30)
    {
        return $query->onlyTrashed()
                    ->where('deleted_at', '>=', now()->subDays($days));
    }

    /**
     * Scope for old deleted items (candidates for force delete)
     */
    public function scopeOldDeleted($query, int $days = 90)
    {
        return $query->onlyTrashed()
                    ->where('deleted_at', '<=', now()->subDays($days));
    }

    /**
     * Auto cleanup old soft deleted records
     */
    public static function cleanupOldDeleted(int $days = 90): int
    {
        $models = static::oldDeleted($days)->get();
        $count = 0;

        foreach ($models as $model) {
            $model->forceDeleteWithReason('Auto cleanup after ' . $days . ' days');
            $count++;
        }

        return $count;
    }

    /**
     * Get soft delete statistics
     */
    public static function getSoftDeleteStats(): array
    {
        return [
            'total' => static::withTrashed()->count(),
            'active' => static::count(),
            'deleted' => static::onlyTrashed()->count(),
            'recently_deleted' => static::recentlyDeleted(30)->count(),
            'old_deleted' => static::oldDeleted(90)->count(),
        ];
    }
}
