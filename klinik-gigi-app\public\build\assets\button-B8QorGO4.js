import{r as b,j as U}from"./app-EmUGGW4y.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oe=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Ce=(...e)=>e.filter((o,r,t)=>!!o&&o.trim()!==""&&t.indexOf(o)===r).join(" ").trim();/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var _e={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $e=b.forwardRef(({color:e="currentColor",size:o=24,strokeWidth:r=2,absoluteStrokeWidth:t,className:n="",children:s,iconNode:l,...p},d)=>b.createElement("svg",{ref:d,..._e,width:o,height:o,stroke:e,strokeWidth:t?Number(r)*24/Number(o):r,className:Ce("lucide",n),...p},[...l.map(([f,g])=>b.createElement(f,g)),...Array.isArray(s)?s:[s]]));/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Er=(e,o)=>{const r=b.forwardRef(({className:t,...n},s)=>b.createElement($e,{ref:s,iconNode:o,className:Ce(`lucide-${Oe(e)}`,t),...n}));return r.displayName=`${e}`,r};function ze(e){var o,r,t="";if(typeof e=="string"||typeof e=="number")t+=e;else if(typeof e=="object")if(Array.isArray(e)){var n=e.length;for(o=0;o<n;o++)e[o]&&(r=ze(e[o]))&&(t&&(t+=" "),t+=r)}else for(r in e)e[r]&&(t&&(t+=" "),t+=r);return t}function Se(){for(var e,o,r=0,t="",n=arguments.length;r<n;r++)(e=arguments[r])&&(o=ze(e))&&(t&&(t+=" "),t+=o);return t}const ie="-",Be=e=>{const o=Fe(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:t}=e;return{getClassGroupId:l=>{const p=l.split(ie);return p[0]===""&&p.length!==1&&p.shift(),Ae(p,o)||We(l)},getConflictingClassGroupIds:(l,p)=>{const d=r[l]||[];return p&&t[l]?[...d,...t[l]]:d}}},Ae=(e,o)=>{if(e.length===0)return o.classGroupId;const r=e[0],t=o.nextPart.get(r),n=t?Ae(e.slice(1),t):void 0;if(n)return n;if(o.validators.length===0)return;const s=e.join(ie);return o.validators.find(({validator:l})=>l(s))?.classGroupId},be=/^\[(.+)\]$/,We=e=>{if(be.test(e)){const o=be.exec(e)[1],r=o?.substring(0,o.indexOf(":"));if(r)return"arbitrary.."+r}},Fe=e=>{const{theme:o,classGroups:r}=e,t={nextPart:new Map,validators:[]};for(const n in r)ne(r[n],t,n,o);return t},ne=(e,o,r,t)=>{e.forEach(n=>{if(typeof n=="string"){const s=n===""?o:he(o,n);s.classGroupId=r;return}if(typeof n=="function"){if(Ue(n)){ne(n(t),o,r,t);return}o.validators.push({validator:n,classGroupId:r});return}Object.entries(n).forEach(([s,l])=>{ne(l,he(o,s),r,t)})})},he=(e,o)=>{let r=e;return o.split(ie).forEach(t=>{r.nextPart.has(t)||r.nextPart.set(t,{nextPart:new Map,validators:[]}),r=r.nextPart.get(t)}),r},Ue=e=>e.isThemeGetter,De=e=>{if(e<1)return{get:()=>{},set:()=>{}};let o=0,r=new Map,t=new Map;const n=(s,l)=>{r.set(s,l),o++,o>e&&(o=0,t=r,r=new Map)};return{get(s){let l=r.get(s);if(l!==void 0)return l;if((l=t.get(s))!==void 0)return n(s,l),l},set(s,l){r.has(s)?r.set(s,l):n(s,l)}}},se="!",ae=":",He=ae.length,qe=e=>{const{prefix:o,experimentalParseClassName:r}=e;let t=n=>{const s=[];let l=0,p=0,d=0,f;for(let v=0;v<n.length;v++){let k=n[v];if(l===0&&p===0){if(k===ae){s.push(n.slice(d,v)),d=v+He;continue}if(k==="/"){f=v;continue}}k==="["?l++:k==="]"?l--:k==="("?p++:k===")"&&p--}const g=s.length===0?n:n.substring(d),y=Ke(g),z=y!==g,P=f&&f>d?f-d:void 0;return{modifiers:s,hasImportantModifier:z,baseClassName:y,maybePostfixModifierPosition:P}};if(o){const n=o+ae,s=t;t=l=>l.startsWith(n)?s(l.substring(n.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:l,maybePostfixModifierPosition:void 0}}if(r){const n=t;t=s=>r({className:s,parseClassName:n})}return t},Ke=e=>e.endsWith(se)?e.substring(0,e.length-1):e.startsWith(se)?e.substring(1):e,Ze=e=>{const o=Object.fromEntries(e.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;const n=[];let s=[];return t.forEach(l=>{l[0]==="["||o[l]?(n.push(...s.sort(),l),s=[]):s.push(l)}),n.push(...s.sort()),n}},Je=e=>({cache:De(e.cacheSize),parseClassName:qe(e),sortModifiers:Ze(e),...Be(e)}),Xe=/\s+/,Qe=(e,o)=>{const{parseClassName:r,getClassGroupId:t,getConflictingClassGroupIds:n,sortModifiers:s}=o,l=[],p=e.trim().split(Xe);let d="";for(let f=p.length-1;f>=0;f-=1){const g=p[f],{isExternal:y,modifiers:z,hasImportantModifier:P,baseClassName:v,maybePostfixModifierPosition:k}=r(g);if(y){d=g+(d.length>0?" "+d:d);continue}let S=!!k,E=t(S?v.substring(0,k):v);if(!E){if(!S){d=g+(d.length>0?" "+d:d);continue}if(E=t(v),!E){d=g+(d.length>0?" "+d:d);continue}S=!1}const D=s(z).join(":"),B=P?D+se:D,j=B+E;if(l.includes(j))continue;l.push(j);const T=n(E,S);for(let N=0;N<T.length;++N){const W=T[N];l.push(B+W)}d=g+(d.length>0?" "+d:d)}return d};function Ye(){let e=0,o,r,t="";for(;e<arguments.length;)(o=arguments[e++])&&(r=Re(o))&&(t&&(t+=" "),t+=r);return t}const Re=e=>{if(typeof e=="string")return e;let o,r="";for(let t=0;t<e.length;t++)e[t]&&(o=Re(e[t]))&&(r&&(r+=" "),r+=o);return r};function er(e,...o){let r,t,n,s=l;function l(d){const f=o.reduce((g,y)=>y(g),e());return r=Je(f),t=r.cache.get,n=r.cache.set,s=p,p(d)}function p(d){const f=t(d);if(f)return f;const g=Qe(d,r);return n(d,g),g}return function(){return s(Ye.apply(null,arguments))}}const h=e=>{const o=r=>r[e]||[];return o.isThemeGetter=!0,o},Me=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Ie=/^\((?:(\w[\w-]*):)?(.+)\)$/i,rr=/^\d+\/\d+$/,or=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,tr=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,nr=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,sr=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ar=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,O=e=>rr.test(e),m=e=>!!e&&!Number.isNaN(Number(e)),I=e=>!!e&&Number.isInteger(Number(e)),oe=e=>e.endsWith("%")&&m(e.slice(0,-1)),M=e=>or.test(e),ir=()=>!0,lr=e=>tr.test(e)&&!nr.test(e),Pe=()=>!1,cr=e=>sr.test(e),dr=e=>ar.test(e),ur=e=>!a(e)&&!i(e),mr=e=>_(e,Ge,Pe),a=e=>Me.test(e),V=e=>_(e,Ve,lr),te=e=>_(e,hr,m),xe=e=>_(e,Ee,Pe),pr=e=>_(e,Ne,dr),X=e=>_(e,je,cr),i=e=>Ie.test(e),F=e=>$(e,Ve),fr=e=>$(e,xr),ye=e=>$(e,Ee),gr=e=>$(e,Ge),br=e=>$(e,Ne),Q=e=>$(e,je,!0),_=(e,o,r)=>{const t=Me.exec(e);return t?t[1]?o(t[1]):r(t[2]):!1},$=(e,o,r=!1)=>{const t=Ie.exec(e);return t?t[1]?o(t[1]):r:!1},Ee=e=>e==="position"||e==="percentage",Ne=e=>e==="image"||e==="url",Ge=e=>e==="length"||e==="size"||e==="bg-size",Ve=e=>e==="length",hr=e=>e==="number",xr=e=>e==="family-name",je=e=>e==="shadow",yr=()=>{const e=h("color"),o=h("font"),r=h("text"),t=h("font-weight"),n=h("tracking"),s=h("leading"),l=h("breakpoint"),p=h("container"),d=h("spacing"),f=h("radius"),g=h("shadow"),y=h("inset-shadow"),z=h("text-shadow"),P=h("drop-shadow"),v=h("blur"),k=h("perspective"),S=h("aspect"),E=h("ease"),D=h("animate"),B=()=>["auto","avoid","all","avoid-page","page","left","right","column"],j=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],T=()=>[...j(),i,a],N=()=>["auto","hidden","clip","visible","scroll"],W=()=>["auto","contain","none"],u=()=>[i,a,d],A=()=>[O,"full","auto",...u()],le=()=>[I,"none","subgrid",i,a],ce=()=>["auto",{span:["full",I,i,a]},I,i,a],H=()=>[I,"auto",i,a],de=()=>["auto","min","max","fr",i,a],Y=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],L=()=>["start","end","center","stretch","center-safe","end-safe"],R=()=>["auto",...u()],G=()=>[O,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...u()],c=()=>[e,i,a],ue=()=>[...j(),ye,xe,{position:[i,a]}],me=()=>["no-repeat",{repeat:["","x","y","space","round"]}],pe=()=>["auto","cover","contain",gr,mr,{size:[i,a]}],ee=()=>[oe,F,V],w=()=>["","none","full",f,i,a],C=()=>["",m,F,V],q=()=>["solid","dashed","dotted","double"],fe=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],x=()=>[m,oe,ye,xe],ge=()=>["","none",v,i,a],K=()=>["none",m,i,a],Z=()=>["none",m,i,a],re=()=>[m,i,a],J=()=>[O,"full",...u()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[M],breakpoint:[M],color:[ir],container:[M],"drop-shadow":[M],ease:["in","out","in-out"],font:[ur],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[M],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[M],shadow:[M],spacing:["px",m],text:[M],"text-shadow":[M],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",O,a,i,S]}],container:["container"],columns:[{columns:[m,a,i,p]}],"break-after":[{"break-after":B()}],"break-before":[{"break-before":B()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:T()}],overflow:[{overflow:N()}],"overflow-x":[{"overflow-x":N()}],"overflow-y":[{"overflow-y":N()}],overscroll:[{overscroll:W()}],"overscroll-x":[{"overscroll-x":W()}],"overscroll-y":[{"overscroll-y":W()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:A()}],"inset-x":[{"inset-x":A()}],"inset-y":[{"inset-y":A()}],start:[{start:A()}],end:[{end:A()}],top:[{top:A()}],right:[{right:A()}],bottom:[{bottom:A()}],left:[{left:A()}],visibility:["visible","invisible","collapse"],z:[{z:[I,"auto",i,a]}],basis:[{basis:[O,"full","auto",p,...u()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[m,O,"auto","initial","none",a]}],grow:[{grow:["",m,i,a]}],shrink:[{shrink:["",m,i,a]}],order:[{order:[I,"first","last","none",i,a]}],"grid-cols":[{"grid-cols":le()}],"col-start-end":[{col:ce()}],"col-start":[{"col-start":H()}],"col-end":[{"col-end":H()}],"grid-rows":[{"grid-rows":le()}],"row-start-end":[{row:ce()}],"row-start":[{"row-start":H()}],"row-end":[{"row-end":H()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":de()}],"auto-rows":[{"auto-rows":de()}],gap:[{gap:u()}],"gap-x":[{"gap-x":u()}],"gap-y":[{"gap-y":u()}],"justify-content":[{justify:[...Y(),"normal"]}],"justify-items":[{"justify-items":[...L(),"normal"]}],"justify-self":[{"justify-self":["auto",...L()]}],"align-content":[{content:["normal",...Y()]}],"align-items":[{items:[...L(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...L(),{baseline:["","last"]}]}],"place-content":[{"place-content":Y()}],"place-items":[{"place-items":[...L(),"baseline"]}],"place-self":[{"place-self":["auto",...L()]}],p:[{p:u()}],px:[{px:u()}],py:[{py:u()}],ps:[{ps:u()}],pe:[{pe:u()}],pt:[{pt:u()}],pr:[{pr:u()}],pb:[{pb:u()}],pl:[{pl:u()}],m:[{m:R()}],mx:[{mx:R()}],my:[{my:R()}],ms:[{ms:R()}],me:[{me:R()}],mt:[{mt:R()}],mr:[{mr:R()}],mb:[{mb:R()}],ml:[{ml:R()}],"space-x":[{"space-x":u()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":u()}],"space-y-reverse":["space-y-reverse"],size:[{size:G()}],w:[{w:[p,"screen",...G()]}],"min-w":[{"min-w":[p,"screen","none",...G()]}],"max-w":[{"max-w":[p,"screen","none","prose",{screen:[l]},...G()]}],h:[{h:["screen","lh",...G()]}],"min-h":[{"min-h":["screen","lh","none",...G()]}],"max-h":[{"max-h":["screen","lh",...G()]}],"font-size":[{text:["base",r,F,V]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[t,i,te]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",oe,a]}],"font-family":[{font:[fr,a,o]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,i,a]}],"line-clamp":[{"line-clamp":[m,"none",i,te]}],leading:[{leading:[s,...u()]}],"list-image":[{"list-image":["none",i,a]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",i,a]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:c()}],"text-color":[{text:c()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...q(),"wavy"]}],"text-decoration-thickness":[{decoration:[m,"from-font","auto",i,V]}],"text-decoration-color":[{decoration:c()}],"underline-offset":[{"underline-offset":[m,"auto",i,a]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:u()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",i,a]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",i,a]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ue()}],"bg-repeat":[{bg:me()}],"bg-size":[{bg:pe()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},I,i,a],radial:["",i,a],conic:[I,i,a]},br,pr]}],"bg-color":[{bg:c()}],"gradient-from-pos":[{from:ee()}],"gradient-via-pos":[{via:ee()}],"gradient-to-pos":[{to:ee()}],"gradient-from":[{from:c()}],"gradient-via":[{via:c()}],"gradient-to":[{to:c()}],rounded:[{rounded:w()}],"rounded-s":[{"rounded-s":w()}],"rounded-e":[{"rounded-e":w()}],"rounded-t":[{"rounded-t":w()}],"rounded-r":[{"rounded-r":w()}],"rounded-b":[{"rounded-b":w()}],"rounded-l":[{"rounded-l":w()}],"rounded-ss":[{"rounded-ss":w()}],"rounded-se":[{"rounded-se":w()}],"rounded-ee":[{"rounded-ee":w()}],"rounded-es":[{"rounded-es":w()}],"rounded-tl":[{"rounded-tl":w()}],"rounded-tr":[{"rounded-tr":w()}],"rounded-br":[{"rounded-br":w()}],"rounded-bl":[{"rounded-bl":w()}],"border-w":[{border:C()}],"border-w-x":[{"border-x":C()}],"border-w-y":[{"border-y":C()}],"border-w-s":[{"border-s":C()}],"border-w-e":[{"border-e":C()}],"border-w-t":[{"border-t":C()}],"border-w-r":[{"border-r":C()}],"border-w-b":[{"border-b":C()}],"border-w-l":[{"border-l":C()}],"divide-x":[{"divide-x":C()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":C()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...q(),"hidden","none"]}],"divide-style":[{divide:[...q(),"hidden","none"]}],"border-color":[{border:c()}],"border-color-x":[{"border-x":c()}],"border-color-y":[{"border-y":c()}],"border-color-s":[{"border-s":c()}],"border-color-e":[{"border-e":c()}],"border-color-t":[{"border-t":c()}],"border-color-r":[{"border-r":c()}],"border-color-b":[{"border-b":c()}],"border-color-l":[{"border-l":c()}],"divide-color":[{divide:c()}],"outline-style":[{outline:[...q(),"none","hidden"]}],"outline-offset":[{"outline-offset":[m,i,a]}],"outline-w":[{outline:["",m,F,V]}],"outline-color":[{outline:c()}],shadow:[{shadow:["","none",g,Q,X]}],"shadow-color":[{shadow:c()}],"inset-shadow":[{"inset-shadow":["none",y,Q,X]}],"inset-shadow-color":[{"inset-shadow":c()}],"ring-w":[{ring:C()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:c()}],"ring-offset-w":[{"ring-offset":[m,V]}],"ring-offset-color":[{"ring-offset":c()}],"inset-ring-w":[{"inset-ring":C()}],"inset-ring-color":[{"inset-ring":c()}],"text-shadow":[{"text-shadow":["none",z,Q,X]}],"text-shadow-color":[{"text-shadow":c()}],opacity:[{opacity:[m,i,a]}],"mix-blend":[{"mix-blend":[...fe(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":fe()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[m]}],"mask-image-linear-from-pos":[{"mask-linear-from":x()}],"mask-image-linear-to-pos":[{"mask-linear-to":x()}],"mask-image-linear-from-color":[{"mask-linear-from":c()}],"mask-image-linear-to-color":[{"mask-linear-to":c()}],"mask-image-t-from-pos":[{"mask-t-from":x()}],"mask-image-t-to-pos":[{"mask-t-to":x()}],"mask-image-t-from-color":[{"mask-t-from":c()}],"mask-image-t-to-color":[{"mask-t-to":c()}],"mask-image-r-from-pos":[{"mask-r-from":x()}],"mask-image-r-to-pos":[{"mask-r-to":x()}],"mask-image-r-from-color":[{"mask-r-from":c()}],"mask-image-r-to-color":[{"mask-r-to":c()}],"mask-image-b-from-pos":[{"mask-b-from":x()}],"mask-image-b-to-pos":[{"mask-b-to":x()}],"mask-image-b-from-color":[{"mask-b-from":c()}],"mask-image-b-to-color":[{"mask-b-to":c()}],"mask-image-l-from-pos":[{"mask-l-from":x()}],"mask-image-l-to-pos":[{"mask-l-to":x()}],"mask-image-l-from-color":[{"mask-l-from":c()}],"mask-image-l-to-color":[{"mask-l-to":c()}],"mask-image-x-from-pos":[{"mask-x-from":x()}],"mask-image-x-to-pos":[{"mask-x-to":x()}],"mask-image-x-from-color":[{"mask-x-from":c()}],"mask-image-x-to-color":[{"mask-x-to":c()}],"mask-image-y-from-pos":[{"mask-y-from":x()}],"mask-image-y-to-pos":[{"mask-y-to":x()}],"mask-image-y-from-color":[{"mask-y-from":c()}],"mask-image-y-to-color":[{"mask-y-to":c()}],"mask-image-radial":[{"mask-radial":[i,a]}],"mask-image-radial-from-pos":[{"mask-radial-from":x()}],"mask-image-radial-to-pos":[{"mask-radial-to":x()}],"mask-image-radial-from-color":[{"mask-radial-from":c()}],"mask-image-radial-to-color":[{"mask-radial-to":c()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":j()}],"mask-image-conic-pos":[{"mask-conic":[m]}],"mask-image-conic-from-pos":[{"mask-conic-from":x()}],"mask-image-conic-to-pos":[{"mask-conic-to":x()}],"mask-image-conic-from-color":[{"mask-conic-from":c()}],"mask-image-conic-to-color":[{"mask-conic-to":c()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ue()}],"mask-repeat":[{mask:me()}],"mask-size":[{mask:pe()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",i,a]}],filter:[{filter:["","none",i,a]}],blur:[{blur:ge()}],brightness:[{brightness:[m,i,a]}],contrast:[{contrast:[m,i,a]}],"drop-shadow":[{"drop-shadow":["","none",P,Q,X]}],"drop-shadow-color":[{"drop-shadow":c()}],grayscale:[{grayscale:["",m,i,a]}],"hue-rotate":[{"hue-rotate":[m,i,a]}],invert:[{invert:["",m,i,a]}],saturate:[{saturate:[m,i,a]}],sepia:[{sepia:["",m,i,a]}],"backdrop-filter":[{"backdrop-filter":["","none",i,a]}],"backdrop-blur":[{"backdrop-blur":ge()}],"backdrop-brightness":[{"backdrop-brightness":[m,i,a]}],"backdrop-contrast":[{"backdrop-contrast":[m,i,a]}],"backdrop-grayscale":[{"backdrop-grayscale":["",m,i,a]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[m,i,a]}],"backdrop-invert":[{"backdrop-invert":["",m,i,a]}],"backdrop-opacity":[{"backdrop-opacity":[m,i,a]}],"backdrop-saturate":[{"backdrop-saturate":[m,i,a]}],"backdrop-sepia":[{"backdrop-sepia":["",m,i,a]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":u()}],"border-spacing-x":[{"border-spacing-x":u()}],"border-spacing-y":[{"border-spacing-y":u()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",i,a]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[m,"initial",i,a]}],ease:[{ease:["linear","initial",E,i,a]}],delay:[{delay:[m,i,a]}],animate:[{animate:["none",D,i,a]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[k,i,a]}],"perspective-origin":[{"perspective-origin":T()}],rotate:[{rotate:K()}],"rotate-x":[{"rotate-x":K()}],"rotate-y":[{"rotate-y":K()}],"rotate-z":[{"rotate-z":K()}],scale:[{scale:Z()}],"scale-x":[{"scale-x":Z()}],"scale-y":[{"scale-y":Z()}],"scale-z":[{"scale-z":Z()}],"scale-3d":["scale-3d"],skew:[{skew:re()}],"skew-x":[{"skew-x":re()}],"skew-y":[{"skew-y":re()}],transform:[{transform:[i,a,"","none","gpu","cpu"]}],"transform-origin":[{origin:T()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:J()}],"translate-x":[{"translate-x":J()}],"translate-y":[{"translate-y":J()}],"translate-z":[{"translate-z":J()}],"translate-none":["translate-none"],accent:[{accent:c()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:c()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",i,a]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":u()}],"scroll-mx":[{"scroll-mx":u()}],"scroll-my":[{"scroll-my":u()}],"scroll-ms":[{"scroll-ms":u()}],"scroll-me":[{"scroll-me":u()}],"scroll-mt":[{"scroll-mt":u()}],"scroll-mr":[{"scroll-mr":u()}],"scroll-mb":[{"scroll-mb":u()}],"scroll-ml":[{"scroll-ml":u()}],"scroll-p":[{"scroll-p":u()}],"scroll-px":[{"scroll-px":u()}],"scroll-py":[{"scroll-py":u()}],"scroll-ps":[{"scroll-ps":u()}],"scroll-pe":[{"scroll-pe":u()}],"scroll-pt":[{"scroll-pt":u()}],"scroll-pr":[{"scroll-pr":u()}],"scroll-pb":[{"scroll-pb":u()}],"scroll-pl":[{"scroll-pl":u()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",i,a]}],fill:[{fill:["none",...c()]}],"stroke-w":[{stroke:[m,F,V,te]}],stroke:[{stroke:["none",...c()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},wr=er(yr);function vr(...e){return wr(Se(e))}function we(e,o){if(typeof e=="function")return e(o);e!=null&&(e.current=o)}function Te(...e){return o=>{let r=!1;const t=e.map(n=>{const s=we(n,o);return!r&&typeof s=="function"&&(r=!0),s});if(r)return()=>{for(let n=0;n<t.length;n++){const s=t[n];typeof s=="function"?s():we(e[n],null)}}}}function Nr(...e){return b.useCallback(Te(...e),e)}function kr(e){const o=zr(e),r=b.forwardRef((t,n)=>{const{children:s,...l}=t,p=b.Children.toArray(s),d=p.find(Sr);if(d){const f=d.props.children,g=p.map(y=>y===d?b.Children.count(f)>1?b.Children.only(null):b.isValidElement(f)?f.props.children:null:y);return U.jsx(o,{...l,ref:n,children:b.isValidElement(f)?b.cloneElement(f,void 0,g):null})}return U.jsx(o,{...l,ref:n,children:s})});return r.displayName=`${e}.Slot`,r}var Cr=kr("Slot");function zr(e){const o=b.forwardRef((r,t)=>{const{children:n,...s}=r;if(b.isValidElement(n)){const l=Rr(n),p=Ar(s,n.props);return n.type!==b.Fragment&&(p.ref=t?Te(t,l):l),b.cloneElement(n,p)}return b.Children.count(n)>1?b.Children.only(null):null});return o.displayName=`${e}.SlotClone`,o}var Le=Symbol("radix.slottable");function Gr(e){const o=({children:r})=>U.jsx(U.Fragment,{children:r});return o.displayName=`${e}.Slottable`,o.__radixId=Le,o}function Sr(e){return b.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Le}function Ar(e,o){const r={...o};for(const t in o){const n=e[t],s=o[t];/^on[A-Z]/.test(t)?n&&s?r[t]=(...p)=>{const d=s(...p);return n(...p),d}:n&&(r[t]=n):t==="style"?r[t]={...n,...s}:t==="className"&&(r[t]=[n,s].filter(Boolean).join(" "))}return{...e,...r}}function Rr(e){let o=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=o&&"isReactWarning"in o&&o.isReactWarning;return r?e.ref:(o=Object.getOwnPropertyDescriptor(e,"ref")?.get,r=o&&"isReactWarning"in o&&o.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}const ve=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,ke=Se,Mr=(e,o)=>r=>{var t;if(o?.variants==null)return ke(e,r?.class,r?.className);const{variants:n,defaultVariants:s}=o,l=Object.keys(n).map(f=>{const g=r?.[f],y=s?.[f];if(g===null)return null;const z=ve(g)||ve(y);return n[f][z]}),p=r&&Object.entries(r).reduce((f,g)=>{let[y,z]=g;return z===void 0||(f[y]=z),f},{}),d=o==null||(t=o.compoundVariants)===null||t===void 0?void 0:t.reduce((f,g)=>{let{class:y,className:z,...P}=g;return Object.entries(P).every(v=>{let[k,S]=v;return Array.isArray(S)?S.includes({...s,...p}[k]):{...s,...p}[k]===S})?[...f,y,z]:f},[]);return ke(e,l,d,r?.class,r?.className)},Ir=Mr("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function Vr({className:e,variant:o,size:r,asChild:t=!1,...n}){const s=t?Cr:"button";return U.jsx(s,{"data-slot":"button",className:vr(Ir({variant:o,size:r,className:e})),...n})}export{Vr as B,Cr as S,Er as a,Mr as b,vr as c,kr as d,Gr as e,Te as f,Nr as u};
