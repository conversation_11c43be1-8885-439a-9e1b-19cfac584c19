# Query Optimization & N+1 Problem Resolution

## Deskripsi

Sistem optimasi query untuk mendeteksi dan mengatasi masalah N+1 query, mengimplementasikan eager loading strategies, dan menambahkan query result caching untuk operasi yang mahal.

## Komponen Utama

### 1. QueryOptimizationService

Service utama untuk optimasi query dan deteksi N+1 problems:

```php
use App\Services\QueryOptimizationService;

// Start N+1 monitoring
QueryOptimizationService::startN1Monitoring();

// Your application code here...

// Stop monitoring and get results
$problems = QueryOptimizationService::stopN1Monitoring();

// Cache expensive queries
$result = QueryOptimizationService::cacheQuery('expensive_query', function () {
    return ExpensiveModel::with('relations')->get();
});
```

### 2. OptimizedQueries Trait

Trait untuk model yang menyediakan optimasi query:

```php
use App\Traits\OptimizedQueries;

class User extends Model
{
    use OptimizedQueries;
    
    // Default relationships to eager load
    protected array $defaultWith = ['role'];
    
    // Relationships for display
    protected function getDisplayRelations(): array
    {
        return ['role', 'role.permissions'];
    }
}
```

### 3. Artisan Commands

```bash
# Analyze queries for N+1 problems
php artisan query:optimize analyze

# Show optimization report
php artisan query:optimize report

# Clear optimization cache
php artisan query:optimize clear

# Run demo to see N+1 detection
php artisan query:optimize demo
```

## N+1 Problem Detection

### Automatic Detection

Sistem otomatis mendeteksi N+1 problems melalui middleware:

```php
// Konfigurasi di config/database.php
'detect_n1_problems' => env('DB_DETECT_N1_PROBLEMS', true),
'n1_threshold' => env('DB_N1_THRESHOLD', 5),
```

### Manual Analysis

```php
// Start monitoring
QueryOptimizationService::startN1Monitoring();

// Execute your code
$users = User::all();
foreach ($users as $user) {
    echo $user->role->nama_peran; // This causes N+1!
}

// Get analysis
$problems = QueryOptimizationService::stopN1Monitoring();
```

### Detection Criteria

N+1 problem terdeteksi jika:
- Query yang sama dieksekusi ≥ 5 kali (configurable)
- Frekuensi eksekusi tinggi (>2 query/detik)
- Pattern SELECT dengan WHERE clause

## Eager Loading Strategies

### 1. Default Eager Loading

```php
class User extends Model
{
    use OptimizedQueries;
    
    // Automatically load these relationships
    protected array $defaultWith = ['role'];
}

// All queries will automatically include role
$users = User::all(); // Includes role relationship
```

### 2. Conditional Eager Loading

```php
// Load relationships based on context
$users = User::withOptimized(['role', 'permissions'])->get();

// For display purposes
$users = User::forDisplay()->get();

// For API responses
$users = User::forApi()->get();
```

### 3. Nested Eager Loading

```php
// Load nested relationships efficiently
$appointments = Appointment::with([
    'patient',
    'doctor.role',
    'medicalRecords.treatments'
])->get();
```

### 4. Batch Loading

```php
// Load related models in batches
$users = User::all();
User::batchLoadRelated($users, 'role');
```

## Query Caching Strategies

### 1. Basic Query Caching

```php
// Cache expensive queries
$activeUsers = QueryOptimizationService::cacheQuery('active_users', function () {
    return User::active()->with('role')->get();
}, 3600); // Cache for 1 hour
```

### 2. Model-Level Caching

```php
// Using trait methods
$count = User::cachedCount('active_users_count');
$total = Patient::cachedSum('estimasi_biaya', 'total_estimates');
```

### 3. Automatic Cache Invalidation

```php
class User extends Model
{
    use OptimizedQueries;
    
    protected static function booted(): void
    {
        static::saved(function ($user) {
            $user->invalidateCache('user_*');
        });
    }
}
```

## Optimization Patterns

### 1. Efficient Searching

```php
// Bad: No indexes, inefficient LIKE
User::where('nama_depan', 'LIKE', "%{$term}%")->get();

// Good: Use indexed columns first
User::searchOptimized($term)->get();

// Implementation in model
public function scopeSearchOptimized($query, string $term)
{
    return $query->where(function ($q) use ($term) {
        $q->where('username', $term)      // Exact match on indexed column
          ->orWhere('email', $term)       // Exact match on indexed column
          ->orWhere('nama_depan', 'LIKE', "%{$term}%"); // LIKE as last resort
    });
}
```

### 2. Efficient Pagination

```php
// Bad: No eager loading
$users = User::paginate(20);

// Good: Optimized pagination with relationships
$users = User::paginateOptimized(20, ['role']);
```

### 3. Selective Column Loading

```php
// Bad: Load all columns
$users = User::with('role')->get();

// Good: Load only needed columns
$users = User::select('id', 'username', 'email')
             ->with('role:id_peran,nama_peran')
             ->get();
```

### 4. Efficient Exists Checks

```php
// Bad: Load full model
$exists = User::where('email', $email)->first() !== null;

// Good: Use exists query
$exists = User::where('email', $email)->existsOptimized();
```

## Performance Monitoring

### 1. N+1 Detection Middleware

Middleware otomatis mendeteksi N+1 problems:

```php
// Logs detected problems
Log::warning('N+1 query problem detected', [
    'url' => $request->fullUrl(),
    'query_count' => $problem['count'],
    'severity' => $problem['severity'],
    'suggestions' => $problem['suggestions']
]);
```

### 2. Query Performance Tracking

```php
// Monitor slow queries
QueryPerformanceService::startMonitoring();

// Get performance statistics
$stats = QueryPerformanceService::getQueryStats();
```

### 3. Cache Hit Ratio Monitoring

```bash
# Check cache statistics
php artisan permission:cache stats
php artisan query:optimize report
```

## Best Practices

### 1. Model Design

```php
class Patient extends Model
{
    use OptimizedQueries;
    
    // Define default relationships
    protected array $defaultWith = [];
    
    // Define display relationships
    protected function getDisplayRelations(): array
    {
        return ['appointments.doctor'];
    }
    
    // Define searchable columns
    protected function getSearchableColumns(): array
    {
        return ['nama_depan', 'nama_belakang', 'nik', 'no_telepon'];
    }
}
```

### 2. Controller Optimization

```php
class PatientController extends Controller
{
    public function index()
    {
        // Good: Optimized loading
        $patients = Patient::forDisplay()
                          ->active()
                          ->paginateOptimized(20);
        
        return response()->json($patients);
    }
    
    public function search(Request $request)
    {
        // Good: Efficient search
        $patients = Patient::searchOptimized($request->term)
                          ->active()
                          ->limit(50)
                          ->get();
        
        return response()->json($patients);
    }
}
```

### 3. Relationship Loading

```php
// Bad: N+1 problem
$appointments = Appointment::all();
foreach ($appointments as $appointment) {
    echo $appointment->patient->nama_depan; // N+1!
    echo $appointment->doctor->nama_depan;  // N+1!
}

// Good: Eager loading
$appointments = Appointment::with(['patient', 'doctor'])->get();
foreach ($appointments as $appointment) {
    echo $appointment->patient->nama_depan; // No additional query
    echo $appointment->doctor->nama_depan;  // No additional query
}
```

### 4. Caching Strategy

```php
// Cache expensive aggregations
public function getDashboardStats()
{
    return QueryOptimizationService::cacheQuery('dashboard_stats', function () {
        return [
            'total_patients' => Patient::active()->count(),
            'total_appointments' => Appointment::today()->count(),
            'pending_appointments' => Appointment::pending()->count(),
        ];
    }, 300); // Cache for 5 minutes
}
```

## Troubleshooting

### 1. Identifying N+1 Problems

```bash
# Enable N+1 detection
DB_DETECT_N1_PROBLEMS=true

# Check logs
tail -f storage/logs/laravel.log | grep "N+1"

# Run analysis
php artisan query:optimize analyze
php artisan query:optimize report
```

### 2. Performance Issues

```bash
# Check slow queries
php artisan query:performance slow

# Analyze indexes
php artisan db:analyze-indexes missing

# Check cache hit ratio
php artisan permission:cache stats
```

### 3. Memory Issues

```php
// Use chunking for large datasets
User::chunk(1000, function ($users) {
    foreach ($users as $user) {
        // Process user
    }
});

// Use cursor for memory-efficient iteration
foreach (User::cursor() as $user) {
    // Process user with minimal memory usage
}
```

### 4. Cache Issues

```bash
# Clear optimization cache
php artisan query:optimize clear

# Clear all caches
php artisan cache:clear
php artisan permission:cache clear
```

## Configuration

### Environment Variables

```env
# Query monitoring
DB_MONITOR_QUERIES=true
DB_SLOW_QUERY_THRESHOLD=100

# N+1 detection
DB_DETECT_N1_PROBLEMS=true
DB_N1_THRESHOLD=5

# Caching
CACHE_STORE=redis
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
```

### Database Configuration

```php
// config/database.php
'monitor_queries' => env('DB_MONITOR_QUERIES', false),
'slow_query_threshold' => env('DB_SLOW_QUERY_THRESHOLD', 100),
'detect_n1_problems' => env('DB_DETECT_N1_PROBLEMS', false),
'n1_threshold' => env('DB_N1_THRESHOLD', 5),
```
