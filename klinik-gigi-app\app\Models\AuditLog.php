<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class AuditLog extends Model
{
    protected $table = 'audit_logs';

    protected $fillable = [
        'user_id',
        'action',
        'model_type',
        'model_id',
        'old_values',
        'new_values',
        'ip_address',
        'user_agent',
        'session_id',
        'additional_data',
        'severity',
        'created_at'
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'additional_data' => 'array',
        'created_at' => 'datetime'
    ];

    public $timestamps = false;

    // Severity levels
    const SEVERITY_LOW = 'low';
    const SEVERITY_MEDIUM = 'medium';
    const SEVERITY_HIGH = 'high';
    const SEVERITY_CRITICAL = 'critical';

    // Common actions
    const ACTION_LOGIN = 'login';
    const ACTION_LOGIN_FAILED = 'login_failed';
    const ACTION_LOGOUT = 'logout';
    const ACTION_PASSWORD_CHANGED = 'password_changed';
    const ACTION_ACCOUNT_LOCKED = 'account_locked';
    const ACTION_ACCOUNT_UNLOCKED = 'account_unlocked';
    const ACTION_USER_CREATED = 'user_created';
    const ACTION_USER_UPDATED = 'user_updated';
    const ACTION_USER_DELETED = 'user_deleted';
    const ACTION_ROLE_ASSIGNED = 'role_assigned';
    const ACTION_PERMISSION_CHANGED = 'permission_changed';
    const ACTION_DATA_ACCESSED = 'data_accessed';
    const ACTION_DATA_EXPORTED = 'data_exported';

    /**
     * Relationship to User
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the auditable model
     */
    public function auditable()
    {
        if ($this->model_type && $this->model_id) {
            return $this->model_type::find($this->model_id);
        }
        return null;
    }

    /**
     * Log an audit event
     */
    public static function log(
        string $action,
        ?string $modelType = null,
        ?int $modelId = null,
        ?array $oldValues = null,
        ?array $newValues = null,
        ?array $additionalData = null,
        string $severity = self::SEVERITY_LOW,
        ?int $userId = null
    ): self {
        return self::create([
            'user_id' => $userId ?? Auth::id(),
            'action' => $action,
            'model_type' => $modelType,
            'model_id' => $modelId,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'session_id' => session()->getId(),
            'additional_data' => $additionalData,
            'severity' => $severity,
            'created_at' => now()
        ]);
    }

    /**
     * Log authentication events
     */
    public static function logAuth(string $action, ?User $user = null, ?array $additionalData = null): self
    {
        $severity = match($action) {
            self::ACTION_LOGIN_FAILED, self::ACTION_ACCOUNT_LOCKED => self::SEVERITY_HIGH,
            self::ACTION_LOGIN, self::ACTION_LOGOUT => self::SEVERITY_MEDIUM,
            default => self::SEVERITY_LOW
        };

        return self::log(
            action: $action,
            modelType: $user ? User::class : null,
            modelId: $user?->getKey(),
            additionalData: $additionalData,
            severity: $severity,
            userId: $user?->getKey()
        );
    }

    /**
     * Log user management events
     */
    public static function logUserManagement(
        string $action,
        User $targetUser,
        ?array $oldValues = null,
        ?array $newValues = null,
        ?array $additionalData = null
    ): self {
        $severity = match($action) {
            self::ACTION_USER_DELETED, self::ACTION_ROLE_ASSIGNED => self::SEVERITY_HIGH,
            self::ACTION_USER_CREATED, self::ACTION_USER_UPDATED => self::SEVERITY_MEDIUM,
            default => self::SEVERITY_LOW
        };

        return self::log(
            action: $action,
            modelType: User::class,
            modelId: $targetUser->getKey(),
            oldValues: $oldValues,
            newValues: $newValues,
            additionalData: $additionalData,
            severity: $severity
        );
    }

    /**
     * Log data access events
     */
    public static function logDataAccess(
        string $modelType,
        int $modelId,
        string $action = self::ACTION_DATA_ACCESSED,
        ?array $additionalData = null
    ): self {
        return self::log(
            action: $action,
            modelType: $modelType,
            modelId: $modelId,
            additionalData: $additionalData,
            severity: self::SEVERITY_LOW
        );
    }

    /**
     * Log permission changes
     */
    public static function logPermissionChange(
        User $user,
        ?array $oldPermissions = null,
        ?array $newPermissions = null,
        ?array $additionalData = null
    ): self {
        return self::log(
            action: self::ACTION_PERMISSION_CHANGED,
            modelType: User::class,
            modelId: $user->getKey(),
            oldValues: $oldPermissions,
            newValues: $newPermissions,
            additionalData: $additionalData,
            severity: self::SEVERITY_HIGH
        );
    }

    /**
     * Get formatted action description
     */
    public function getActionDescriptionAttribute(): string
    {
        $user = $this->user ? $this->user->full_name : 'System';
        
        return match($this->action) {
            self::ACTION_LOGIN => "{$user} berhasil login",
            self::ACTION_LOGIN_FAILED => "{$user} gagal login",
            self::ACTION_LOGOUT => "{$user} logout",
            self::ACTION_PASSWORD_CHANGED => "{$user} mengubah password",
            self::ACTION_ACCOUNT_LOCKED => "Akun {$user} dikunci",
            self::ACTION_ACCOUNT_UNLOCKED => "Akun {$user} dibuka",
            self::ACTION_USER_CREATED => "{$user} membuat user baru",
            self::ACTION_USER_UPDATED => "{$user} mengupdate user",
            self::ACTION_USER_DELETED => "{$user} menghapus user",
            self::ACTION_ROLE_ASSIGNED => "{$user} mengubah role user",
            self::ACTION_PERMISSION_CHANGED => "{$user} mengubah permission",
            self::ACTION_DATA_ACCESSED => "{$user} mengakses data",
            self::ACTION_DATA_EXPORTED => "{$user} mengekspor data",
            default => "{$user} melakukan {$this->action}"
        };
    }

    /**
     * Get severity color for UI
     */
    public function getSeverityColorAttribute(): string
    {
        return match($this->severity) {
            self::SEVERITY_CRITICAL => 'red',
            self::SEVERITY_HIGH => 'orange',
            self::SEVERITY_MEDIUM => 'yellow',
            self::SEVERITY_LOW => 'green',
            default => 'gray'
        };
    }

    /**
     * Scope for filtering by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope for filtering by user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for filtering by action
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope for filtering by severity
     */
    public function scopeBySeverity($query, $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * Clean up old audit logs (for data retention)
     */
    public static function cleanup(int $daysToKeep = 365): int
    {
        $cutoffDate = now()->subDays($daysToKeep);
        return self::where('created_at', '<', $cutoffDate)->delete();
    }
}
