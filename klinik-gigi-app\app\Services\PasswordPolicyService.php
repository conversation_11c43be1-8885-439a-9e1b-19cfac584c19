<?php

namespace App\Services;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;

class PasswordPolicyService
{
    // Password policy configuration
    const MIN_LENGTH = 12;
    const MAX_LENGTH = 128;
    const MIN_UPPERCASE = 1;
    const MIN_LOWERCASE = 1;
    const MIN_NUMBERS = 1;
    const MIN_SYMBOLS = 1;
    const PASSWORD_HISTORY_COUNT = 5;
    const PASSWORD_EXPIRY_DAYS = 90;
    const COMMON_PASSWORDS_FILE = 'common-passwords.txt';

    /**
     * Validate password against policy
     */
    public static function validatePassword(string $password, ?User $user = null): array
    {
        $errors = [];

        // Length validation
        if (strlen($password) < self::MIN_LENGTH) {
            $errors[] = "Password minimal " . self::MIN_LENGTH . " karakter";
        }

        if (strlen($password) > self::MAX_LENGTH) {
            $errors[] = "Password maksimal " . self::MAX_LENGTH . " karakter";
        }

        // Character composition validation
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = "Password harus mengandung minimal " . self::MIN_UPPERCASE . " huruf besar";
        }

        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = "Password harus mengandung minimal " . self::MIN_LOWERCASE . " huruf kecil";
        }

        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = "Password harus mengandung minimal " . self::MIN_NUMBERS . " angka";
        }

        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = "Password harus mengandung minimal " . self::MIN_SYMBOLS . " simbol khusus";
        }

        // Check for common patterns
        if (self::containsCommonPatterns($password)) {
            $errors[] = "Password tidak boleh mengandung pola umum (123, abc, qwerty, dll)";
        }

        // Check against common passwords
        if (self::isCommonPassword($password)) {
            $errors[] = "Password terlalu umum, gunakan password yang lebih unik";
        }

        // User-specific validations
        if ($user) {
            // Check if password contains user information
            if (self::containsUserInfo($password, $user)) {
                $errors[] = "Password tidak boleh mengandung informasi pribadi (nama, email, username)";
            }

            // Check password history
            if (self::isPasswordReused($password, $user)) {
                $errors[] = "Password tidak boleh sama dengan " . self::PASSWORD_HISTORY_COUNT . " password sebelumnya";
            }
        }

        return $errors;
    }

    /**
     * Check password strength and return score
     */
    public static function getPasswordStrength(string $password): array
    {
        $score = 0;
        $feedback = [];

        // Length scoring
        if (strlen($password) >= 12) $score += 25;
        elseif (strlen($password) >= 8) $score += 15;
        else $feedback[] = "Gunakan minimal 12 karakter";

        // Character variety scoring
        if (preg_match('/[A-Z]/', $password)) $score += 15;
        else $feedback[] = "Tambahkan huruf besar";

        if (preg_match('/[a-z]/', $password)) $score += 15;
        else $feedback[] = "Tambahkan huruf kecil";

        if (preg_match('/[0-9]/', $password)) $score += 15;
        else $feedback[] = "Tambahkan angka";

        if (preg_match('/[^A-Za-z0-9]/', $password)) $score += 15;
        else $feedback[] = "Tambahkan simbol khusus";

        // Bonus points for extra complexity
        if (strlen($password) >= 16) $score += 10;
        if (preg_match_all('/[A-Z]/', $password) >= 2) $score += 5;
        if (preg_match_all('/[0-9]/', $password) >= 2) $score += 5;
        if (preg_match_all('/[^A-Za-z0-9]/', $password) >= 2) $score += 5;

        // Determine strength level
        if ($score >= 90) $level = 'very_strong';
        elseif ($score >= 70) $level = 'strong';
        elseif ($score >= 50) $level = 'medium';
        elseif ($score >= 30) $level = 'weak';
        else $level = 'very_weak';

        return [
            'score' => min($score, 100),
            'level' => $level,
            'feedback' => $feedback
        ];
    }

    /**
     * Check if password contains common patterns
     */
    private static function containsCommonPatterns(string $password): bool
    {
        $commonPatterns = [
            '/123+/',           // Sequential numbers
            '/abc+/i',          // Sequential letters
            '/qwerty/i',        // Keyboard patterns
            '/asdf/i',          // Keyboard patterns
            '/password/i',      // Common words
            '/admin/i',         // Common words
            '/login/i',         // Common words
            '/(.)\1{2,}/',      // Repeated characters (aaa, 111)
        ];

        foreach ($commonPatterns as $pattern) {
            if (preg_match($pattern, $password)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if password is in common passwords list
     */
    private static function isCommonPassword(string $password): bool
    {
        $commonPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey',
            'dragon', 'master', 'shadow', 'superman', 'michael',
            'football', 'baseball', 'liverpool', 'jordan', 'harley'
        ];

        return in_array(strtolower($password), $commonPasswords);
    }

    /**
     * Check if password contains user information
     */
    private static function containsUserInfo(string $password, User $user): bool
    {
        $userInfo = [
            strtolower($user->username ?? ''),
            strtolower($user->nama_depan ?? ''),
            strtolower($user->nama_belakang ?? ''),
            strtolower($user->email ?? ''),
            strtolower(explode('@', $user->email ?? '')[0] ?? ''),
        ];

        $passwordLower = strtolower($password);

        foreach ($userInfo as $info) {
            if (!empty($info) && strlen($info) >= 3) {
                if (strpos($passwordLower, $info) !== false) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check if password was used recently
     */
    private static function isPasswordReused(string $password, User $user): bool
    {
        $passwordHistories = $user->passwordHistories()
            ->orderBy('created_at', 'desc')
            ->limit(self::PASSWORD_HISTORY_COUNT)
            ->get();

        foreach ($passwordHistories as $history) {
            if (Hash::check($password, $history->password_hash)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if password is expired
     */
    public static function isPasswordExpired(User $user): bool
    {
        if (!$user->password_changed_at) {
            return true; // Force password change for users without timestamp
        }

        return $user->password_changed_at->addDays(self::PASSWORD_EXPIRY_DAYS)->isPast();
    }

    /**
     * Get days until password expires
     */
    public static function getDaysUntilExpiry(User $user): int
    {
        if (!$user->password_changed_at) {
            return 0;
        }

        $expiryDate = $user->password_changed_at->addDays(self::PASSWORD_EXPIRY_DAYS);
        return max(0, $expiryDate->diffInDays(now(), false));
    }

    /**
     * Generate secure random password
     */
    public static function generateSecurePassword(int $length = 16): string
    {
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

        $password = '';
        
        // Ensure at least one character from each category
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        $password .= $symbols[random_int(0, strlen($symbols) - 1)];

        // Fill the rest randomly
        $allChars = $uppercase . $lowercase . $numbers . $symbols;
        for ($i = 4; $i < $length; $i++) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }

        // Shuffle the password
        return str_shuffle($password);
    }
}
