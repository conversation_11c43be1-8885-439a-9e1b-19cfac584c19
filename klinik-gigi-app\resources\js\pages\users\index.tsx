import { Head, Link, router } from '@inertiajs/react';
import { Plus, Edit, Trash2, Eye } from 'lucide-react';
import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import UserModal from '@/components/user-modal';
import DeleteUserModal from '@/components/delete-user-modal';

interface Role {
    id_peran: number;
    nama_peran: string;
    deskripsi?: string;
}

interface User {
    id: number;
    username: string;
    nama_depan: string;
    nama_belakang: string;
    email: string;
    no_telepon?: string;
    aktif: boolean;
    id_peran: number;
    nomor_str?: string;
    nomor_sip?: string;
    expired_str?: string;
    expired_sip?: string;
    role?: {
        id_peran: number;
        nama_peran: string;
    };
    login_terakhir?: string;
    created_at: string;
}

interface UsersIndexProps {
    users: {
        data: User[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    roles: Role[];
}

export default function UsersIndex({ users, roles }: UsersIndexProps) {
    const [userModalOpen, setUserModalOpen] = useState(false);
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const [selectedUser, setSelectedUser] = useState<User | null>(null);

    const handleAddUser = () => {
        setSelectedUser(null);
        setUserModalOpen(true);
    };

    const handleEditUser = (user: User) => {
        setSelectedUser(user);
        setUserModalOpen(true);
    };

    const handleDeleteUser = (user: User) => {
        setSelectedUser(user);
        setDeleteModalOpen(true);
    };

    const handleViewUser = (user: User) => {
        router.visit(route('users.show', user.id));
    };

    const refreshData = () => {
        router.reload({ only: ['users'] });
    };

    return (
        <AppLayout>
            <Head title="Manajemen Pengguna" />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Manajemen Pengguna</h1>
                        <p className="text-muted-foreground">
                            Kelola pengguna sistem dan hak akses mereka
                        </p>
                    </div>
                    <Button onClick={handleAddUser}>
                        <Plus className="mr-2 h-4 w-4" />
                        Tambah Pengguna
                    </Button>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Daftar Pengguna</CardTitle>
                        <CardDescription>
                            Total {users.total} pengguna terdaftar
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Username</TableHead>
                                    <TableHead>Nama Lengkap</TableHead>
                                    <TableHead>Email</TableHead>
                                    <TableHead>Role</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Login Terakhir</TableHead>
                                    <TableHead className="text-right">Aksi</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {users.data.map((user) => (
                                    <TableRow key={user.id}>
                                        <TableCell className="font-medium">
                                            {user.username}
                                        </TableCell>
                                        <TableCell>
                                            {user.nama_depan} {user.nama_belakang}
                                        </TableCell>
                                        <TableCell>{user.email}</TableCell>
                                        <TableCell>
                                            <Badge variant="secondary">
                                                {user.role?.nama_peran || 'Tidak ada role'}
                                            </Badge>
                                        </TableCell>
                                        <TableCell>
                                            <Badge
                                                variant={user.aktif ? "default" : "destructive"}
                                            >
                                                {user.aktif ? 'Aktif' : 'Tidak Aktif'}
                                            </Badge>
                                        </TableCell>
                                        <TableCell>
                                            {user.login_terakhir
                                                ? new Date(user.login_terakhir).toLocaleDateString('id-ID')
                                                : 'Belum pernah login'
                                            }
                                        </TableCell>
                                        <TableCell className="text-right">
                                            <div className="flex items-center justify-end gap-2">
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => handleViewUser(user)}
                                                    title="Lihat Detail"
                                                >
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => handleEditUser(user)}
                                                    title="Edit Pengguna"
                                                >
                                                    <Edit className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="text-destructive hover:text-destructive"
                                                    onClick={() => handleDeleteUser(user)}
                                                    title="Hapus Pengguna"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>

                        {users.data.length === 0 && (
                            <div className="text-center py-8">
                                <p className="text-muted-foreground">Tidak ada pengguna ditemukan</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* User Modal */}
            <UserModal
                isOpen={userModalOpen}
                onClose={() => setUserModalOpen(false)}
                user={selectedUser}
                roles={roles}
                onSuccess={refreshData}
            />

            {/* Delete User Modal */}
            <DeleteUserModal
                isOpen={deleteModalOpen}
                onClose={() => setDeleteModalOpen(false)}
                user={selectedUser}
                onSuccess={refreshData}
            />
        </AppLayout>
    );
}
