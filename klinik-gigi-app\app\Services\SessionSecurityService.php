<?php

namespace App\Services;

use App\Models\AuditLog;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

class SessionSecurityService
{
    // Session timeout in minutes
    const SESSION_TIMEOUT = 30;
    const ADMIN_SESSION_TIMEOUT = 15;
    const CONCURRENT_SESSION_LIMIT = 3;

    /**
     * Check if session should timeout based on user role
     */
    public static function getSessionTimeout(?User $user = null): int
    {
        if (!$user) {
            $user = Auth::user();
        }

        if (!$user) {
            return self::SESSION_TIMEOUT;
        }

        // Shorter timeout for admin roles
        if ($user->hasRole('Super Admin') || $user->hasRole('Admin')) {
            return self::ADMIN_SESSION_TIMEOUT;
        }

        return self::SESSION_TIMEOUT;
    }

    /**
     * Check if session has timed out
     */
    public static function isSessionExpired(): bool
    {
        $lastActivity = Session::get('last_activity');

        // If no last_activity is set, initialize it now (fresh login)
        if (!$lastActivity) {
            self::updateSessionActivity();
            return false; // Fresh session, not expired
        }

        $timeout = self::getSessionTimeout() * 60; // Convert to seconds
        return (time() - $lastActivity) > $timeout;
    }

    /**
     * Update session activity timestamp
     */
    public static function updateSessionActivity(): void
    {
        Session::put('last_activity', time());
        Session::put('user_agent_hash', md5(request()->userAgent()));
        Session::put('ip_address', request()->ip());
    }

    /**
     * Validate session security
     */
    public static function validateSessionSecurity(): bool
    {
        // Check if session has expired
        if (self::isSessionExpired()) {
            self::terminateSession('session_timeout');
            return false;
        }

        // Check if user agent has changed (potential session hijacking)
        $currentUserAgentHash = md5(request()->userAgent());
        $sessionUserAgentHash = Session::get('user_agent_hash');

        if ($sessionUserAgentHash && $currentUserAgentHash !== $sessionUserAgentHash) {
            self::terminateSession('user_agent_mismatch');
            return false;
        }

        // Check if IP address has changed (optional - can be disabled for mobile users)
        if (config('session.strict_ip_check', false)) {
            $currentIp = request()->ip();
            $sessionIp = Session::get('ip_address');

            if ($sessionIp && $currentIp !== $sessionIp) {
                self::terminateSession('ip_address_mismatch');
                return false;
            }
        }

        return true;
    }

    /**
     * Terminate session with reason
     */
    public static function terminateSession(string $reason): void
    {
        $user = Auth::user();

        if ($user) {
            // Log session termination
            AuditLog::logAuth(AuditLog::ACTION_LOGOUT, $user, [
                'reason' => $reason,
                'forced' => true
            ]);
        }

        Auth::logout();
        Session::invalidate();
        Session::regenerateToken();
    }

    /**
     * Check concurrent session limit
     */
    public static function checkConcurrentSessions(User $user): bool
    {
        $activeSessions = self::getActiveSessionsCount($user);
        return $activeSessions < self::CONCURRENT_SESSION_LIMIT;
    }

    /**
     * Get active sessions count for user
     */
    public static function getActiveSessionsCount(User $user): int
    {
        return DB::table('sessions')
            ->where('user_id', $user->getKey())
            ->where('last_activity', '>', now()->subMinutes(self::getSessionTimeout($user))->timestamp)
            ->count();
    }

    /**
     * Get active sessions for user
     */
    public static function getActiveSessions(User $user): array
    {
        $sessions = DB::table('sessions')
            ->where('user_id', $user->getKey())
            ->where('last_activity', '>', now()->subMinutes(self::getSessionTimeout($user))->timestamp)
            ->orderBy('last_activity', 'desc')
            ->get();

        return $sessions->map(function ($session) {
            $payload = unserialize(base64_decode($session->payload));

            return [
                'id' => $session->id,
                'ip_address' => $session->ip_address,
                'user_agent' => $session->user_agent,
                'last_activity' => $session->last_activity,
                'is_current' => $session->id === Session::getId(),
                'location' => self::getLocationFromIp($session->ip_address),
                'device_info' => self::parseUserAgent($session->user_agent)
            ];
        })->toArray();
    }

    /**
     * Terminate specific session
     */
    public static function terminateSpecificSession(string $sessionId, User $user): bool
    {
        $deleted = DB::table('sessions')
            ->where('id', $sessionId)
            ->where('user_id', $user->getKey())
            ->delete();

        if ($deleted) {
            // Log session termination
            AuditLog::logAuth(AuditLog::ACTION_LOGOUT, $user, [
                'reason' => 'manual_termination',
                'session_id' => $sessionId,
                'terminated_by' => Auth::user()?->full_name ?? 'System'
            ]);
        }

        return $deleted > 0;
    }

    /**
     * Terminate all other sessions for user
     */
    public static function terminateOtherSessions(User $user): int
    {
        $currentSessionId = Session::getId();

        $deleted = DB::table('sessions')
            ->where('user_id', $user->getKey())
            ->where('id', '!=', $currentSessionId)
            ->delete();

        if ($deleted > 0) {
            // Log bulk session termination
            AuditLog::logAuth(AuditLog::ACTION_LOGOUT, $user, [
                'reason' => 'terminate_other_sessions',
                'sessions_terminated' => $deleted,
                'terminated_by' => Auth::user()?->full_name ?? 'System'
            ]);
        }

        return $deleted;
    }

    /**
     * Clean up expired sessions
     */
    public static function cleanupExpiredSessions(): int
    {
        $expiredTime = now()->subMinutes(self::SESSION_TIMEOUT)->timestamp;

        return DB::table('sessions')
            ->where('last_activity', '<', $expiredTime)
            ->delete();
    }

    /**
     * Get session warning time (5 minutes before timeout)
     */
    public static function getSessionWarningTime(): int
    {
        return max(5, self::getSessionTimeout() - 5);
    }

    /**
     * Check if session warning should be shown
     */
    public static function shouldShowSessionWarning(): bool
    {
        $lastActivity = Session::get('last_activity');

        if (!$lastActivity) {
            return false;
        }

        $warningTime = self::getSessionWarningTime() * 60;
        $timeSinceActivity = time() - $lastActivity;

        return $timeSinceActivity >= $warningTime;
    }

    /**
     * Get location from IP address (basic implementation)
     */
    private static function getLocationFromIp(string $ip): string
    {
        // This is a basic implementation
        // In production, you might want to use a service like MaxMind GeoIP
        if ($ip === '127.0.0.1' || $ip === '::1') {
            return 'Local';
        }

        return 'Unknown Location';
    }

    /**
     * Parse user agent to get device info
     */
    private static function parseUserAgent(string $userAgent): array
    {
        // Basic user agent parsing
        $info = [
            'browser' => 'Unknown',
            'platform' => 'Unknown',
            'device' => 'Desktop'
        ];

        // Detect browser
        if (strpos($userAgent, 'Chrome') !== false) {
            $info['browser'] = 'Chrome';
        } elseif (strpos($userAgent, 'Firefox') !== false) {
            $info['browser'] = 'Firefox';
        } elseif (strpos($userAgent, 'Safari') !== false) {
            $info['browser'] = 'Safari';
        } elseif (strpos($userAgent, 'Edge') !== false) {
            $info['browser'] = 'Edge';
        }

        // Detect platform
        if (strpos($userAgent, 'Windows') !== false) {
            $info['platform'] = 'Windows';
        } elseif (strpos($userAgent, 'Mac') !== false) {
            $info['platform'] = 'macOS';
        } elseif (strpos($userAgent, 'Linux') !== false) {
            $info['platform'] = 'Linux';
        } elseif (strpos($userAgent, 'Android') !== false) {
            $info['platform'] = 'Android';
            $info['device'] = 'Mobile';
        } elseif (strpos($userAgent, 'iOS') !== false) {
            $info['platform'] = 'iOS';
            $info['device'] = 'Mobile';
        }

        return $info;
    }

    /**
     * Generate secure session token
     */
    public static function generateSecureToken(): string
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * Store session security metadata
     */
    public static function storeSessionMetadata(User $user): void
    {
        $metadata = [
            'login_time' => now()->timestamp,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'security_token' => self::generateSecureToken()
        ];

        Session::put('security_metadata', $metadata);

        // Store in cache for cross-session validation
        Cache::put(
            "session_metadata_{$user->getKey()}_{Session::getId()}",
            $metadata,
            now()->addMinutes(self::getSessionTimeout($user))
        );
    }
}
