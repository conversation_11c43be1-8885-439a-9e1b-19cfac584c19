<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class QueryPerformanceService
{
    const SLOW_QUERY_THRESHOLD = 100; // milliseconds
    const CACHE_KEY_PREFIX = 'query_performance:';
    const STATS_CACHE_TTL = 3600; // 1 hour

    /**
     * Start monitoring database queries
     */
    public static function startMonitoring(): void
    {
        try {
            DB::listen(function ($query) {
                try {
                    $executionTime = $query->time;

                    // Log slow queries
                    if ($executionTime > self::SLOW_QUERY_THRESHOLD) {
                        Log::warning('Slow query detected', [
                            'sql' => $query->sql,
                            'bindings' => $query->bindings,
                            'time' => $executionTime . 'ms',
                            'connection' => $query->connectionName
                        ]);

                        // Store slow query for analysis
                        self::recordSlowQuery($query->sql, $executionTime, $query->bindings);
                    }

                    // Record query statistics
                    self::recordQueryStats($query->sql, $executionTime);
                } catch (\Exception $e) {
                    // Log error but don't break the query execution
                    Log::error('QueryPerformanceService listener error: ' . $e->getMessage());
                }
            });
        } catch (\Exception $e) {
            // Log error but don't break the application
            Log::error('QueryPerformanceService startMonitoring error: ' . $e->getMessage());
        }
    }

    /**
     * Record slow query for analysis
     */
    protected static function recordSlowQuery(string $sql, float $time, array $bindings): void
    {
        $cacheKey = self::CACHE_KEY_PREFIX . 'slow_queries';
        $slowQueries = Cache::get($cacheKey, []);

        $queryHash = md5($sql);
        $slowQueries[$queryHash] = [
            'sql' => $sql,
            'time' => $time,
            'bindings' => $bindings,
            'count' => ($slowQueries[$queryHash]['count'] ?? 0) + 1,
            'last_seen' => now()->toDateTimeString(),
            'avg_time' => isset($slowQueries[$queryHash])
                ? (($slowQueries[$queryHash]['avg_time'] * $slowQueries[$queryHash]['count']) + $time) / ($slowQueries[$queryHash]['count'] + 1)
                : $time
        ];

        // Keep only last 100 slow queries
        if (count($slowQueries) > 100) {
            $slowQueries = array_slice($slowQueries, -100, 100, true);
        }

        Cache::put($cacheKey, $slowQueries, self::STATS_CACHE_TTL);
    }

    /**
     * Record general query statistics
     */
    protected static function recordQueryStats(string $sql, float $time): void
    {
        $cacheKey = self::CACHE_KEY_PREFIX . 'stats';
        $stats = Cache::get($cacheKey, [
            'total_queries' => 0,
            'total_time' => 0,
            'avg_time' => 0,
            'slow_queries' => 0,
            'query_types' => []
        ]);

        $stats['total_queries']++;
        $stats['total_time'] += $time;
        $stats['avg_time'] = $stats['total_time'] / $stats['total_queries'];

        if ($time > self::SLOW_QUERY_THRESHOLD) {
            $stats['slow_queries']++;
        }

        // Categorize query type
        $queryType = self::getQueryType($sql);
        $stats['query_types'][$queryType] = ($stats['query_types'][$queryType] ?? 0) + 1;

        Cache::put($cacheKey, $stats, self::STATS_CACHE_TTL);
    }

    /**
     * Get query type from SQL
     */
    protected static function getQueryType(string $sql): string
    {
        $sql = trim(strtoupper($sql));

        if (strpos($sql, 'SELECT') === 0) return 'SELECT';
        if (strpos($sql, 'INSERT') === 0) return 'INSERT';
        if (strpos($sql, 'UPDATE') === 0) return 'UPDATE';
        if (strpos($sql, 'DELETE') === 0) return 'DELETE';
        if (strpos($sql, 'CREATE') === 0) return 'CREATE';
        if (strpos($sql, 'ALTER') === 0) return 'ALTER';
        if (strpos($sql, 'DROP') === 0) return 'DROP';

        return 'OTHER';
    }

    /**
     * Get slow queries
     */
    public static function getSlowQueries(): array
    {
        $cacheKey = self::CACHE_KEY_PREFIX . 'slow_queries';
        return Cache::get($cacheKey, []);
    }

    /**
     * Get query statistics
     */
    public static function getQueryStats(): array
    {
        $cacheKey = self::CACHE_KEY_PREFIX . 'stats';
        return Cache::get($cacheKey, [
            'total_queries' => 0,
            'total_time' => 0,
            'avg_time' => 0,
            'slow_queries' => 0,
            'query_types' => []
        ]);
    }

    /**
     * Clear query statistics
     */
    public static function clearStats(): void
    {
        $keys = [
            self::CACHE_KEY_PREFIX . 'slow_queries',
            self::CACHE_KEY_PREFIX . 'stats'
        ];

        foreach ($keys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * Analyze query patterns and suggest optimizations
     */
    public static function analyzeQueries(): array
    {
        $slowQueries = self::getSlowQueries();
        $suggestions = [];

        foreach ($slowQueries as $hash => $query) {
            $sql = strtoupper($query['sql']);
            $suggestion = [];

            // Check for missing WHERE clause
            if (strpos($sql, 'SELECT') === 0 && strpos($sql, 'WHERE') === false) {
                $suggestion[] = 'Consider adding WHERE clause to limit results';
            }

            // Check for SELECT *
            if (strpos($sql, 'SELECT *') !== false) {
                $suggestion[] = 'Avoid SELECT *, specify only needed columns';
            }

            // Check for missing LIMIT
            if (strpos($sql, 'SELECT') === 0 && strpos($sql, 'LIMIT') === false) {
                $suggestion[] = 'Consider adding LIMIT clause for pagination';
            }

            // Check for complex JOINs
            $joinCount = substr_count($sql, 'JOIN');
            if ($joinCount > 3) {
                $suggestion[] = 'Complex query with multiple JOINs, consider query optimization';
            }

            // Check for subqueries
            if (strpos($sql, '(SELECT') !== false) {
                $suggestion[] = 'Subquery detected, consider using JOINs or EXISTS instead';
            }

            if (!empty($suggestion)) {
                $suggestions[$hash] = [
                    'sql' => $query['sql'],
                    'time' => $query['time'],
                    'count' => $query['count'],
                    'suggestions' => $suggestion
                ];
            }
        }

        return $suggestions;
    }

    /**
     * Get database index recommendations
     */
    public static function getIndexRecommendations(): array
    {
        $slowQueries = self::getSlowQueries();
        $recommendations = [];

        foreach ($slowQueries as $hash => $query) {
            $sql = $query['sql'];

            // Simple pattern matching for WHERE clauses
            if (preg_match_all('/WHERE\s+(\w+)\s*[=<>]/i', $sql, $matches)) {
                foreach ($matches[1] as $column) {
                    $table = self::extractTableFromQuery($sql);
                    if ($table) {
                        $key = $table . '.' . $column;
                        if (!isset($recommendations[$key])) {
                            $recommendations[$key] = [
                                'table' => $table,
                                'column' => $column,
                                'reason' => 'Frequently used in WHERE clause',
                                'queries' => []
                            ];
                        }
                        $recommendations[$key]['queries'][] = $sql;
                    }
                }
            }

            // Check for ORDER BY clauses
            if (preg_match_all('/ORDER\s+BY\s+(\w+)/i', $sql, $matches)) {
                foreach ($matches[1] as $column) {
                    $table = self::extractTableFromQuery($sql);
                    if ($table) {
                        $key = $table . '.' . $column . '_order';
                        if (!isset($recommendations[$key])) {
                            $recommendations[$key] = [
                                'table' => $table,
                                'column' => $column,
                                'reason' => 'Used in ORDER BY clause',
                                'queries' => []
                            ];
                        }
                        $recommendations[$key]['queries'][] = $sql;
                    }
                }
            }
        }

        return $recommendations;
    }

    /**
     * Extract table name from SQL query
     */
    protected static function extractTableFromQuery(string $sql): ?string
    {
        // Simple pattern matching - in production, use a proper SQL parser
        if (preg_match('/FROM\s+`?(\w+)`?/i', $sql, $matches)) {
            return $matches[1];
        }

        if (preg_match('/UPDATE\s+`?(\w+)`?/i', $sql, $matches)) {
            return $matches[1];
        }

        if (preg_match('/INSERT\s+INTO\s+`?(\w+)`?/i', $sql, $matches)) {
            return $matches[1];
        }

        return null;
    }

    /**
     * Generate performance report
     */
    public static function generateReport(): array
    {
        $stats = self::getQueryStats();
        $slowQueries = self::getSlowQueries();
        $suggestions = self::analyzeQueries();
        $indexRecommendations = self::getIndexRecommendations();

        return [
            'summary' => [
                'total_queries' => $stats['total_queries'],
                'avg_query_time' => round($stats['avg_time'], 2) . 'ms',
                'slow_queries_count' => $stats['slow_queries'],
                'slow_query_percentage' => $stats['total_queries'] > 0
                    ? round(($stats['slow_queries'] / $stats['total_queries']) * 100, 2) . '%'
                    : '0%'
            ],
            'query_types' => $stats['query_types'],
            'slow_queries' => array_slice($slowQueries, 0, 10), // Top 10 slow queries
            'optimization_suggestions' => count($suggestions),
            'index_recommendations' => count($indexRecommendations),
            'generated_at' => now()->toDateTimeString()
        ];
    }
}
