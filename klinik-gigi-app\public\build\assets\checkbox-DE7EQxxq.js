import{r as s,j as n}from"./app-EmUGGW4y.js";import{a as F,u as N,c as O}from"./button-B8QorGO4.js";import{c as H,P as G,u as K,a as j,d as U}from"./index-sju6-yWZ.js";import{P as R}from"./index-CrVQA8Zu.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],$=F("Check",X);function J(e){const r=s.useRef({value:e,previous:e});return s.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}var y="Checkbox",[Q,oe]=H(y),[V,P]=Q(y);function W(e){const{__scopeCheckbox:r,checked:i,children:l,defaultChecked:c,disabled:t,form:f,name:h,onCheckedChange:d,required:k,value:m="on",internal_do_not_use_render:u}=e,[p,v]=K({prop:i,defaultProp:c??!1,onChange:d,caller:y}),[x,C]=s.useState(null),[g,o]=s.useState(null),a=s.useRef(!1),_=x?!!f||!!x.closest("form"):!0,E={checked:p,disabled:t,setChecked:v,control:x,setControl:C,name:h,form:f,value:m,hasConsumerStoppedPropagationRef:a,required:k,defaultChecked:b(c)?!1:c,isFormControl:_,bubbleInput:g,setBubbleInput:o};return n.jsx(V,{scope:r,...E,children:Y(u)?u(E):l})}var S="CheckboxTrigger",w=s.forwardRef(({__scopeCheckbox:e,onKeyDown:r,onClick:i,...l},c)=>{const{control:t,value:f,disabled:h,checked:d,required:k,setControl:m,setChecked:u,hasConsumerStoppedPropagationRef:p,isFormControl:v,bubbleInput:x}=P(S,e),C=N(c,m),g=s.useRef(d);return s.useEffect(()=>{const o=t?.form;if(o){const a=()=>u(g.current);return o.addEventListener("reset",a),()=>o.removeEventListener("reset",a)}},[t,u]),n.jsx(R.button,{type:"button",role:"checkbox","aria-checked":b(d)?"mixed":d,"aria-required":k,"data-state":L(d),"data-disabled":h?"":void 0,disabled:h,value:f,...l,ref:C,onKeyDown:j(r,o=>{o.key==="Enter"&&o.preventDefault()}),onClick:j(i,o=>{u(a=>b(a)?!0:!a),x&&v&&(p.current=o.isPropagationStopped(),p.current||o.stopPropagation())})})});w.displayName=S;var B=s.forwardRef((e,r)=>{const{__scopeCheckbox:i,name:l,checked:c,defaultChecked:t,required:f,disabled:h,value:d,onCheckedChange:k,form:m,...u}=e;return n.jsx(W,{__scopeCheckbox:i,checked:c,defaultChecked:t,disabled:h,required:f,onCheckedChange:k,name:l,form:m,value:d,internal_do_not_use_render:({isFormControl:p})=>n.jsxs(n.Fragment,{children:[n.jsx(w,{...u,ref:r,__scopeCheckbox:i}),p&&n.jsx(A,{__scopeCheckbox:i})]})})});B.displayName=y;var M="CheckboxIndicator",T=s.forwardRef((e,r)=>{const{__scopeCheckbox:i,forceMount:l,...c}=e,t=P(M,i);return n.jsx(G,{present:l||b(t.checked)||t.checked===!0,children:n.jsx(R.span,{"data-state":L(t.checked),"data-disabled":t.disabled?"":void 0,...c,ref:r,style:{pointerEvents:"none",...e.style}})})});T.displayName=M;var q="CheckboxBubbleInput",A=s.forwardRef(({__scopeCheckbox:e,...r},i)=>{const{control:l,hasConsumerStoppedPropagationRef:c,checked:t,defaultChecked:f,required:h,disabled:d,name:k,value:m,form:u,bubbleInput:p,setBubbleInput:v}=P(q,e),x=N(i,v),C=J(t),g=U(l);s.useEffect(()=>{const a=p;if(!a)return;const _=window.HTMLInputElement.prototype,I=Object.getOwnPropertyDescriptor(_,"checked").set,z=!c.current;if(C!==t&&I){const D=new Event("click",{bubbles:z});a.indeterminate=b(t),I.call(a,b(t)?!1:t),a.dispatchEvent(D)}},[p,C,t,c]);const o=s.useRef(b(t)?!1:t);return n.jsx(R.input,{type:"checkbox","aria-hidden":!0,defaultChecked:f??o.current,required:h,disabled:d,name:k,value:m,form:u,...r,tabIndex:-1,ref:x,style:{...r.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});A.displayName=q;function Y(e){return typeof e=="function"}function b(e){return e==="indeterminate"}function L(e){return b(e)?"indeterminate":e?"checked":"unchecked"}function ne({className:e,...r}){return n.jsx(B,{"data-slot":"checkbox",className:O("peer border-input data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:n.jsx(T,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:n.jsx($,{className:"size-3.5"})})})}export{ne as C,$ as a,J as u};
