<?php

namespace App\Providers;

use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use App\Observers\PermissionCacheObserver;
use App\Observers\RoleCacheObserver;
use App\Observers\UserCacheObserver;
use App\Services\PermissionCacheService;
use Illuminate\Support\ServiceProvider;

class PermissionCacheServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register PermissionCacheService as singleton
        $this->app->singleton(PermissionCacheService::class, function ($app) {
            return new PermissionCacheService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register observers
        Permission::observe(PermissionCacheObserver::class);
        Role::observe(RoleCacheObserver::class);
        User::observe(UserCacheObserver::class);
    }
}
