import { useForm } from '@inertiajs/react';
import { FormEventHandler, useEffect, useRef } from 'react';
import { AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import InputError from '@/components/input-error';

interface User {
    id: number;
    username: string;
    nama_depan: string;
    nama_belakang: string;
    email: string;
}

interface DeleteUserModalProps {
    isOpen: boolean;
    onClose: () => void;
    user?: User | null;
    onSuccess?: () => void;
}

type DeleteFormData = {
    password: string;
};

export default function DeleteUserModal({ isOpen, onClose, user, onSuccess }: DeleteUserModalProps) {
    const passwordInput = useRef<HTMLInputElement>(null);
    
    const { data, setData, delete: destroy, processing, errors, reset, clearErrors } = useForm<DeleteFormData>({
        password: '',
    });

    // Reset form when modal opens/closes
    useEffect(() => {
        if (isOpen) {
            reset();
            clearErrors();
            // Focus password input after modal animation
            setTimeout(() => {
                passwordInput.current?.focus();
            }, 100);
        }
    }, [isOpen]);

    const handleSubmit: FormEventHandler = (e) => {
        e.preventDefault();
        
        if (!user?.id) return;

        destroy(route('users.destroy', user.id), {
            preserveScroll: true,
            data: {
                password: data.password,
            },
            onSuccess: () => {
                onClose();
                onSuccess?.();
                reset();
            },
            onError: () => {
                passwordInput.current?.focus();
            }
        });
    };

    const handleClose = () => {
        onClose();
        reset();
        clearErrors();
    };

    if (!user) return null;

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2 text-destructive">
                        <AlertTriangle className="h-5 w-5" />
                        Hapus Pengguna
                    </DialogTitle>
                    <DialogDescription>
                        Tindakan ini tidak dapat dibatalkan. Pengguna akan dihapus secara permanen dari sistem.
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                    <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                            <strong>Peringatan:</strong> Anda akan menghapus pengguna berikut:
                        </AlertDescription>
                    </Alert>

                    <div className="bg-muted p-4 rounded-lg space-y-2">
                        <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>
                                <span className="font-medium">Username:</span>
                                <p className="text-muted-foreground">{user.username}</p>
                            </div>
                            <div>
                                <span className="font-medium">Email:</span>
                                <p className="text-muted-foreground">{user.email}</p>
                            </div>
                            <div className="col-span-2">
                                <span className="font-medium">Nama Lengkap:</span>
                                <p className="text-muted-foreground">
                                    {user.nama_depan} {user.nama_belakang}
                                </p>
                            </div>
                        </div>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="password">
                                Konfirmasi dengan Password Anda *
                            </Label>
                            <Input
                                ref={passwordInput}
                                id="password"
                                type="password"
                                value={data.password}
                                onChange={(e) => setData('password', e.target.value)}
                                placeholder="Masukkan password Anda"
                                disabled={processing}
                                required
                                autoComplete="current-password"
                            />
                            <InputError message={errors.password} />
                            <p className="text-xs text-muted-foreground">
                                Masukkan password akun Anda untuk mengkonfirmasi penghapusan.
                            </p>
                        </div>

                        <DialogFooter>
                            <Button
                                type="button"
                                variant="outline"
                                onClick={handleClose}
                                disabled={processing}
                            >
                                Batal
                            </Button>
                            <Button 
                                type="submit" 
                                variant="destructive"
                                disabled={processing || !data.password.trim()}
                            >
                                {processing ? 'Menghapus...' : 'Hapus Pengguna'}
                            </Button>
                        </DialogFooter>
                    </form>
                </div>
            </DialogContent>
        </Dialog>
    );
}
