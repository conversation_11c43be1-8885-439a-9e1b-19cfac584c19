<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class DatabaseIndexAnalyzer extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:analyze-indexes {action : Action to perform (analyze|missing|unused|stats)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Analyze database indexes for performance optimization';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'analyze':
                return $this->analyzeIndexes();
            case 'missing':
                return $this->findMissingIndexes();
            case 'unused':
                return $this->findUnusedIndexes();
            case 'stats':
                return $this->showIndexStats();
            default:
                $this->error("Invalid action: {$action}");
                $this->info('Available actions: analyze, missing, unused, stats');
                return 1;
        }
    }

    /**
     * Analyze all indexes in the database
     */
    protected function analyzeIndexes(): int
    {
        $this->info('Analyzing database indexes...');

        $tables = $this->getAllTables();
        $totalIndexes = 0;
        $indexData = [];

        foreach ($tables as $table) {
            $indexes = $this->getTableIndexes($table);
            $totalIndexes += count($indexes);

            if (!empty($indexes)) {
                $indexData[$table] = $indexes;
            }
        }

        $this->info("Found {$totalIndexes} indexes across " . count($tables) . " tables");

        // Display index information
        foreach ($indexData as $table => $indexes) {
            $this->line("\n<comment>Table: {$table}</comment>");

            $tableData = [];
            foreach ($indexes as $index) {
                $tableData[] = [
                    $index['name'],
                    $index['type'],
                    implode(', ', $index['columns']),
                    $index['unique'] ? 'Yes' : 'No',
                    $index['cardinality'] ?? 'N/A'
                ];
            }

            $this->table(
                ['Index Name', 'Type', 'Columns', 'Unique', 'Cardinality'],
                $tableData
            );
        }

        return 0;
    }

    /**
     * Find potentially missing indexes
     */
    protected function findMissingIndexes(): int
    {
        $this->info('Analyzing potentially missing indexes...');

        $recommendations = [
            'users' => [
                ['columns' => ['email', 'aktif'], 'reason' => 'Login queries with active status'],
                ['columns' => ['id_peran', 'aktif'], 'reason' => 'Role-based queries with active status'],
                ['columns' => ['login_terakhir'], 'reason' => 'Last login filtering'],
            ],
            'patients' => [
                ['columns' => ['nama_depan', 'nama_belakang'], 'reason' => 'Name search queries'],
                ['columns' => ['nik'], 'reason' => 'NIK lookup'],
                ['columns' => ['aktif', 'created_at'], 'reason' => 'Active patient listing'],
            ],
            'appointments' => [
                ['columns' => ['id_dokter', 'tanggal_janji'], 'reason' => 'Doctor schedule queries'],
                ['columns' => ['id_pasien', 'status'], 'reason' => 'Patient appointment status'],
                ['columns' => ['tanggal_janji', 'status'], 'reason' => 'Daily schedule filtering'],
            ],
            'medical_records' => [
                ['columns' => ['id_pasien', 'tanggal_kunjungan'], 'reason' => 'Patient medical history'],
                ['columns' => ['id_dokter', 'created_at'], 'reason' => 'Doctor activity tracking'],
            ],
            'audit_logs' => [
                ['columns' => ['user_id', 'created_at'], 'reason' => 'User activity tracking'],
                ['columns' => ['action', 'created_at'], 'reason' => 'Action-based audit queries'],
                ['columns' => ['model_type', 'model_id'], 'reason' => 'Model-specific audit trails'],
            ]
        ];

        foreach ($recommendations as $table => $indexes) {
            if (!Schema::hasTable($table)) {
                continue;
            }

            $this->line("\n<comment>Table: {$table}</comment>");

            $existingIndexes = $this->getTableIndexes($table);
            $existingColumns = [];

            foreach ($existingIndexes as $index) {
                $existingColumns[] = implode(',', $index['columns']);
            }

            $tableData = [];
            foreach ($indexes as $index) {
                $columnString = implode(',', $index['columns']);
                $exists = in_array($columnString, $existingColumns);

                $tableData[] = [
                    implode(', ', $index['columns']),
                    $index['reason'],
                    $exists ? 'Exists' : 'Missing'
                ];
            }

            $this->table(
                ['Columns', 'Reason', 'Status'],
                $tableData
            );
        }

        return 0;
    }

    /**
     * Find potentially unused indexes
     */
    protected function findUnusedIndexes(): int
    {
        $this->info('Analyzing potentially unused indexes...');

        // This is a simplified analysis - in production, you'd want to use
        // database-specific tools like MySQL's performance_schema or PostgreSQL's pg_stat_user_indexes

        $tables = $this->getAllTables();

        foreach ($tables as $table) {
            $indexes = $this->getTableIndexes($table);

            if (empty($indexes)) {
                continue;
            }

            $this->line("\n<comment>Table: {$table}</comment>");

            $tableData = [];
            foreach ($indexes as $index) {
                // Skip primary keys and unique constraints
                if ($index['type'] === 'PRIMARY' || $index['unique']) {
                    continue;
                }

                $tableData[] = [
                    $index['name'],
                    implode(', ', $index['columns']),
                    'Manual review needed'
                ];
            }

            if (!empty($tableData)) {
                $this->table(
                    ['Index Name', 'Columns', 'Usage Analysis'],
                    $tableData
                );
            } else {
                $this->line('No potentially unused indexes found.');
            }
        }

        $this->warn('Note: This is a basic analysis. Use database-specific tools for detailed usage statistics.');

        return 0;
    }

    /**
     * Show index statistics
     */
    protected function showIndexStats(): int
    {
        $this->info('Database Index Statistics:');

        $tables = $this->getAllTables();
        $totalTables = count($tables);
        $totalIndexes = 0;
        $indexTypes = [];

        foreach ($tables as $table) {
            $indexes = $this->getTableIndexes($table);
            $totalIndexes += count($indexes);

            foreach ($indexes as $index) {
                $type = $index['type'];
                $indexTypes[$type] = ($indexTypes[$type] ?? 0) + 1;
            }
        }

        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Tables', $totalTables],
                ['Total Indexes', $totalIndexes],
                ['Average Indexes per Table', round($totalIndexes / $totalTables, 2)],
            ]
        );

        $this->line("\n<comment>Index Types:</comment>");
        $typeData = [];
        foreach ($indexTypes as $type => $count) {
            $typeData[] = [$type, $count];
        }

        $this->table(['Type', 'Count'], $typeData);

        return 0;
    }

    /**
     * Get all tables in the database
     */
    protected function getAllTables(): array
    {
        $connection = DB::connection();
        $database = $connection->getDatabaseName();

        if ($connection->getDriverName() === 'mysql') {
            $tables = DB::select("SELECT table_name FROM information_schema.tables WHERE table_schema = ?", [$database]);
            return array_map(fn($table) => $table->table_name, $tables);
        } elseif ($connection->getDriverName() === 'sqlite') {
            $tables = DB::select("SELECT name as table_name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
            return array_map(fn($table) => $table->table_name, $tables);
        }

        return [];
    }

    /**
     * Get indexes for a specific table
     */
    protected function getTableIndexes(string $table): array
    {
        $connection = DB::connection();

        try {
            if ($connection->getDriverName() === 'mysql') {
                $indexes = DB::select("SHOW INDEX FROM `{$table}`");
                return $this->formatMysqlIndexes($indexes);
            } elseif ($connection->getDriverName() === 'sqlite') {
                $indexes = DB::select("PRAGMA index_list('{$table}')");
                return $this->formatSqliteIndexes($table, $indexes);
            }
        } catch (\Exception $e) {
            // Table might not exist
            return [];
        }

        return [];
    }

    /**
     * Format MySQL index information
     */
    protected function formatMysqlIndexes(array $indexes): array
    {
        $formatted = [];
        $grouped = [];

        foreach ($indexes as $index) {
            $name = $index->Key_name;
            if (!isset($grouped[$name])) {
                $grouped[$name] = [
                    'name' => $name,
                    'type' => $name === 'PRIMARY' ? 'PRIMARY' : 'INDEX',
                    'unique' => $index->Non_unique == 0,
                    'columns' => [],
                    'cardinality' => $index->Cardinality
                ];
            }
            $grouped[$name]['columns'][] = $index->Column_name;
        }

        return array_values($grouped);
    }

    /**
     * Format SQLite index information
     */
    protected function formatSqliteIndexes(string $table, array $indexes): array
    {
        $formatted = [];

        foreach ($indexes as $index) {
            $indexInfo = DB::select("PRAGMA index_info('{$index->name}')");
            $columns = [];

            foreach ($indexInfo as $info) {
                $columns[] = $info->name;
            }

            $formatted[] = [
                'name' => $index->name,
                'type' => 'INDEX',
                'unique' => $index->unique == 1,
                'columns' => $columns,
                'cardinality' => null
            ];
        }

        return $formatted;
    }
}
