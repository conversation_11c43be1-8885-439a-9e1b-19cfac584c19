<?php

namespace App\Providers;

use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use App\Observers\PermissionCacheObserver;
use App\Observers\RoleCacheObserver;
use App\Observers\UserCacheObserver;
use App\Services\PermissionCacheService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register PermissionCacheService as singleton
        $this->app->singleton(PermissionCacheService::class, function ($app) {
            return new PermissionCacheService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register observers for cache invalidation
        Permission::observe(PermissionCacheObserver::class);
        Role::observe(RoleCacheObserver::class);
        User::observe(UserCacheObserver::class);
    }
}
