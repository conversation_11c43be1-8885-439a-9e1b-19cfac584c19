import{r as i,j as x,t as y}from"./app-EmUGGW4y.js";import{u as A}from"./button-B8QorGO4.js";function U(e,t){const n=i.createContext(t),r=c=>{const{children:u,...s}=c,a=i.useMemo(()=>s,Object.values(s));return x.jsx(n.Provider,{value:a,children:u})};r.displayName=e+"Provider";function o(c){const u=i.useContext(n);if(u)return u;if(t!==void 0)return t;throw new Error(`\`${c}\` must be used within \`${e}\``)}return[r,o]}function z(e,t=[]){let n=[];function r(c,u){const s=i.createContext(u),a=n.length;n=[...n,u];const f=d=>{const{scope:p,children:m,...v}=d,S=p?.[e]?.[a]||s,g=i.useMemo(()=>v,Object.values(v));return x.jsx(S.Provider,{value:g,children:m})};f.displayName=c+"Provider";function l(d,p){const m=p?.[e]?.[a]||s,v=i.useContext(m);if(v)return v;if(u!==void 0)return u;throw new Error(`\`${d}\` must be used within \`${c}\``)}return[f,l]}const o=()=>{const c=n.map(u=>i.createContext(u));return function(s){const a=s?.[e]||c;return i.useMemo(()=>({[`__scope${e}`]:{...s,[e]:a}}),[s,a])}};return o.scopeName=e,[r,E(o,...t)]}function E(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(c){const u=r.reduce((s,{useScope:a,scopeName:f})=>{const d=a(c)[`__scope${f}`];return{...s,...d}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:u}),[u])}};return n.scopeName=t.scopeName,n}function _(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e?.(o),n===!1||!o.defaultPrevented)return t?.(o)}}var N=globalThis?.document?i.useLayoutEffect:()=>{},b=y[" useInsertionEffect ".trim().toString()]||N;function $({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,c,u]=w({defaultProp:t,onChange:n}),s=e!==void 0,a=s?e:o;{const l=i.useRef(e!==void 0);i.useEffect(()=>{const d=l.current;d!==s&&console.warn(`${r} is changing from ${d?"controlled":"uncontrolled"} to ${s?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),l.current=s},[s,r])}const f=i.useCallback(l=>{if(s){const d=C(l)?l(e):l;d!==e&&u.current?.(d)}else c(l)},[s,e,c,u]);return[a,f]}function w({defaultProp:e,onChange:t}){const[n,r]=i.useState(e),o=i.useRef(n),c=i.useRef(t);return b(()=>{c.current=t},[t]),i.useEffect(()=>{o.current!==n&&(c.current?.(n),o.current=n)},[n,o]),[n,r,c]}function C(e){return typeof e=="function"}function j(e){const[t,n]=i.useState(void 0);return N(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const c=o[0];let u,s;if("borderBoxSize"in c){const a=c.borderBoxSize,f=Array.isArray(a)?a[0]:a;u=f.inlineSize,s=f.blockSize}else u=e.offsetWidth,s=e.offsetHeight;n({width:u,height:s})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}function P(e,t){return i.useReducer((n,r)=>t[n][r]??n,e)}var M=e=>{const{present:t,children:n}=e,r=R(t),o=typeof n=="function"?n({present:r.isPresent}):i.Children.only(n),c=A(r.ref,O(o));return typeof n=="function"||r.isPresent?i.cloneElement(o,{ref:c}):null};M.displayName="Presence";function R(e){const[t,n]=i.useState(),r=i.useRef(null),o=i.useRef(e),c=i.useRef("none"),u=e?"mounted":"unmounted",[s,a]=P(u,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return i.useEffect(()=>{const f=h(r.current);c.current=s==="mounted"?f:"none"},[s]),N(()=>{const f=r.current,l=o.current;if(l!==e){const p=c.current,m=h(f);e?a("MOUNT"):m==="none"||f?.display==="none"?a("UNMOUNT"):a(l&&p!==m?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,a]),N(()=>{if(t){let f;const l=t.ownerDocument.defaultView??window,d=m=>{const S=h(r.current).includes(m.animationName);if(m.target===t&&S&&(a("ANIMATION_END"),!o.current)){const g=t.style.animationFillMode;t.style.animationFillMode="forwards",f=l.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=g)})}},p=m=>{m.target===t&&(c.current=h(r.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{l.clearTimeout(f),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else a("ANIMATION_END")},[t,a]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:i.useCallback(f=>{r.current=f?getComputedStyle(f):null,n(f)},[])}}function h(e){return e?.animationName||"none"}function O(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}export{M as P,_ as a,N as b,z as c,j as d,U as e,$ as u};
