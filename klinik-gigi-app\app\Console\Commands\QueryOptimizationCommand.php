<?php

namespace App\Console\Commands;

use App\Services\QueryOptimizationService;
use Illuminate\Console\Command;

class QueryOptimizationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'query:optimize {action : Action to perform (analyze|report|clear|demo)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Analyze and optimize database queries, detect N+1 problems';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'analyze':
                return $this->analyzeQueries();
            case 'report':
                return $this->showReport();
            case 'clear':
                return $this->clearCache();
            case 'demo':
                return $this->runDemo();
            default:
                $this->error("Invalid action: {$action}");
                $this->info('Available actions: analyze, report, clear, demo');
                return 1;
        }
    }

    /**
     * Analyze queries for N+1 problems
     */
    protected function analyzeQueries(): int
    {
        $this->info('Starting N+1 query analysis...');
        $this->info('This will monitor queries for potential N+1 problems.');
        $this->info('Run some application operations and then check the report.');
        
        QueryOptimizationService::startN1Monitoring();
        
        $this->info('N+1 monitoring started. Run your application operations now.');
        $this->info('When finished, run: php artisan query:optimize report');
        
        return 0;
    }

    /**
     * Show optimization report
     */
    protected function showReport(): int
    {
        $report = QueryOptimizationService::generateOptimizationReport();
        
        $this->info('Query Optimization Report');
        $this->line('Generated at: ' . $report['generated_at']);
        
        // Summary
        $this->line("\n<comment>Summary:</comment>");
        $this->table(
            ['Metric', 'Value'],
            [
                ['N+1 Problems Found', $report['summary']['n_plus_one_problems']],
                ['High Severity Problems', $report['summary']['high_severity_problems']],
                ['Total Wasted Queries', $report['summary']['total_wasted_queries']],
                ['Total Wasted Time', number_format($report['summary']['total_wasted_time'], 2) . 'ms'],
            ]
        );
        
        // N+1 Problems
        if (!empty($report['problems'])) {
            $this->line("\n<comment>N+1 Problems Detected:</comment>");
            
            foreach (array_slice($report['problems'], 0, 10) as $index => $problem) {
                $this->line("\n<error>Problem #" . ($index + 1) . " (Severity: {$problem['severity']}/100)</error>");
                $this->line("Query Count: {$problem['count']}");
                $this->line("Total Time: " . number_format($problem['total_time'], 2) . 'ms');
                $this->line("Average Time: " . number_format($problem['avg_time'], 2) . 'ms');
                $this->line("SQL: " . substr($problem['sql'], 0, 100) . '...');
                
                $this->line("<info>Suggestions:</info>");
                foreach ($problem['suggestions'] as $suggestion) {
                    $this->line("  - {$suggestion}");
                }
            }
        } else {
            $this->info("\nNo N+1 problems detected!");
        }
        
        // Recommendations
        $this->line("\n<comment>General Optimization Recommendations:</comment>");
        foreach ($report['recommendations'] as $category => $recommendation) {
            $this->line("\n<info>{$recommendation['title']}:</info>");
            $this->line($recommendation['description']);
            
            if (!empty($recommendation['examples'])) {
                $this->line("Examples:");
                foreach ($recommendation['examples'] as $code => $description) {
                    $this->line("  • {$description}");
                    $this->line("    <comment>{$code}</comment>");
                }
            }
        }
        
        return 0;
    }

    /**
     * Clear optimization cache
     */
    protected function clearCache(): int
    {
        if ($this->confirm('Are you sure you want to clear all query optimization cache?')) {
            QueryOptimizationService::clearOptimizationCache();
            $this->info('Query optimization cache cleared.');
        } else {
            $this->info('Operation cancelled.');
        }
        
        return 0;
    }

    /**
     * Run demo to show N+1 problems
     */
    protected function runDemo(): int
    {
        $this->info('Running N+1 problem demonstration...');
        
        // Start monitoring
        QueryOptimizationService::startN1Monitoring();
        
        // Simulate N+1 problem
        $this->simulateN1Problem();
        
        // Generate report
        $problems = QueryOptimizationService::stopN1Monitoring();
        
        if (!empty($problems)) {
            $this->line("\n<error>N+1 Problems Detected in Demo:</error>");
            
            foreach ($problems as $index => $problem) {
                $this->line("\nProblem #" . ($index + 1));
                $this->line("Query executed {$problem['count']} times");
                $this->line("Total time: " . number_format($problem['total_time'], 2) . 'ms');
                $this->line("SQL: " . substr($problem['sql'], 0, 80) . '...');
            }
            
            $this->line("\n<info>This demonstrates how N+1 problems are detected.</info>");
            $this->line("In real applications, use eager loading to prevent these issues.");
        } else {
            $this->info('No N+1 problems detected in demo.');
        }
        
        return 0;
    }

    /**
     * Simulate N+1 problem for demonstration
     */
    protected function simulateN1Problem(): void
    {
        // This would normally cause N+1 if we had data
        // In a real scenario, this would be:
        // $users = User::all();
        // foreach ($users as $user) {
        //     $user->role; // This would cause N+1
        // }
        
        // For demo purposes, we'll simulate multiple similar queries
        for ($i = 0; $i < 10; $i++) {
            \DB::select("SELECT * FROM users WHERE id = ?", [$i + 1]);
        }
        
        $this->line('Simulated N+1 problem with 10 similar queries.');
    }
}
