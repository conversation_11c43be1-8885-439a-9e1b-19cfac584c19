<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Permission extends Model
{
    protected $table = 'permissions';
    protected $primaryKey = 'id_hak_akses';

    public $timestamps = false;

    protected $fillable = [
        'id_peran',
        'modul',
        'baca',
        'tulis',
        'ubah',
        'hapus'
    ];

    protected $casts = [
        'baca' => 'boolean',
        'tulis' => 'boolean',
        'ubah' => 'boolean',
        'hapus' => 'boolean',
        'dibuat_pada' => 'datetime'
    ];

    /**
     * Relasi ke tabel roles
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class, 'id_peran', 'id_peran');
    }

    /**
     * Check if has specific permission
     */
    public function hasAction(string $action): bool
    {
        return match($action) {
            'read' => $this->baca,
            'create' => $this->tulis,
            'update' => $this->ubah,
            'delete' => $this->hapus,
            default => false
        };
    }

    /**
     * Get all allowed actions for this permission
     */
    public function getAllowedActions(): array
    {
        $actions = [];

        if ($this->baca) $actions[] = 'read';
        if ($this->tulis) $actions[] = 'create';
        if ($this->ubah) $actions[] = 'update';
        if ($this->hapus) $actions[] = 'delete';

        return $actions;
    }
}
