<?php

namespace App\Models;

use App\Traits\OptimizedQueries;
use App\Traits\SoftDeleteWithAudit;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Appointment extends Model
{
    use HasFactory, SoftDeleteWithAudit, OptimizedQueries;

    protected $table = 'appointments';
    protected $primaryKey = 'id_janji_temu';

    /**
     * Default relationships to eager load
     */
    protected array $defaultWith = ['patient', 'doctor'];

    protected $fillable = [
        'nomor_janji',
        'id_pasien',
        'id_dokter',
        'id_ruang_perawatan',
        'tanggal_janji',
        'durasi_menit',
        'status',
        'jenis_janji',
        'catatan',
        'keluhan_utama',
        'estimasi_biaya',
        'dibuat_oleh'
    ];

    protected $casts = [
        'tanggal_janji' => 'datetime',
        'estimasi_biaya' => 'decimal:2',
        'durasi_menit' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * Relationships
     */
    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class, 'id_pasien', 'id_pasien');
    }

    public function doctor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'id_dokter', 'id');
    }

    public function treatmentRoom(): BelongsTo
    {
        return $this->belongsTo(TreatmentRoom::class, 'id_ruang_perawatan', 'id_ruang');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'dibuat_oleh', 'id');
    }

    public function medicalRecord(): HasOne
    {
        return $this->hasOne(MedicalRecord::class, 'id_janji_temu', 'id_janji_temu');
    }

    /**
     * Scopes
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('jenis_janji', $type);
    }

    public function scopeByDoctor($query, int $doctorId)
    {
        return $query->where('id_dokter', $doctorId);
    }

    public function scopeByPatient($query, int $patientId)
    {
        return $query->where('id_pasien', $patientId);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('tanggal_janji', today());
    }

    public function scopeTomorrow($query)
    {
        return $query->whereDate('tanggal_janji', today()->addDay());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('tanggal_janji', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    public function scopeUpcoming($query)
    {
        return $query->where('tanggal_janji', '>=', now());
    }

    public function scopePending($query)
    {
        return $query->whereIn('status', ['dijadwalkan', 'dikonfirmasi']);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'selesai');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'dibatalkan');
    }

    /**
     * Optimized query methods
     */
    protected function getDisplayRelations(): array
    {
        return ['patient', 'doctor', 'treatmentRoom'];
    }

    protected function getApiRelations(): array
    {
        return ['patient', 'doctor'];
    }

    protected function getSearchableColumns(): array
    {
        return ['nomor_janji', 'keluhan_utama', 'catatan'];
    }

    /**
     * Get model dependencies for safe delete
     */
    protected function getDependencies(): array
    {
        return [
            'medicalRecord' => [
                'message' => 'Appointment has medical records. Cannot delete appointment with existing medical records.'
            ]
        ];
    }

    /**
     * Status management
     */
    public function confirm(): bool
    {
        return $this->update(['status' => 'dikonfirmasi']);
    }

    public function start(): bool
    {
        return $this->update(['status' => 'sedang_berlangsung']);
    }

    public function complete(): bool
    {
        return $this->update(['status' => 'selesai']);
    }

    public function cancel(string $reason = null): bool
    {
        $result = $this->update(['status' => 'dibatalkan']);
        
        if ($result && $reason) {
            // Log cancellation reason
            \App\Models\AuditLog::log(
                action: 'appointment_cancelled',
                modelType: get_class($this),
                modelId: $this->getKey(),
                additionalData: [
                    'cancellation_reason' => $reason,
                    'cancelled_by' => auth()->id(),
                    'cancelled_at' => now()
                ]
            );
        }
        
        return $result;
    }

    public function markNoShow(): bool
    {
        return $this->update(['status' => 'tidak_hadir']);
    }

    /**
     * Check if appointment can be modified
     */
    public function canBeModified(): bool
    {
        return in_array($this->status, ['dijadwalkan', 'dikonfirmasi']);
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['dijadwalkan', 'dikonfirmasi']);
    }

    public function canBeStarted(): bool
    {
        return $this->status === 'dikonfirmasi' && 
               $this->tanggal_janji->isToday() &&
               now()->gte($this->tanggal_janji->subMinutes(15));
    }

    /**
     * Time-based queries
     */
    public function scopeInTimeRange($query, $startTime, $endTime)
    {
        return $query->whereBetween('tanggal_janji', [$startTime, $endTime]);
    }

    public function scopeConflictsWith($query, $doctorId, $startTime, $endTime, $excludeId = null)
    {
        $query = $query->where('id_dokter', $doctorId)
                      ->where(function ($q) use ($startTime, $endTime) {
                          $q->whereBetween('tanggal_janji', [$startTime, $endTime])
                            ->orWhere(function ($q2) use ($startTime, $endTime) {
                                $q2->where('tanggal_janji', '<=', $startTime)
                                   ->whereRaw('DATE_ADD(tanggal_janji, INTERVAL durasi_menit MINUTE) > ?', [$startTime]);
                            });
                      })
                      ->whereNotIn('status', ['dibatalkan', 'tidak_hadir']);

        if ($excludeId) {
            $query->where('id_janji_temu', '!=', $excludeId);
        }

        return $query;
    }

    /**
     * Statistics methods
     */
    public static function getTodayStats(): array
    {
        return [
            'total' => static::today()->count(),
            'confirmed' => static::today()->byStatus('dikonfirmasi')->count(),
            'in_progress' => static::today()->byStatus('sedang_berlangsung')->count(),
            'completed' => static::today()->byStatus('selesai')->count(),
            'cancelled' => static::today()->byStatus('dibatalkan')->count(),
            'no_show' => static::today()->byStatus('tidak_hadir')->count(),
        ];
    }

    public static function getDoctorStats(int $doctorId, $startDate = null, $endDate = null): array
    {
        $query = static::byDoctor($doctorId);
        
        if ($startDate && $endDate) {
            $query->whereBetween('tanggal_janji', [$startDate, $endDate]);
        }

        return [
            'total' => $query->count(),
            'completed' => $query->byStatus('selesai')->count(),
            'cancelled' => $query->byStatus('dibatalkan')->count(),
            'no_show' => $query->byStatus('tidak_hadir')->count(),
        ];
    }

    /**
     * Model events
     */
    protected static function booted(): void
    {
        static::saved(function ($appointment) {
            $appointment->invalidateCache('appointment_*');
        });

        static::deleted(function ($appointment) {
            $appointment->invalidateCache('appointment_*');
        });
    }
}
