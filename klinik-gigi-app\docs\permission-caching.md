# Sistem Permission Caching

## Deskripsi

Sistem permission caching menggunakan Redis untuk menyimpan permission dan role user dalam cache, mengurangi query database dan meningkatkan performa aplikasi.

## Komponen Utama

### 1. PermissionCacheService

Service utama untuk mengelola cache permission:

```php
use App\Services\PermissionCacheService;

$permissionCache = app(PermissionCacheService::class);

// Cek permission user
$hasPermission = $permissionCache->hasPermission($userId, 'users', 'read');

// Dapatkan semua permission user
$permissions = $permissionCache->getUserPermissions($userId);

// Dapatkan role user
$role = $permissionCache->getUserRole($userId);
```

### 2. CachedPermissionMiddleware

Middleware yang menggunakan cached permissions:

```php
// Dalam routes
Route::middleware(['cached.permission:users,read'])->group(function () {
    Route::get('/users', [UserController::class, 'index']);
});

// Atau dalam controller constructor
public function __construct()
{
    $this->middleware('cached.permission:users,create')->only(['store']);
    $this->middleware('cached.permission:users,update')->only(['update']);
    $this->middleware('cached.permission:users,delete')->only(['destroy']);
}
```

### 3. Cache Observers

Observer otomatis untuk invalidasi cache:

- `PermissionCacheObserver`: Invalidasi cache ketika permission berubah
- `RoleCacheObserver`: Invalidasi cache ketika role berubah  
- `UserCacheObserver`: Invalidasi cache ketika user role berubah

## Konfigurasi Cache

### Cache Keys

- `user_permissions:{user_id}`: Permission user
- `role_permissions:{role_id}`: Permission role
- `user_role:{user_id}`: Role user

### Cache TTL

- User permissions: 1 jam (3600 detik)
- Role permissions: 2 jam (7200 detik)
- User roles: 1 jam (3600 detik)

## Artisan Commands

### Warm Up Cache

```bash
php artisan permission:cache warm-up
```

Mengisi cache dengan permission user aktif dan semua role.

### Clear Cache

```bash
php artisan permission:cache clear
```

Menghapus semua cache permission.

### Cache Statistics

```bash
php artisan permission:cache stats
```

Menampilkan statistik cache permission.

## API Methods

### PermissionCacheService Methods

#### getUserPermissions(int $userId): array

Mendapatkan semua permission user dari cache atau database.

**Return:**
```php
[
    'users' => [
        'baca' => true,
        'tulis' => true,
        'ubah' => false,
        'hapus' => false
    ],
    'patients' => [
        'baca' => true,
        'tulis' => false,
        'ubah' => true,
        'hapus' => false
    ]
]
```

#### hasPermission(int $userId, string $module, string $action): bool

Mengecek apakah user memiliki permission tertentu.

**Parameters:**
- `$userId`: ID user
- `$module`: Nama modul (contoh: 'users', 'patients')
- `$action`: Aksi ('read'/'baca', 'create'/'tulis', 'update'/'ubah', 'delete'/'hapus')

#### getUserRole(int $userId): ?array

Mendapatkan role user dari cache atau database.

**Return:**
```php
[
    'id_peran' => 1,
    'nama_peran' => 'Admin',
    'deskripsi' => 'Administrator sistem'
]
```

#### invalidateUserCache(int $userId): void

Menghapus cache permission dan role untuk user tertentu.

#### invalidateRoleCache(int $roleId): void

Menghapus cache permission role dan semua user yang memiliki role tersebut.

#### warmUpCache(): void

Mengisi cache dengan data user aktif dan semua role.

#### clearAllCache(): void

Menghapus semua cache permission.

#### getCacheStats(): array

Mendapatkan statistik cache.

**Return:**
```php
[
    'user_permissions' => 25,
    'role_permissions' => 5,
    'user_roles' => 25,
    'total_cached_items' => 55
]
```

## Cache Invalidation Strategy

### Automatic Invalidation

Cache akan otomatis di-invalidate ketika:

1. **Permission berubah**: Cache role dan semua user dengan role tersebut
2. **Role berubah**: Cache role dan semua user dengan role tersebut
3. **User role berubah**: Cache user yang bersangkutan

### Manual Invalidation

```php
$permissionCache = app(PermissionCacheService::class);

// Invalidate user tertentu
$permissionCache->invalidateUserCache($userId);

// Invalidate role tertentu
$permissionCache->invalidateRoleCache($roleId);

// Clear semua cache
$permissionCache->clearAllCache();
```

## Performance Benefits

### Before Caching

Setiap request permission check:
1. Query user table
2. Join dengan role table
3. Join dengan permissions table
4. Filter berdasarkan modul dan aksi

**Total queries per request**: 3-4 queries

### After Caching

Setiap request permission check:
1. Redis GET operation (sub-millisecond)

**Total queries per request**: 0 queries (dari cache)

### Benchmark Results

- **Database query**: ~5-10ms per permission check
- **Redis cache**: ~0.1-0.5ms per permission check
- **Performance improvement**: 10-100x faster

## Monitoring & Logging

### Cache Hit/Miss Logging

Service akan log ketika:
- Permission dimuat dari database (cache miss)
- Cache di-invalidate
- Cache warm-up dilakukan

### Monitoring Commands

```bash
# Lihat statistik cache
php artisan permission:cache stats

# Monitor Redis
redis-cli monitor

# Lihat log aplikasi
tail -f storage/logs/laravel.log | grep "permission"
```

## Best Practices

### 1. Cache Warming

Jalankan cache warm-up setelah deployment:

```bash
php artisan permission:cache warm-up
```

### 2. Monitoring

Monitor cache hit ratio dan performance:

```bash
# Cron job untuk warm-up harian
0 2 * * * cd /path/to/app && php artisan permission:cache warm-up
```

### 3. Error Handling

Service akan fallback ke database jika Redis tidak tersedia.

### 4. Testing

Selalu clear cache dalam testing:

```php
protected function setUp(): void
{
    parent::setUp();
    Cache::flush();
}
```

## Troubleshooting

### Cache Tidak Ter-update

```bash
# Clear cache manual
php artisan permission:cache clear

# Warm-up ulang
php artisan permission:cache warm-up
```

### Redis Connection Error

Service akan otomatis fallback ke database query jika Redis tidak tersedia.

### Performance Issues

```bash
# Cek statistik cache
php artisan permission:cache stats

# Monitor Redis memory usage
redis-cli info memory
```
