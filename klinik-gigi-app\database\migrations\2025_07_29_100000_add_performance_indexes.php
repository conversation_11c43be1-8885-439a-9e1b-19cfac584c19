<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Users table indexes
        Schema::table('users', function (Blueprint $table) {
            // Composite index untuk login dan authentication
            $table->index(['email', 'aktif'], 'idx_users_email_active');
            $table->index(['username', 'aktif'], 'idx_users_username_active');
            
            // Index untuk filtering dan searching
            $table->index(['aktif', 'login_terakhir'], 'idx_users_active_last_login');
            $table->index(['id_peran', 'aktif'], 'idx_users_role_active');
            $table->index(['expired_str'], 'idx_users_expired_str');
            $table->index(['expired_sip'], 'idx_users_expired_sip');
            
            // Index untuk security features
            $table->index(['locked_until'], 'idx_users_locked_until');
            $table->index(['password_changed_at'], 'idx_users_password_changed');
            
            // Full text search index untuk nama
            $table->index(['nama_depan', 'nama_belakang'], 'idx_users_fullname');
        });

        // Roles table indexes
        Schema::table('roles', function (Blueprint $table) {
            // Index sudah ada: nama_peran (unique)
            // Tambahan index untuk performance
            $table->index(['created_at'], 'idx_roles_created_at');
        });

        // Permissions table indexes  
        Schema::table('permissions', function (Blueprint $table) {
            // Index sudah ada: [id_peran, modul] (unique)
            // Tambahan index untuk query permission
            $table->index(['modul'], 'idx_permissions_module');
            $table->index(['baca', 'tulis', 'ubah', 'hapus'], 'idx_permissions_actions');
        });

        // Audit logs table indexes
        Schema::table('audit_logs', function (Blueprint $table) {
            // Index untuk filtering dan searching audit logs
            $table->index(['user_id', 'created_at'], 'idx_audit_user_date');
            $table->index(['action', 'created_at'], 'idx_audit_action_date');
            $table->index(['model_type', 'model_id'], 'idx_audit_model');
            $table->index(['severity', 'created_at'], 'idx_audit_severity_date');
            $table->index(['ip_address'], 'idx_audit_ip');
            $table->index(['session_id'], 'idx_audit_session');
            
            // Composite index untuk complex queries
            $table->index(['user_id', 'action', 'created_at'], 'idx_audit_user_action_date');
            $table->index(['model_type', 'action', 'created_at'], 'idx_audit_model_action_date');
        });

        // Password histories table indexes
        Schema::table('password_histories', function (Blueprint $table) {
            // Index untuk cleanup dan validation
            $table->index(['user_id', 'created_at'], 'idx_password_history_user_date');
        });

        // Sessions table indexes (jika menggunakan database session)
        if (Schema::hasTable('sessions')) {
            Schema::table('sessions', function (Blueprint $table) {
                // Index sudah ada: user_id, last_activity
                // Tambahan index untuk cleanup
                $table->index(['last_activity', 'user_id'], 'idx_sessions_activity_user');
            });
        }

        // Cache table indexes (jika menggunakan database cache)
        if (Schema::hasTable('cache')) {
            Schema::table('cache', function (Blueprint $table) {
                // Index untuk cache cleanup
                $table->index(['expiration'], 'idx_cache_expiration');
            });
        }

        // Jobs table indexes (untuk queue)
        if (Schema::hasTable('jobs')) {
            Schema::table('jobs', function (Blueprint $table) {
                // Index sudah ada di Laravel default
                // Tambahan index untuk monitoring
                $table->index(['queue', 'created_at'], 'idx_jobs_queue_created');
                $table->index(['attempts', 'reserved_at'], 'idx_jobs_attempts_reserved');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Users table indexes
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_users_email_active');
            $table->dropIndex('idx_users_username_active');
            $table->dropIndex('idx_users_active_last_login');
            $table->dropIndex('idx_users_role_active');
            $table->dropIndex('idx_users_expired_str');
            $table->dropIndex('idx_users_expired_sip');
            $table->dropIndex('idx_users_locked_until');
            $table->dropIndex('idx_users_password_changed');
            $table->dropIndex('idx_users_fullname');
        });

        // Roles table indexes
        Schema::table('roles', function (Blueprint $table) {
            $table->dropIndex('idx_roles_created_at');
        });

        // Permissions table indexes
        Schema::table('permissions', function (Blueprint $table) {
            $table->dropIndex('idx_permissions_module');
            $table->dropIndex('idx_permissions_actions');
        });

        // Audit logs table indexes
        Schema::table('audit_logs', function (Blueprint $table) {
            $table->dropIndex('idx_audit_user_date');
            $table->dropIndex('idx_audit_action_date');
            $table->dropIndex('idx_audit_model');
            $table->dropIndex('idx_audit_severity_date');
            $table->dropIndex('idx_audit_ip');
            $table->dropIndex('idx_audit_session');
            $table->dropIndex('idx_audit_user_action_date');
            $table->dropIndex('idx_audit_model_action_date');
        });

        // Password histories table indexes
        Schema::table('password_histories', function (Blueprint $table) {
            $table->dropIndex('idx_password_history_user_date');
        });

        // Sessions table indexes
        if (Schema::hasTable('sessions')) {
            Schema::table('sessions', function (Blueprint $table) {
                $table->dropIndex('idx_sessions_activity_user');
            });
        }

        // Cache table indexes
        if (Schema::hasTable('cache')) {
            Schema::table('cache', function (Blueprint $table) {
                $table->dropIndex('idx_cache_expiration');
            });
        }

        // Jobs table indexes
        if (Schema::hasTable('jobs')) {
            Schema::table('jobs', function (Blueprint $table) {
                $table->dropIndex('idx_jobs_queue_created');
                $table->dropIndex('idx_jobs_attempts_reserved');
            });
        }
    }
};
