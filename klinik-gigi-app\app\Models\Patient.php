<?php

namespace App\Models;

use App\Traits\OptimizedQueries;
use App\Traits\SoftDeleteWithAudit;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Patient extends Model
{
    use HasFactory, SoftDeleteWithAudit, OptimizedQueries;

    protected $table = 'patients';
    protected $primaryKey = 'id_pasien';

    /**
     * Default relationships to eager load
     */
    protected array $defaultWith = [];

    protected $fillable = [
        'nomor_pasien',
        'nama_depan',
        'nama_belakang',
        'tanggal_lahir',
        'jenis_kelamin',
        'no_telepon',
        'email',
        'alamat',
        'kota',
        'kode_pos',
        'provinsi',
        'kabupaten_kota',
        'kecamatan',
        'kelurahan_desa',
        'rt_rw',
        'nik',
        'nama_kontak_darurat',
        'telepon_kontak_darurat',
        'penyedia_asuransi',
        'nomor_asuransi',
        'nomor_bpjs',
        'kelas_bpjs',
        'status_kepesertaan_bpjs',
        'faskes_tingkat_1',
        'alergi',
        'riwayat_medis',
        'catatan',
        'aktif'
    ];

    protected $casts = [
        'tanggal_lahir' => 'date',
        'aktif' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * Relationships
     */
    public function appointments(): HasMany
    {
        return $this->hasMany(Appointment::class, 'id_pasien', 'id_pasien');
    }

    public function medicalRecords(): HasMany
    {
        return $this->hasMany(MedicalRecord::class, 'id_pasien', 'id_pasien');
    }

    /**
     * Accessors
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->nama_depan . ' ' . $this->nama_belakang);
    }

    public function getAgeAttribute(): int
    {
        return $this->tanggal_lahir ? $this->tanggal_lahir->age : 0;
    }

    /**
     * Scopes for optimization
     */
    public function scopeWithAppointments($query)
    {
        return $query->with(['appointments' => function ($q) {
            $q->with('doctor')->orderBy('tanggal_janji', 'desc');
        }]);
    }

    public function scopeWithLatestMedicalRecord($query)
    {
        return $query->with(['medicalRecords' => function ($q) {
            $q->with('doctor')->latest('tanggal_kunjungan')->limit(1);
        }]);
    }

    public function scopeByGender($query, string $gender)
    {
        return $query->where('jenis_kelamin', $gender);
    }

    public function scopeByAge($query, int $minAge = null, int $maxAge = null)
    {
        if ($minAge) {
            $query->whereDate('tanggal_lahir', '<=', now()->subYears($minAge));
        }

        if ($maxAge) {
            $query->whereDate('tanggal_lahir', '>=', now()->subYears($maxAge));
        }

        return $query;
    }

    public function scopeByBpjsStatus($query, string $status)
    {
        return $query->where('status_kepesertaan_bpjs', $status);
    }

    public function scopeWithInsurance($query)
    {
        return $query->where(function ($q) {
            $q->whereNotNull('nomor_bpjs')
              ->orWhereNotNull('nomor_asuransi');
        });
    }

    public function scopeByLocation($query, string $kota = null, string $provinsi = null)
    {
        if ($kota) {
            $query->where('kota', 'LIKE', "%{$kota}%");
        }

        if ($provinsi) {
            $query->where('provinsi', 'LIKE', "%{$provinsi}%");
        }

        return $query;
    }

    /**
     * Optimized query methods
     */
    protected function getDisplayRelations(): array
    {
        return ['appointments.doctor'];
    }

    protected function getApiRelations(): array
    {
        return [];
    }

    protected function getSearchableColumns(): array
    {
        return ['nama_depan', 'nama_belakang', 'nik', 'no_telepon', 'email', 'nomor_bpjs'];
    }

    /**
     * Cache frequently accessed data
     */
    public static function getCachedActiveCount(): int
    {
        return (new static)->cachedCount('active_patients_count');
    }

    public static function getCachedGenderStats(): array
    {
        return \App\Services\QueryOptimizationService::cacheQuery('patient_gender_stats', function () {
            return [
                'male' => static::active()->byGender('L')->count(),
                'female' => static::active()->byGender('P')->count(),
            ];
        });
    }

    public static function getCachedBpjsStats(): array
    {
        return \App\Services\QueryOptimizationService::cacheQuery('patient_bpjs_stats', function () {
            return [
                'aktif' => static::active()->byBpjsStatus('aktif')->count(),
                'tidak_aktif' => static::active()->byBpjsStatus('tidak_aktif')->count(),
                'pending' => static::active()->byBpjsStatus('pending')->count(),
            ];
        });
    }

    /**
     * Efficient search with proper indexing
     */
    public function scopeSearchOptimized($query, string $term)
    {
        if (empty($term)) {
            return $query;
        }

        // Use indexed columns first for better performance
        return $query->where(function ($q) use ($term) {
            // NIK exact match (most specific)
            $q->where('nik', $term)
              // Phone number exact match
              ->orWhere('no_telepon', $term)
              // BPJS number exact match
              ->orWhere('nomor_bpjs', $term)
              // Email exact match
              ->orWhere('email', $term)
              // Name partial match (use LIKE only if necessary)
              ->orWhere('nama_depan', 'LIKE', "%{$term}%")
              ->orWhere('nama_belakang', 'LIKE', "%{$term}%");
        });
    }

    /**
     * Batch operations for better performance
     */
    public static function batchUpdateStatus(array $patientIds, bool $status): int
    {
        return static::whereIn('id_pasien', $patientIds)
                    ->update(['aktif' => $status]);
    }

    public static function batchDelete(array $patientIds): int
    {
        return static::whereIn('id_pasien', $patientIds)->delete();
    }

    /**
     * Efficient exists checks
     */
    public static function existsByNik(string $nik): bool
    {
        return static::where('nik', $nik)->existsOptimized();
    }

    public static function existsByPhone(string $phone): bool
    {
        return static::where('no_telepon', $phone)->existsOptimized();
    }

    public static function existsByEmail(string $email): bool
    {
        return static::where('email', $email)->existsOptimized();
    }

    /**
     * Get model dependencies for safe delete
     */
    protected function getDependencies(): array
    {
        return [
            'appointments' => [
                'message' => 'Patient has active appointments. Please cancel or complete them first.'
            ],
            'medicalRecords' => [
                'message' => 'Patient has medical records. Consider archiving instead of deleting.'
            ]
        ];
    }

    /**
     * Model events for cache invalidation
     */
    protected static function booted(): void
    {
        static::saved(function ($patient) {
            $patient->invalidateCache('patient_*');
        });

        static::deleted(function ($patient) {
            $patient->invalidateCache('patient_*');
        });
    }
}
