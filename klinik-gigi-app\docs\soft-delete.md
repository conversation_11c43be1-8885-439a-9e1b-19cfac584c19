# Soft Delete Implementation

## Deskripsi

Implementasi soft delete untuk entitas kritis (pasien, janji temu, rekam medis) dengan audit trails, dependency checking, dan fungsi restore yang aman.

## Komponen Utama

### 1. SoftDeleteWithAudit Trait

Trait yang menggabungkan soft delete Laravel dengan audit logging:

```php
use App\Traits\SoftDeleteWithAudit;

class Patient extends Model
{
    use SoftDeleteWithAudit;
    
    // Define dependencies for safe delete
    protected function getDependencies(): array
    {
        return [
            'appointments' => [
                'message' => 'Patient has active appointments'
            ],
            'medicalRecords' => [
                'message' => 'Patient has medical records'
            ]
        ];
    }
}
```

### 2. Artisan Commands

```bash
# Show soft delete statistics
php artisan soft-delete:manage stats

# Show stats for specific model
php artisan soft-delete:manage stats patient

# Cleanup old deleted records (90+ days)
php artisan soft-delete:manage cleanup

# Cleanup specific model
php artisan soft-delete:manage cleanup patient --days=60

# Restore deleted record
php artisan soft-delete:manage restore patient --id=123
```

### 3. API Endpoints

```bash
# Get statistics
GET /soft-delete/stats
GET /soft-delete/stats?model=patient

# Get deleted records
GET /soft-delete/{model}/deleted

# Check if record can be deleted
GET /soft-delete/{model}/{id}/can-delete

# Safe delete with dependency check
DELETE /soft-delete/{model}/{id}/safe

# Restore record
POST /soft-delete/{model}/{id}/restore

# Force delete (permanent)
DELETE /soft-delete/{model}/{id}/force

# Get deletion history
GET /soft-delete/{model}/{id}/history

# Batch restore
POST /soft-delete/{model}/batch/restore
```

## Fitur Utama

### 1. Audit Trails

Setiap operasi soft delete dicatat dalam audit log:

```php
// Soft delete dengan alasan
$patient->softDeleteWithReason('Patient moved to another clinic');

// Restore dengan alasan
$patient->restoreWithReason('Patient returned');

// Force delete dengan alasan
$patient->forceDeleteWithReason('Data cleanup after 2 years');
```

### 2. Dependency Checking

Sistem mengecek dependencies sebelum menghapus:

```php
// Check if can be deleted
$canDelete = $patient->canBeDeleted();

if (!$canDelete['can_delete']) {
    foreach ($canDelete['reasons'] as $reason) {
        echo "Cannot delete: " . $reason;
    }
}

// Safe delete with automatic dependency check
$result = $patient->safeDelete('Reason for deletion');

if (!$result['success']) {
    echo "Delete failed: " . $result['message'];
}
```

### 3. Batch Operations

```php
// Batch soft delete
$result = Patient::batchSoftDelete([1, 2, 3], 'Batch cleanup');
echo "Deleted: {$result['deleted']}, Failed: " . count($result['failed']);

// Batch restore
$result = Patient::batchRestore([1, 2, 3], 'Batch restore');
echo "Restored: {$result['restored']}, Failed: " . count($result['failed']);
```

### 4. Automatic Cleanup

```php
// Cleanup records older than 90 days
$cleaned = Patient::cleanupOldDeleted(90);
echo "Permanently deleted {$cleaned} old records";

// Schedule in cron
// 0 2 * * 0 cd /path/to/app && php artisan soft-delete:manage cleanup
```

## Model Implementation

### Patient Model

```php
class Patient extends Model
{
    use SoftDeleteWithAudit, OptimizedQueries;
    
    protected function getDependencies(): array
    {
        return [
            'appointments' => [
                'message' => 'Patient has active appointments. Please cancel or complete them first.'
            ],
            'medicalRecords' => [
                'message' => 'Patient has medical records. Consider archiving instead of deleting.'
            ]
        ];
    }
}
```

### Appointment Model

```php
class Appointment extends Model
{
    use SoftDeleteWithAudit, OptimizedQueries;
    
    protected function getDependencies(): array
    {
        return [
            'medicalRecord' => [
                'message' => 'Appointment has medical records. Cannot delete appointment with existing medical records.'
            ]
        ];
    }
    
    // Status management methods
    public function cancel(string $reason = null): bool
    {
        $result = $this->update(['status' => 'dibatalkan']);
        
        if ($result && $reason) {
            AuditLog::log(
                action: 'appointment_cancelled',
                modelType: get_class($this),
                modelId: $this->getKey(),
                additionalData: ['cancellation_reason' => $reason]
            );
        }
        
        return $result;
    }
}
```

### Medical Record Model

```php
class MedicalRecord extends Model
{
    use SoftDeleteWithAudit, OptimizedQueries;
    
    protected function getDependencies(): array
    {
        return [
            'dentalChartings' => [
                'message' => 'Medical record has dental charting data. This is critical medical information that should not be deleted.'
            ]
        ];
    }
}
```

## Scopes dan Query Methods

### Time-based Scopes

```php
// Recently deleted (last 30 days)
$recentlyDeleted = Patient::recentlyDeleted(30)->get();

// Old deleted (90+ days ago)
$oldDeleted = Patient::oldDeleted(90)->get();

// This week's deleted records
$thisWeek = Patient::onlyTrashed()
                  ->whereBetween('deleted_at', [
                      now()->startOfWeek(),
                      now()->endOfWeek()
                  ])->get();
```

### Statistics Methods

```php
// Get comprehensive stats
$stats = Patient::getSoftDeleteStats();
/*
[
    'total' => 1000,
    'active' => 950,
    'deleted' => 50,
    'recently_deleted' => 10,
    'old_deleted' => 5
]
*/

// Get deletion history for specific record
$history = $patient->getDeletionHistory();
```

## API Usage Examples

### JavaScript/Frontend

```javascript
// Check if patient can be deleted
const canDelete = await fetch(`/soft-delete/patient/${patientId}/can-delete`)
    .then(r => r.json());

if (!canDelete.can_delete) {
    alert('Cannot delete: ' + canDelete.reasons.join(', '));
    return;
}

// Safe delete
const result = await fetch(`/soft-delete/patient/${patientId}/safe`, {
    method: 'DELETE',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ reason: 'Patient requested deletion' })
}).then(r => r.json());

if (result.error) {
    alert('Delete failed: ' + result.error);
} else {
    alert('Patient deleted successfully');
}

// Restore patient
const restored = await fetch(`/soft-delete/patient/${patientId}/restore`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ reason: 'Patient returned' })
}).then(r => r.json());

// Get deletion history
const history = await fetch(`/soft-delete/patient/${patientId}/history`)
    .then(r => r.json());

console.log('Deletion history:', history.history);
```

## Best Practices

### 1. Always Use Safe Delete

```php
// Good: Check dependencies first
$result = $patient->safeDelete('Reason');

// Bad: Direct soft delete without checking
$patient->delete();
```

### 2. Provide Meaningful Reasons

```php
// Good: Descriptive reason
$patient->softDeleteWithReason('Patient moved to another city and requested data removal');

// Bad: Generic reason
$patient->softDeleteWithReason('Deleted');
```

### 3. Regular Cleanup

```php
// Schedule regular cleanup in cron
// 0 2 * * 0 cd /path/to/app && php artisan soft-delete:manage cleanup --days=90

// Or in Laravel scheduler
protected function schedule(Schedule $schedule)
{
    $schedule->command('soft-delete:manage cleanup --days=90')
             ->weekly()
             ->sundays()
             ->at('02:00');
}
```

### 4. Monitor Deletion Patterns

```php
// Regular monitoring
$stats = Patient::getSoftDeleteStats();

if ($stats['deleted'] / $stats['total'] > 0.1) {
    // Alert: High deletion rate (>10%)
    Log::warning('High patient deletion rate detected', $stats);
}
```

## Security Considerations

### 1. Permission Checks

```php
// All soft delete operations require appropriate permissions
Route::delete('patient/{id}', [PatientController::class, 'destroy'])
     ->middleware('cached.permission:patients,delete');

Route::post('patient/{id}/restore', [PatientController::class, 'restore'])
     ->middleware('cached.permission:patients,update');
```

### 2. Force Delete Restrictions

```php
// Force delete requires explicit confirmation
public function forceDelete(Request $request, int $id)
{
    $request->validate([
        'confirm' => 'required|boolean|accepted',
        'reason' => 'required|string|min:10'
    ]);
    
    // Additional checks for critical data
    if ($model instanceof MedicalRecord) {
        throw new \Exception('Medical records cannot be permanently deleted');
    }
}
```

### 3. Audit Trail Integrity

```php
// Audit logs should never be deleted
class AuditLog extends Model
{
    // No soft delete for audit logs
    // Implement archiving instead of deletion
    
    public static function archive(int $olderThanDays = 365): int
    {
        // Move old logs to archive table instead of deleting
    }
}
```

## Troubleshooting

### 1. Dependency Issues

```bash
# Check what's preventing deletion
php artisan soft-delete:manage stats patient

# Check specific record
curl -X GET "/soft-delete/patient/123/can-delete"
```

### 2. Restore Issues

```bash
# Check if record exists and is deleted
php artisan soft-delete:manage restore patient --id=123

# Check deletion history
curl -X GET "/soft-delete/patient/123/history"
```

### 3. Performance Issues

```bash
# Check soft delete statistics
php artisan soft-delete:manage stats

# Cleanup old records if too many soft deleted
php artisan soft-delete:manage cleanup --days=30
```
