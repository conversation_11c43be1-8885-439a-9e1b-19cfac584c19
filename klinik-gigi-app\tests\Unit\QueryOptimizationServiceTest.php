<?php

namespace Tests\Unit;

use App\Services\QueryOptimizationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class QueryOptimizationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Cache::flush();
    }

    public function test_start_n1_monitoring()
    {
        QueryOptimizationService::startN1Monitoring();
        
        // Execute some queries
        DB::select("SELECT * FROM users WHERE id = ?", [1]);
        DB::select("SELECT * FROM users WHERE id = ?", [2]);
        DB::select("SELECT * FROM users WHERE id = ?", [3]);
        
        $problems = QueryOptimizationService::stopN1Monitoring();
        
        // Should detect similar queries
        $this->assertIsArray($problems);
    }

    public function test_cache_query()
    {
        $key = 'test_query';
        $expectedResult = ['data' => 'test'];
        
        $result = QueryOptimizationService::cacheQuery($key, function () use ($expectedResult) {
            return $expectedResult;
        });
        
        $this->assertEquals($expectedResult, $result);
        
        // Second call should return cached result
        $cachedResult = QueryOptimizationService::cacheQuery($key, function () {
            return ['data' => 'different'];
        });
        
        $this->assertEquals($expectedResult, $cachedResult);
    }

    public function test_invalidate_cache()
    {
        $key = 'test_pattern';
        
        // Cache some data
        QueryOptimizationService::cacheQuery($key, function () {
            return ['data' => 'test'];
        });
        
        // Verify it's cached
        $this->assertTrue(Cache::has(QueryOptimizationService::CACHE_KEY_PREFIX . $key));
        
        // Invalidate cache
        QueryOptimizationService::invalidateCache($key);
        
        // Verify it's cleared
        $this->assertFalse(Cache::has(QueryOptimizationService::CACHE_KEY_PREFIX . $key));
    }

    public function test_get_optimization_recommendations()
    {
        $recommendations = QueryOptimizationService::getOptimizationRecommendations();
        
        $this->assertIsArray($recommendations);
        $this->assertArrayHasKey('eager_loading', $recommendations);
        $this->assertArrayHasKey('batch_loading', $recommendations);
        $this->assertArrayHasKey('query_caching', $recommendations);
        $this->assertArrayHasKey('selective_loading', $recommendations);
        
        // Check structure of recommendations
        foreach ($recommendations as $recommendation) {
            $this->assertArrayHasKey('title', $recommendation);
            $this->assertArrayHasKey('description', $recommendation);
            $this->assertArrayHasKey('examples', $recommendation);
        }
    }

    public function test_generate_optimization_report()
    {
        // Start monitoring
        QueryOptimizationService::startN1Monitoring();
        
        // Execute some queries to simulate N+1
        for ($i = 0; $i < 6; $i++) {
            DB::select("SELECT * FROM users WHERE id = ?", [$i + 1]);
        }
        
        // Generate report
        $report = QueryOptimizationService::generateOptimizationReport();
        
        $this->assertIsArray($report);
        $this->assertArrayHasKey('summary', $report);
        $this->assertArrayHasKey('problems', $report);
        $this->assertArrayHasKey('recommendations', $report);
        $this->assertArrayHasKey('generated_at', $report);
        
        // Check summary structure
        $summary = $report['summary'];
        $this->assertArrayHasKey('n_plus_one_problems', $summary);
        $this->assertArrayHasKey('high_severity_problems', $summary);
        $this->assertArrayHasKey('total_wasted_queries', $summary);
        $this->assertArrayHasKey('total_wasted_time', $summary);
    }

    public function test_clear_optimization_cache()
    {
        // Cache some data
        QueryOptimizationService::cacheQuery('test1', fn() => 'data1');
        QueryOptimizationService::cacheQuery('test2', fn() => 'data2');
        
        // Verify cached
        $this->assertTrue(Cache::has(QueryOptimizationService::CACHE_KEY_PREFIX . 'test1'));
        $this->assertTrue(Cache::has(QueryOptimizationService::CACHE_KEY_PREFIX . 'test2'));
        
        // Clear all optimization cache
        QueryOptimizationService::clearOptimizationCache();
        
        // Verify cleared
        $this->assertFalse(Cache::has(QueryOptimizationService::CACHE_KEY_PREFIX . 'test1'));
        $this->assertFalse(Cache::has(QueryOptimizationService::CACHE_KEY_PREFIX . 'test2'));
    }

    public function test_n1_problem_detection_threshold()
    {
        QueryOptimizationService::startN1Monitoring();
        
        // Execute queries below threshold (should not be detected)
        for ($i = 0; $i < 3; $i++) {
            DB::select("SELECT * FROM users WHERE id = ?", [$i + 1]);
        }
        
        $problems = QueryOptimizationService::stopN1Monitoring();
        $this->assertEmpty($problems);
        
        // Execute queries above threshold (should be detected)
        QueryOptimizationService::startN1Monitoring();
        
        for ($i = 0; $i < 6; $i++) {
            DB::select("SELECT * FROM users WHERE id = ?", [$i + 1]);
        }
        
        $problems = QueryOptimizationService::stopN1Monitoring();
        $this->assertNotEmpty($problems);
    }

    public function test_query_normalization()
    {
        QueryOptimizationService::startN1Monitoring();
        
        // Execute similar queries with different parameters
        DB::select("SELECT * FROM users WHERE id = ?", [1]);
        DB::select("SELECT * FROM users WHERE id = ?", [2]);
        DB::select("SELECT * FROM users WHERE id = ?", [3]);
        DB::select("SELECT * FROM users WHERE id = ?", [4]);
        DB::select("SELECT * FROM users WHERE id = ?", [5]);
        DB::select("SELECT * FROM users WHERE id = ?", [6]);
        
        $problems = QueryOptimizationService::stopN1Monitoring();
        
        // Should group similar queries together
        $this->assertCount(1, $problems);
        $this->assertEquals(6, $problems[0]['count']);
    }

    public function test_severity_calculation()
    {
        QueryOptimizationService::startN1Monitoring();
        
        // Execute many slow queries
        for ($i = 0; $i < 20; $i++) {
            DB::select("SELECT * FROM users WHERE id = ?", [$i + 1]);
        }
        
        $problems = QueryOptimizationService::stopN1Monitoring();
        
        if (!empty($problems)) {
            $problem = $problems[0];
            $this->assertArrayHasKey('severity', $problem);
            $this->assertIsInt($problem['severity']);
            $this->assertGreaterThanOrEqual(0, $problem['severity']);
            $this->assertLessThanOrEqual(100, $problem['severity']);
        }
    }

    public function test_suggestions_generation()
    {
        QueryOptimizationService::startN1Monitoring();
        
        // Execute queries that should generate suggestions
        for ($i = 0; $i < 10; $i++) {
            DB::select("SELECT * FROM users WHERE id = ?", [$i + 1]);
        }
        
        $problems = QueryOptimizationService::stopN1Monitoring();
        
        if (!empty($problems)) {
            $problem = $problems[0];
            $this->assertArrayHasKey('suggestions', $problem);
            $this->assertIsArray($problem['suggestions']);
            $this->assertNotEmpty($problem['suggestions']);
            
            // Check that suggestions are strings
            foreach ($problem['suggestions'] as $suggestion) {
                $this->assertIsString($suggestion);
            }
        }
    }
}
