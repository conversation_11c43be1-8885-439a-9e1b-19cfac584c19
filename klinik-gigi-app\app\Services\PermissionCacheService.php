<?php

namespace App\Services;

use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Support\Facades\Cache;

use Illuminate\Support\Facades\Log;

class PermissionCacheService
{
    // Cache keys
    const USER_PERMISSIONS_KEY = 'user_permissions:';
    const ROLE_PERMISSIONS_KEY = 'role_permissions:';
    const USER_ROLE_KEY = 'user_role:';
    const PERMISSION_MODULES_KEY = 'permission_modules:';

    // Cache TTL (Time To Live) dalam detik
    const CACHE_TTL = 3600; // 1 jam
    const ROLE_CACHE_TTL = 7200; // 2 jam (role jarang berubah)

    /**
     * Get user permissions from cache or database
     */
    public function getUserPermissions(int $userId): array
    {
        $cacheKey = self::USER_PERMISSIONS_KEY . $userId;

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($userId) {
            $user = User::with(['role.permissions'])->find($userId);

            if (!$user || !$user->role) {
                return [];
            }

            $permissions = [];
            foreach ($user->role->permissions as $permission) {
                $permissions[$permission->modul] = [
                    'baca' => $permission->baca,
                    'tulis' => $permission->tulis,
                    'ubah' => $permission->ubah,
                    'hapus' => $permission->hapus,
                ];
            }

            Log::info('User permissions loaded from database', [
                'user_id' => $userId,
                'permissions_count' => count($permissions)
            ]);

            return $permissions;
        });
    }

    /**
     * Get role permissions from cache or database
     */
    public function getRolePermissions(int $roleId): array
    {
        $cacheKey = self::ROLE_PERMISSIONS_KEY . $roleId;

        return Cache::remember($cacheKey, self::ROLE_CACHE_TTL, function () use ($roleId) {
            $role = Role::with('permissions')->find($roleId);

            if (!$role) {
                return [];
            }

            $permissions = [];
            foreach ($role->permissions as $permission) {
                $permissions[$permission->modul] = [
                    'baca' => $permission->baca,
                    'tulis' => $permission->tulis,
                    'ubah' => $permission->ubah,
                    'hapus' => $permission->hapus,
                ];
            }

            return $permissions;
        });
    }

    /**
     * Get user role from cache or database
     */
    public function getUserRole(int $userId): ?array
    {
        $cacheKey = self::USER_ROLE_KEY . $userId;

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($userId) {
            $user = User::with('role')->find($userId);

            if (!$user || !$user->role) {
                return null;
            }

            return [
                'id_peran' => $user->role->id_peran,
                'nama_peran' => $user->role->nama_peran,
                'deskripsi' => $user->role->deskripsi,
            ];
        });
    }

    /**
     * Check if user has specific permission (with caching)
     */
    public function hasPermission(int $userId, string $module, string $action): bool
    {
        $permissions = $this->getUserPermissions($userId);

        if (!isset($permissions[$module])) {
            return false;
        }

        return match($action) {
            'read', 'baca' => $permissions[$module]['baca'] ?? false,
            'create', 'tulis' => $permissions[$module]['tulis'] ?? false,
            'update', 'ubah' => $permissions[$module]['ubah'] ?? false,
            'delete', 'hapus' => $permissions[$module]['hapus'] ?? false,
            default => false
        };
    }

    /**
     * Get all available modules for a user
     */
    public function getUserModules(int $userId): array
    {
        $permissions = $this->getUserPermissions($userId);
        return array_keys($permissions);
    }

    /**
     * Invalidate user permission cache
     */
    public function invalidateUserCache(int $userId): void
    {
        $keys = [
            self::USER_PERMISSIONS_KEY . $userId,
            self::USER_ROLE_KEY . $userId,
        ];

        foreach ($keys as $key) {
            Cache::forget($key);
        }

        Log::info('User permission cache invalidated', ['user_id' => $userId]);
    }

    /**
     * Invalidate role permission cache
     */
    public function invalidateRoleCache(int $roleId): void
    {
        Cache::forget(self::ROLE_PERMISSIONS_KEY . $roleId);

        // Invalidate all users with this role
        $userIds = User::where('id_peran', $roleId)->pluck('id');
        foreach ($userIds as $userId) {
            $this->invalidateUserCache($userId);
        }

        Log::info('Role permission cache invalidated', [
            'role_id' => $roleId,
            'affected_users' => $userIds->count()
        ]);
    }

    /**
     * Warm up cache for frequently accessed permissions
     */
    public function warmUpCache(): void
    {
        Log::info('Starting permission cache warm-up');

        // Warm up active users (yang login dalam 7 hari terakhir)
        $activeUsers = User::where('aktif', true)
            ->where('login_terakhir', '>=', now()->subDays(7))
            ->limit(100) // Batasi untuk menghindari overload
            ->get();

        foreach ($activeUsers as $user) {
            $this->getUserPermissions($user->id);
            $this->getUserRole($user->id);
        }

        // Warm up all roles
        $roles = Role::all();
        foreach ($roles as $role) {
            $this->getRolePermissions($role->id_peran);
        }

        Log::info('Permission cache warm-up completed', [
            'users_cached' => $activeUsers->count(),
            'roles_cached' => $roles->count()
        ]);
    }

    /**
     * Clear all permission caches
     */
    public function clearAllCache(): void
    {
        // For file/database cache, we'll clear by individual keys
        // This is less efficient than Redis pattern matching but works with all cache drivers

        // Clear known cache keys (this is a simplified approach)
        Cache::flush(); // This clears all cache, which is more aggressive but works

        Log::info('All permission caches cleared');
    }

    /**
     * Get cache statistics
     */
    public function getCacheStats(): array
    {
        // For non-Redis cache drivers, we can't easily count keys by pattern
        // So we'll return a simplified stats structure
        $stats = [
            'user_permissions' => 'N/A (requires Redis)',
            'role_permissions' => 'N/A (requires Redis)',
            'user_roles' => 'N/A (requires Redis)',
            'total_cached_items' => 'N/A (requires Redis)',
            'cache_driver' => config('cache.default')
        ];

        return $stats;
    }
}
