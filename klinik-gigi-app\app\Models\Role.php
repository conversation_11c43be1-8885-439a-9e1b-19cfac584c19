<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Role extends Model
{
    protected $table = 'roles';
    protected $primaryKey = 'id_peran';

    protected $fillable = [
        'nama_peran',
        'deskripsi',
        'hak_akses'
    ];

    protected $casts = [
        'hak_akses' => 'array'
    ];

    /**
     * Relasi ke tabel users
     */
    public function users(): Has<PERSON>any
    {
        return $this->hasMany(User::class, 'id_peran', 'id_peran');
    }

    /**
     * Relasi ke tabel permissions
     */
    public function permissions(): HasMany
    {
        return $this->hasMany(Permission::class, 'id_peran', 'id_peran');
    }

    /**
     * Check if role has permission for specific module and action
     */
    public function hasPermission(string $module, string $action): bool
    {
        $permission = $this->permissions()
            ->where('modul', $module)
            ->first();

        if (!$permission) {
            return false;
        }

        return match($action) {
            'read' => $permission->baca,
            'create' => $permission->tulis,
            'update' => $permission->ubah,
            'delete' => $permission->hapus,
            default => false
        };
    }

    /**
     * Get all modules this role has access to
     */
    public function getModules(): array
    {
        return $this->permissions()
            ->select('modul')
            ->distinct()
            ->pluck('modul')
            ->toArray();
    }
}
