<?php

namespace App\Services;

use Illuminate\Support\Str;

class InputSanitizationService
{
    // XSS patterns to detect and remove
    const XSS_PATTERNS = [
        '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
        '/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/mi',
        '/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/mi',
        '/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/mi',
        '/<applet\b[^<]*(?:(?!<\/applet>)<[^<]*)*<\/applet>/mi',
        '/<form\b[^<]*(?:(?!<\/form>)<[^<]*)*<\/form>/mi',
        '/javascript:/i',
        '/vbscript:/i',
        '/onload\s*=/i',
        '/onerror\s*=/i',
        '/onclick\s*=/i',
        '/onmouseover\s*=/i',
        '/onfocus\s*=/i',
        '/onblur\s*=/i',
        '/onchange\s*=/i',
        '/onsubmit\s*=/i',
        '/expression\s*\(/i',
        '/url\s*\(/i',
        '/@import/i',
        '/binding\s*:/i'
    ];

    // SQL injection patterns
    const SQL_INJECTION_PATTERNS = [
        '/(\s|^)(union|select|insert|update|delete|drop|create|alter|exec|execute)\s/i',
        '/(\s|^)(or|and)\s+\d+\s*=\s*\d+/i',
        '/(\s|^)(or|and)\s+[\'"].*[\'"](\s*=\s*[\'"].*[\'"])?/i',
        '/(\s|^)(or|and)\s+\w+\s*=\s*\w+/i',
        '/--/i',
        '/\/\*/i',
        '/\*\//i',
        '/;/i'
    ];

    // Command injection patterns
    const COMMAND_INJECTION_PATTERNS = [
        '/[;&|`$(){}[\]]/i',
        '/\.\.\//i',
        '/(cat|ls|pwd|whoami|id|uname|ps|netstat|ifconfig|ping|wget|curl|nc|telnet|ssh|ftp)/i'
    ];

    /**
     * Sanitize input string for XSS protection
     */
    public static function sanitizeXSS(string $input): string
    {
        if (empty($input)) {
            return $input;
        }

        // Remove null bytes
        $input = str_replace(chr(0), '', $input);

        // Remove XSS patterns
        foreach (self::XSS_PATTERNS as $pattern) {
            $input = preg_replace($pattern, '', $input);
        }

        // HTML encode special characters
        $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        return $input;
    }

    /**
     * Sanitize input for SQL injection protection
     */
    public static function sanitizeSQL(string $input): string
    {
        if (empty($input)) {
            return $input;
        }

        // Remove SQL injection patterns
        foreach (self::SQL_INJECTION_PATTERNS as $pattern) {
            if (preg_match($pattern, $input)) {
                // Log potential SQL injection attempt
                \Log::warning('Potential SQL injection attempt detected', [
                    'input' => $input,
                    'ip' => request()->ip(),
                    'user_agent' => request()->userAgent(),
                    'user_id' => auth()->id()
                ]);
                
                // Remove the malicious content
                $input = preg_replace($pattern, '', $input);
            }
        }

        return trim($input);
    }

    /**
     * Sanitize input for command injection protection
     */
    public static function sanitizeCommand(string $input): string
    {
        if (empty($input)) {
            return $input;
        }

        // Remove command injection patterns
        foreach (self::COMMAND_INJECTION_PATTERNS as $pattern) {
            if (preg_match($pattern, $input)) {
                // Log potential command injection attempt
                \Log::warning('Potential command injection attempt detected', [
                    'input' => $input,
                    'ip' => request()->ip(),
                    'user_agent' => request()->userAgent(),
                    'user_id' => auth()->id()
                ]);
                
                // Remove the malicious content
                $input = preg_replace($pattern, '', $input);
            }
        }

        return trim($input);
    }

    /**
     * Comprehensive input sanitization
     */
    public static function sanitize(string $input, array $options = []): string
    {
        if (empty($input)) {
            return $input;
        }

        $options = array_merge([
            'xss' => true,
            'sql' => true,
            'command' => true,
            'trim' => true,
            'strip_tags' => false,
            'allowed_tags' => '',
            'max_length' => null
        ], $options);

        // Trim whitespace
        if ($options['trim']) {
            $input = trim($input);
        }

        // Limit length
        if ($options['max_length'] && strlen($input) > $options['max_length']) {
            $input = substr($input, 0, $options['max_length']);
        }

        // Strip HTML tags
        if ($options['strip_tags']) {
            $input = strip_tags($input, $options['allowed_tags']);
        }

        // XSS protection
        if ($options['xss']) {
            $input = self::sanitizeXSS($input);
        }

        // SQL injection protection
        if ($options['sql']) {
            $input = self::sanitizeSQL($input);
        }

        // Command injection protection
        if ($options['command']) {
            $input = self::sanitizeCommand($input);
        }

        return $input;
    }

    /**
     * Sanitize array of inputs
     */
    public static function sanitizeArray(array $inputs, array $options = []): array
    {
        $sanitized = [];

        foreach ($inputs as $key => $value) {
            if (is_string($value)) {
                $sanitized[$key] = self::sanitize($value, $options);
            } elseif (is_array($value)) {
                $sanitized[$key] = self::sanitizeArray($value, $options);
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }

    /**
     * Validate email format and sanitize
     */
    public static function sanitizeEmail(string $email): string
    {
        $email = trim(strtolower($email));
        $email = filter_var($email, FILTER_SANITIZE_EMAIL);
        
        return $email;
    }

    /**
     * Sanitize phone number
     */
    public static function sanitizePhone(string $phone): string
    {
        // Remove all non-numeric characters except + and -
        $phone = preg_replace('/[^0-9+\-]/', '', $phone);
        
        // Limit length
        $phone = substr($phone, 0, 20);
        
        return $phone;
    }

    /**
     * Sanitize username
     */
    public static function sanitizeUsername(string $username): string
    {
        // Allow only alphanumeric, underscore, and hyphen
        $username = preg_replace('/[^a-zA-Z0-9_\-]/', '', $username);
        
        // Convert to lowercase
        $username = strtolower($username);
        
        // Limit length
        $username = substr($username, 0, 50);
        
        return $username;
    }

    /**
     * Sanitize filename
     */
    public static function sanitizeFilename(string $filename): string
    {
        // Remove path traversal attempts
        $filename = basename($filename);
        
        // Remove dangerous characters
        $filename = preg_replace('/[^a-zA-Z0-9._\-]/', '', $filename);
        
        // Prevent hidden files
        $filename = ltrim($filename, '.');
        
        // Limit length
        $filename = substr($filename, 0, 255);
        
        return $filename;
    }

    /**
     * Sanitize URL
     */
    public static function sanitizeUrl(string $url): string
    {
        $url = trim($url);
        
        // Remove javascript: and data: protocols
        $url = preg_replace('/^(javascript|data|vbscript):/i', '', $url);
        
        // Validate URL format
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return '';
        }
        
        return $url;
    }

    /**
     * Check if input contains potential XSS
     */
    public static function containsXSS(string $input): bool
    {
        foreach (self::XSS_PATTERNS as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if input contains potential SQL injection
     */
    public static function containsSQLInjection(string $input): bool
    {
        foreach (self::SQL_INJECTION_PATTERNS as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if input contains potential command injection
     */
    public static function containsCommandInjection(string $input): bool
    {
        foreach (self::COMMAND_INJECTION_PATTERNS as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Generate Content Security Policy header
     */
    public static function generateCSPHeader(): string
    {
        $directives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
            "style-src 'self' 'unsafe-inline'",
            "img-src 'self' data: https:",
            "font-src 'self' data:",
            "connect-src 'self'",
            "media-src 'self'",
            "object-src 'none'",
            "child-src 'self'",
            "frame-ancestors 'none'",
            "base-uri 'self'",
            "form-action 'self'"
        ];

        return implode('; ', $directives);
    }

    /**
     * Sanitize rich text content (for WYSIWYG editors)
     */
    public static function sanitizeRichText(string $content): string
    {
        // Allow only safe HTML tags
        $allowedTags = '<p><br><strong><em><u><ol><ul><li><h1><h2><h3><h4><h5><h6><blockquote><a>';
        
        // Strip dangerous tags
        $content = strip_tags($content, $allowedTags);
        
        // Remove dangerous attributes
        $content = preg_replace('/<([^>]+)(?:on\w+|style|class|id)=["\'][^"\']*["\']([^>]*)>/i', '<$1$2>', $content);
        
        // Sanitize links
        $content = preg_replace_callback('/<a\s+href=["\']([^"\']*)["\']([^>]*)>/i', function($matches) {
            $href = self::sanitizeUrl($matches[1]);
            return $href ? "<a href=\"{$href}\"{$matches[2]}>" : '';
        }, $content);
        
        return $content;
    }
}
