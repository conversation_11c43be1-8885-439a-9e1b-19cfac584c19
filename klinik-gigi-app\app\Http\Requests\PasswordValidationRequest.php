<?php

namespace App\Http\Requests;

use App\Services\PasswordPolicyService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class PasswordValidationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'password' => [
                'required',
                'string',
                'min:12',
                'max:128',
                'confirmed'
            ],
            'current_password' => 'required_if:is_update,true|string'
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            if ($this->has('password')) {
                $password = $this->input('password');
                $user = $this->route('user') ?? auth()->user();
                
                // Validate password against policy
                $policyErrors = PasswordPolicyService::validatePassword($password, $user);
                
                if (!empty($policyErrors)) {
                    foreach ($policyErrors as $error) {
                        $validator->errors()->add('password', $error);
                    }
                }
                
                // If updating password, verify current password
                if ($this->input('is_update') && $user) {
                    $currentPassword = $this->input('current_password');
                    if (!password_verify($currentPassword, $user->password)) {
                        $validator->errors()->add('current_password', 'Password saat ini tidak benar.');
                    }
                }
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'password.required' => 'Password wajib diisi.',
            'password.string' => 'Password harus berupa teks.',
            'password.min' => 'Password minimal 12 karakter.',
            'password.max' => 'Password maksimal 128 karakter.',
            'password.confirmed' => 'Konfirmasi password tidak cocok.',
            'current_password.required_if' => 'Password saat ini wajib diisi.',
            'current_password.string' => 'Password saat ini harus berupa teks.'
        ];
    }

    /**
     * Get password strength information
     */
    public function getPasswordStrength(): array
    {
        if ($this->has('password')) {
            return PasswordPolicyService::getPasswordStrength($this->input('password'));
        }
        
        return ['score' => 0, 'level' => 'very_weak', 'feedback' => []];
    }
}
