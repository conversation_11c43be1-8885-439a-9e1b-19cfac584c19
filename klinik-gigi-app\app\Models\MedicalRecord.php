<?php

namespace App\Models;

use App\Traits\OptimizedQueries;
use App\Traits\SoftDeleteWithAudit;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MedicalRecord extends Model
{
    use HasFactory, SoftDeleteWithAudit, OptimizedQueries;

    protected $table = 'medical_records';
    protected $primaryKey = 'id_rekam_medis';

    /**
     * Default relationships to eager load
     */
    protected array $defaultWith = ['patient', 'doctor'];

    protected $fillable = [
        'id_pasien',
        'id_janji_temu',
        'id_dokter',
        'tanggal_kunjungan',
        'keluhan_utama',
        'pemeriksaan_objektif',
        'diagnosis',
        'rencana_perawatan',
        'catatan_klinis'
    ];

    protected $casts = [
        'tanggal_kunjungan' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * Relationships
     */
    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class, 'id_pasien', 'id_pasien');
    }

    public function doctor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'id_dokter', 'id');
    }

    public function appointment(): BelongsTo
    {
        return $this->belongsTo(Appointment::class, 'id_janji_temu', 'id_janji_temu');
    }

    public function dentalChartings(): HasMany
    {
        return $this->hasMany(DentalCharting::class, 'id_rekam_medis', 'id_rekam_medis');
    }

    /**
     * Scopes
     */
    public function scopeByPatient($query, int $patientId)
    {
        return $query->where('id_pasien', $patientId);
    }

    public function scopeByDoctor($query, int $doctorId)
    {
        return $query->where('id_dokter', $doctorId);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('tanggal_kunjungan', [$startDate, $endDate]);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('tanggal_kunjungan', today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('tanggal_kunjungan', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereBetween('tanggal_kunjungan', [
            now()->startOfMonth(),
            now()->endOfMonth()
        ]);
    }

    public function scopeWithDiagnosis($query, string $diagnosis)
    {
        return $query->where('diagnosis', 'LIKE', "%{$diagnosis}%");
    }

    /**
     * Optimized query methods
     */
    protected function getDisplayRelations(): array
    {
        return ['patient', 'doctor', 'appointment', 'dentalChartings'];
    }

    protected function getApiRelations(): array
    {
        return ['patient', 'doctor'];
    }

    protected function getSearchableColumns(): array
    {
        return ['keluhan_utama', 'diagnosis', 'pemeriksaan_objektif', 'catatan_klinis'];
    }

    /**
     * Get model dependencies for safe delete
     */
    protected function getDependencies(): array
    {
        return [
            'dentalChartings' => [
                'message' => 'Medical record has dental charting data. This is critical medical information that should not be deleted.'
            ]
        ];
    }

    /**
     * Medical record specific methods
     */
    public function addDentalCharting(int $toothNumber, string $condition, string $description = null): DentalCharting
    {
        return $this->dentalChartings()->create([
            'nomor_gigi' => $toothNumber,
            'kondisi_gigi' => $condition,
            'keterangan' => $description,
            'warna_status' => $this->getConditionColor($condition)
        ]);
    }

    public function updateDentalCharting(int $toothNumber, string $condition, string $description = null): bool
    {
        return $this->dentalChartings()
                   ->where('nomor_gigi', $toothNumber)
                   ->update([
                       'kondisi_gigi' => $condition,
                       'keterangan' => $description,
                       'warna_status' => $this->getConditionColor($condition)
                   ]);
    }

    protected function getConditionColor(string $condition): string
    {
        return match($condition) {
            'sehat' => 'green',
            'karies' => 'red',
            'tambalan' => 'blue',
            'mahkota' => 'gold',
            'hilang' => 'black',
            'impaksi' => 'orange',
            default => 'gray'
        };
    }

    /**
     * Get patient's medical history
     */
    public static function getPatientHistory(int $patientId, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return static::byPatient($patientId)
                    ->with(['doctor', 'appointment'])
                    ->orderBy('tanggal_kunjungan', 'desc')
                    ->limit($limit)
                    ->get();
    }

    /**
     * Search medical records
     */
    public function scopeSearchMedical($query, string $term)
    {
        return $query->where(function ($q) use ($term) {
            $q->where('keluhan_utama', 'LIKE', "%{$term}%")
              ->orWhere('diagnosis', 'LIKE', "%{$term}%")
              ->orWhere('pemeriksaan_objektif', 'LIKE', "%{$term}%")
              ->orWhere('catatan_klinis', 'LIKE', "%{$term}%");
        });
    }

    /**
     * Get statistics
     */
    public static function getDoctorStats(int $doctorId, $startDate = null, $endDate = null): array
    {
        $query = static::byDoctor($doctorId);
        
        if ($startDate && $endDate) {
            $query->byDateRange($startDate, $endDate);
        }

        return [
            'total_records' => $query->count(),
            'this_month' => static::byDoctor($doctorId)->thisMonth()->count(),
            'this_week' => static::byDoctor($doctorId)->thisWeek()->count(),
            'today' => static::byDoctor($doctorId)->today()->count(),
        ];
    }

    public static function getPatientStats(int $patientId): array
    {
        $records = static::byPatient($patientId)->get();
        
        return [
            'total_visits' => $records->count(),
            'first_visit' => $records->min('tanggal_kunjungan'),
            'last_visit' => $records->max('tanggal_kunjungan'),
            'common_diagnoses' => $records->pluck('diagnosis')
                                         ->filter()
                                         ->countBy()
                                         ->sortDesc()
                                         ->take(5)
                                         ->toArray()
        ];
    }

    /**
     * Export medical record data
     */
    public function toExportArray(): array
    {
        return [
            'nomor_rekam_medis' => $this->id_rekam_medis,
            'nomor_pasien' => $this->patient->nomor_pasien ?? '',
            'nama_pasien' => $this->patient->full_name ?? '',
            'nama_dokter' => $this->doctor->full_name ?? '',
            'tanggal_kunjungan' => $this->tanggal_kunjungan->format('Y-m-d H:i:s'),
            'keluhan_utama' => $this->keluhan_utama,
            'pemeriksaan_objektif' => $this->pemeriksaan_objektif,
            'diagnosis' => $this->diagnosis,
            'rencana_perawatan' => $this->rencana_perawatan,
            'catatan_klinis' => $this->catatan_klinis,
        ];
    }

    /**
     * Model events
     */
    protected static function booted(): void
    {
        static::saved(function ($record) {
            $record->invalidateCache('medical_record_*');
        });

        static::deleted(function ($record) {
            $record->invalidateCache('medical_record_*');
        });
    }
}
