<?php

namespace App\Http\Requests\Auth;

use App\Models\AuditLog;
use App\Models\User;
use App\Services\AccountLockoutService;
use Illuminate\Auth\Events\Lockout;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'login' => ['required', 'string'],
            'password' => ['required', 'string'],
        ];
    }

    /**
     * Attempt to authenticate the request's credentials.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function authenticate(): void
    {
        $this->ensureIsNotRateLimited();

        $login = $this->input('login');
        $password = $this->input('password');

        // Determine if login is email or username
        $field = filter_var($login, FILTER_VALIDATE_EMAIL) ? 'email' : 'username';

        // Check IP lockout first
        $ipAddress = request()->ip();
        if (AccountLockoutService::isIpLocked($ipAddress)) {
            throw ValidationException::withMessages([
                'login' => 'IP Address Anda telah diblokir sementara karena terlalu banyak percobaan login gagal.',
            ]);
        }

        // Find user first to check account status
        $user = User::where($field, $login)->first();

        if ($user) {
            // Check if account is locked
            if ($user->isLocked()) {
                // Log failed login attempt due to locked account
                AuditLog::logAuth(AuditLog::ACTION_LOGIN_FAILED, $user, [
                    'reason' => 'account_locked',
                    'login_field' => $field,
                    'login_value' => $login
                ]);

                throw ValidationException::withMessages([
                    'login' => 'Akun Anda terkunci. Silakan hubungi administrator atau coba lagi nanti.',
                ]);
            }

            // Check if account is active
            if (!$user->isActive()) {
                // Log failed login attempt due to inactive account
                AuditLog::logAuth(AuditLog::ACTION_LOGIN_FAILED, $user, [
                    'reason' => 'account_inactive',
                    'login_field' => $field,
                    'login_value' => $login
                ]);

                throw ValidationException::withMessages([
                    'login' => 'Akun Anda tidak aktif. Hubungi administrator.',
                ]);
            }
        }

        $credentials = [
            $field => $login,
            'password' => $password,
            'aktif' => true
        ];

        if (! Auth::attempt($credentials, $this->boolean('remember'))) {
            RateLimiter::hit($this->throttleKey());

            // Handle failed login attempt using AccountLockoutService
            if ($user) {
                $lockoutResult = AccountLockoutService::handleFailedAttempt($user, $ipAddress);

                // Log failed login attempt
                AuditLog::logAuth(AuditLog::ACTION_LOGIN_FAILED, $user, [
                    'reason' => 'invalid_credentials',
                    'login_field' => $field,
                    'login_value' => $login,
                    'failed_attempts' => $user->failed_login_attempts,
                    'lockout_result' => $lockoutResult
                ]);

                // Throw appropriate error message
                throw ValidationException::withMessages([
                    'login' => $lockoutResult['message']
                ]);
            }

            // Generic error for non-existent users
            throw ValidationException::withMessages([
                'login' => 'Username/email atau password salah.',
            ]);
        }

        // Reset failed attempts and IP attempts on successful login
        if ($user) {
            $user->update([
                'failed_login_attempts' => 0,
                'locked_until' => null
            ]);

            // Reset IP failed attempts on successful login
            AccountLockoutService::resetIpFailedAttempts($ipAddress);
        }

        RateLimiter::clear($this->throttleKey());
    }

    /**
     * Ensure the login request is not rate limited.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function ensureIsNotRateLimited(): void
    {
        if (! RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout($this));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'login' => __('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Get the rate limiting throttle key for the request.
     */
    public function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->string('login')).'|'.$this->ip());
    }
}
