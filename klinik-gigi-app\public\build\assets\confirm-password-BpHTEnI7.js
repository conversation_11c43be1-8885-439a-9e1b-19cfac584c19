import{u as p,j as s,H as m}from"./app-EmUGGW4y.js";import{L as l,I as c,a as u}from"./label-BIgw4gz0.js";import{B as w}from"./button-B8QorGO4.js";import{A as f,L as h}from"./auth-layout-CTfIlSls.js";/* empty css            */import"./index-CrVQA8Zu.js";import"./app-logo-icon-BW5sZeJe.js";function L(){const{data:o,setData:e,post:t,processing:a,errors:i,reset:d}=p({password:""}),n=r=>{r.preventDefault(),t(route("password.confirm"),{onFinish:()=>d("password")})};return s.jsxs(f,{title:"Confirm your password",description:"This is a secure area of the application. Please confirm your password before continuing.",children:[s.jsx(m,{title:"Confirm password"}),s.jsx("form",{onSubmit:n,children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(l,{htmlFor:"password",children:"Password"}),s.jsx(c,{id:"password",type:"password",name:"password",placeholder:"Password",autoComplete:"current-password",value:o.password,autoFocus:!0,onChange:r=>e("password",r.target.value)}),s.jsx(u,{message:i.password})]}),s.jsx("div",{className:"flex items-center",children:s.jsxs(w,{className:"w-full",disabled:a,children:[a&&s.jsx(h,{className:"h-4 w-4 animate-spin"}),"Confirm password"]})})]})})]})}export{L as default};
