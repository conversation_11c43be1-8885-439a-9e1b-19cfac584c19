<?php

namespace App\Rules;

use App\Services\InputSanitizationService;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class NoMaliciousContent implements ValidationRule
{
    private array $detectedThreats = [];

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!is_string($value)) {
            return;
        }

        $this->detectedThreats = [];

        // Check for XSS
        if (InputSanitizationService::containsXSS($value)) {
            $this->detectedThreats[] = 'XSS';
        }

        // Check for SQL injection
        if (InputSanitizationService::containsSQLInjection($value)) {
            $this->detectedThreats[] = 'SQL injection';
        }

        // Check for command injection
        if (InputSanitizationService::containsCommandInjection($value)) {
            $this->detectedThreats[] = 'Command injection';
        }

        if (!empty($this->detectedThreats)) {
            $threats = implode(', ', $this->detectedThreats);
            $fail("Field {$attribute} contains potentially malicious content ({$threats}). Please remove any scripts, SQL commands, or system commands.");
        }
    }

    /**
     * Get detected threats
     */
    public function getDetectedThreats(): array
    {
        return $this->detectedThreats;
    }
}
