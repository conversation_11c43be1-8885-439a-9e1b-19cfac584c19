<?php

namespace App\Console\Commands;

use App\Services\PermissionCacheService;
use Illuminate\Console\Command;

class PermissionCacheCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permission:cache {action : Action to perform (warm-up|clear|stats)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage permission cache (warm-up, clear, or show stats)';

    protected PermissionCacheService $permissionCache;

    public function __construct(PermissionCacheService $permissionCache)
    {
        parent::__construct();
        $this->permissionCache = $permissionCache;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'warm-up':
                return $this->warmUpCache();
            case 'clear':
                return $this->clearCache();
            case 'stats':
                return $this->showStats();
            default:
                $this->error("Invalid action: {$action}");
                $this->info('Available actions: warm-up, clear, stats');
                return 1;
        }
    }

    /**
     * Warm up permission cache
     */
    protected function warmUpCache(): int
    {
        $this->info('Warming up permission cache...');
        
        $startTime = microtime(true);
        $this->permissionCache->warmUpCache();
        $endTime = microtime(true);
        
        $duration = round(($endTime - $startTime) * 1000, 2);
        
        $this->info("Permission cache warmed up successfully in {$duration}ms");
        
        // Show stats after warm-up
        $this->showStats();
        
        return 0;
    }

    /**
     * Clear permission cache
     */
    protected function clearCache(): int
    {
        if ($this->confirm('Are you sure you want to clear all permission caches?')) {
            $this->info('Clearing permission cache...');
            
            $this->permissionCache->clearAllCache();
            
            $this->info('Permission cache cleared successfully');
            return 0;
        }
        
        $this->info('Cache clear cancelled');
        return 0;
    }

    /**
     * Show cache statistics
     */
    protected function showStats(): int
    {
        $stats = $this->permissionCache->getCacheStats();
        
        $this->info('Permission Cache Statistics:');
        $this->table(
            ['Cache Type', 'Count'],
            [
                ['User Permissions', $stats['user_permissions']],
                ['Role Permissions', $stats['role_permissions']],
                ['User Roles', $stats['user_roles']],
                ['Total Items', $stats['total_cached_items']],
            ]
        );
        
        return 0;
    }
}
