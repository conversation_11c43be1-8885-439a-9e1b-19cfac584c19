<?php

namespace App\Http\Controllers;

use App\Models\Patient;
use App\Models\Appointment;
use App\Models\MedicalRecord;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class SoftDeleteController extends Controller
{
    protected array $models = [
        'patient' => Patient::class,
        'appointment' => Appointment::class,
        'medical-record' => MedicalRecord::class,
    ];

    /**
     * Get soft delete statistics
     */
    public function stats(Request $request): JsonResponse
    {
        $model = $request->query('model');
        
        if ($model) {
            if (!isset($this->models[$model])) {
                return response()->json([
                    'error' => 'Invalid model',
                    'available_models' => array_keys($this->models)
                ], 400);
            }

            $modelClass = $this->models[$model];
            $stats = $modelClass::getSoftDeleteStats();
            
            return response()->json([
                'model' => $model,
                'stats' => $stats
            ]);
        }

        // Get stats for all models
        $allStats = [];
        foreach ($this->models as $modelName => $modelClass) {
            $allStats[$modelName] = $modelClass::getSoftDeleteStats();
        }

        return response()->json([
            'stats' => $allStats,
            'summary' => [
                'total_active' => array_sum(array_column($allStats, 'active')),
                'total_deleted' => array_sum(array_column($allStats, 'deleted')),
                'total_records' => array_sum(array_column($allStats, 'total')),
            ]
        ]);
    }

    /**
     * Get deleted records
     */
    public function deleted(Request $request, string $model): JsonResponse
    {
        if (!isset($this->models[$model])) {
            return response()->json([
                'error' => 'Invalid model',
                'available_models' => array_keys($this->models)
            ], 400);
        }

        $modelClass = $this->models[$model];
        $perPage = $request->query('per_page', 15);
        $search = $request->query('search');

        $query = $modelClass::onlyTrashed()->orderBy('deleted_at', 'desc');

        // Apply search if provided
        if ($search && method_exists($modelClass, 'scopeSearchOptimized')) {
            $query->searchOptimized($search);
        }

        $records = $query->paginate($perPage);

        return response()->json([
            'model' => $model,
            'data' => $records->items(),
            'pagination' => [
                'current_page' => $records->currentPage(),
                'last_page' => $records->lastPage(),
                'per_page' => $records->perPage(),
                'total' => $records->total(),
            ]
        ]);
    }

    /**
     * Restore a soft deleted record
     */
    public function restore(Request $request, string $model, int $id): JsonResponse
    {
        if (!isset($this->models[$model])) {
            return response()->json([
                'error' => 'Invalid model',
                'available_models' => array_keys($this->models)
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'reason' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 422);
        }

        $modelClass = $this->models[$model];
        $record = $modelClass::withTrashed()->find($id);

        if (!$record) {
            return response()->json([
                'error' => 'Record not found'
            ], 404);
        }

        if (!$record->trashed()) {
            return response()->json([
                'error' => 'Record is not deleted'
            ], 400);
        }

        try {
            $reason = $request->input('reason', 'Restored via API');
            $record->restoreWithReason($reason);

            return response()->json([
                'message' => 'Record restored successfully',
                'data' => $record->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to restore record',
                'details' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Batch restore multiple records
     */
    public function batchRestore(Request $request, string $model): JsonResponse
    {
        if (!isset($this->models[$model])) {
            return response()->json([
                'error' => 'Invalid model',
                'available_models' => array_keys($this->models)
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer',
            'reason' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 422);
        }

        $modelClass = $this->models[$model];
        $ids = $request->input('ids');
        $reason = $request->input('reason', 'Batch restored via API');

        $result = $modelClass::batchRestore($ids, $reason);

        return response()->json([
            'message' => 'Batch restore completed',
            'result' => $result
        ]);
    }

    /**
     * Force delete a record permanently
     */
    public function forceDelete(Request $request, string $model, int $id): JsonResponse
    {
        if (!isset($this->models[$model])) {
            return response()->json([
                'error' => 'Invalid model',
                'available_models' => array_keys($this->models)
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'reason' => 'nullable|string|max:255',
            'confirm' => 'required|boolean|accepted'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 422);
        }

        $modelClass = $this->models[$model];
        $record = $modelClass::withTrashed()->find($id);

        if (!$record) {
            return response()->json([
                'error' => 'Record not found'
            ], 404);
        }

        if (!$record->trashed()) {
            return response()->json([
                'error' => 'Record must be soft deleted first'
            ], 400);
        }

        try {
            $reason = $request->input('reason', 'Force deleted via API');
            $record->forceDeleteWithReason($reason);

            return response()->json([
                'message' => 'Record permanently deleted'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to delete record',
                'details' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get deletion history for a record
     */
    public function history(Request $request, string $model, int $id): JsonResponse
    {
        if (!isset($this->models[$model])) {
            return response()->json([
                'error' => 'Invalid model',
                'available_models' => array_keys($this->models)
            ], 400);
        }

        $modelClass = $this->models[$model];
        $record = $modelClass::withTrashed()->find($id);

        if (!$record) {
            return response()->json([
                'error' => 'Record not found'
            ], 404);
        }

        $history = $record->getDeletionHistory();

        return response()->json([
            'model' => $model,
            'record_id' => $id,
            'history' => $history
        ]);
    }

    /**
     * Check if record can be safely deleted
     */
    public function canDelete(Request $request, string $model, int $id): JsonResponse
    {
        if (!isset($this->models[$model])) {
            return response()->json([
                'error' => 'Invalid model',
                'available_models' => array_keys($this->models)
            ], 400);
        }

        $modelClass = $this->models[$model];
        $record = $modelClass::find($id);

        if (!$record) {
            return response()->json([
                'error' => 'Record not found'
            ], 404);
        }

        $canDelete = $record->canBeDeleted();

        return response()->json([
            'model' => $model,
            'record_id' => $id,
            'can_delete' => $canDelete
        ]);
    }

    /**
     * Safe delete with dependency check
     */
    public function safeDelete(Request $request, string $model, int $id): JsonResponse
    {
        if (!isset($this->models[$model])) {
            return response()->json([
                'error' => 'Invalid model',
                'available_models' => array_keys($this->models)
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'reason' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 422);
        }

        $modelClass = $this->models[$model];
        $record = $modelClass::find($id);

        if (!$record) {
            return response()->json([
                'error' => 'Record not found'
            ], 404);
        }

        $reason = $request->input('reason', 'Deleted via API');
        $result = $record->safeDelete($reason);

        if ($result['success']) {
            return response()->json([
                'message' => $result['message'],
                'deleted_at' => $result['deleted_at']
            ]);
        } else {
            return response()->json([
                'error' => $result['message'],
                'details' => $result['details'] ?? null
            ], 400);
        }
    }
}
