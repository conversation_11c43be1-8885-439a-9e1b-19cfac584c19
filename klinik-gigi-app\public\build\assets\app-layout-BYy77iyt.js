import{r as l,t as fa,j as s,b as Xt,L as je,U as me,d as pa,c as ma}from"./app-EmUGGW4y.js";import{a as se,u as W,d as tt,c as T,e as ha,S as qt,b as ga,B as va,f as qn}from"./button-B8QorGO4.js";import{b as ge,a as A,u as lt,c as de,P as ie,e as xa,d as wa}from"./index-sju6-yWZ.js";import{P as I,d as Zn,R as ba,r as ya}from"./index-CrVQA8Zu.js";import{A as Ca}from"./app-logo-icon-BW5sZeJe.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ea=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],Sa=se("BookOpen",Ea);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ra=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Ma=se("ChevronRight",Ra);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Aa=[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]],Pa=se("ChevronsUpDown",Aa);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _a=[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]],Ta=se("Folder",_a);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Da=[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]],Na=se("LayoutGrid",Da);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oa=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],ja=se("LogOut",Oa);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ia=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],ka=se("PanelLeft",Ia);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const La=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Fa=se("Settings",La);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $a=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Ba=se("X",$a),At=768;function Qn(){const[e,t]=l.useState();return l.useEffect(()=>{const n=window.matchMedia(`(max-width: ${At-1}px)`),r=()=>{t(window.innerWidth<At)};return n.addEventListener("change",r),t(window.innerWidth<At),()=>n.removeEventListener("change",r)},[]),!!e}var Wa=fa[" useId ".trim().toString()]||(()=>{}),Ha=0;function he(e){const[t,n]=l.useState(Wa());return ge(()=>{n(r=>r??String(Ha++))},[e]),e||(t?`radix-${t}`:"")}function te(e){const t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...n)=>t.current?.(...n),[])}function Ga(e,t=globalThis?.document){const n=te(e);l.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Ka="DismissableLayer",Bt="dismissableLayer.update",Ua="dismissableLayer.pointerDownOutside",Va="dismissableLayer.focusOutside",Cn,Jn=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),ut=l.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:a,onInteractOutside:i,onDismiss:c,...p}=e,f=l.useContext(Jn),[d,u]=l.useState(null),m=d?.ownerDocument??globalThis?.document,[,g]=l.useState({}),x=W(t,S=>u(S)),h=Array.from(f.layers),[v]=[...f.layersWithOutsidePointerEventsDisabled].slice(-1),w=h.indexOf(v),b=d?h.indexOf(d):-1,y=f.layersWithOutsidePointerEventsDisabled.size>0,C=b>=w,E=Xa(S=>{const P=S.target,O=[...f.branches].some(D=>D.contains(P));!C||O||(o?.(S),i?.(S),S.defaultPrevented||c?.())},m),M=qa(S=>{const P=S.target;[...f.branches].some(D=>D.contains(P))||(a?.(S),i?.(S),S.defaultPrevented||c?.())},m);return Ga(S=>{b===f.layers.size-1&&(r?.(S),!S.defaultPrevented&&c&&(S.preventDefault(),c()))},m),l.useEffect(()=>{if(d)return n&&(f.layersWithOutsidePointerEventsDisabled.size===0&&(Cn=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),f.layersWithOutsidePointerEventsDisabled.add(d)),f.layers.add(d),En(),()=>{n&&f.layersWithOutsidePointerEventsDisabled.size===1&&(m.body.style.pointerEvents=Cn)}},[d,m,n,f]),l.useEffect(()=>()=>{d&&(f.layers.delete(d),f.layersWithOutsidePointerEventsDisabled.delete(d),En())},[d,f]),l.useEffect(()=>{const S=()=>g({});return document.addEventListener(Bt,S),()=>document.removeEventListener(Bt,S)},[]),s.jsx(I.div,{...p,ref:x,style:{pointerEvents:y?C?"auto":"none":void 0,...e.style},onFocusCapture:A(e.onFocusCapture,M.onFocusCapture),onBlurCapture:A(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:A(e.onPointerDownCapture,E.onPointerDownCapture)})});ut.displayName=Ka;var za="DismissableLayerBranch",Ya=l.forwardRef((e,t)=>{const n=l.useContext(Jn),r=l.useRef(null),o=W(t,r);return l.useEffect(()=>{const a=r.current;if(a)return n.branches.add(a),()=>{n.branches.delete(a)}},[n.branches]),s.jsx(I.div,{...e,ref:o})});Ya.displayName=za;function Xa(e,t=globalThis?.document){const n=te(e),r=l.useRef(!1),o=l.useRef(()=>{});return l.useEffect(()=>{const a=c=>{if(c.target&&!r.current){let p=function(){er(Ua,n,f,{discrete:!0})};const f={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=p,t.addEventListener("click",o.current,{once:!0})):p()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",a),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function qa(e,t=globalThis?.document){const n=te(e),r=l.useRef(!1);return l.useEffect(()=>{const o=a=>{a.target&&!r.current&&er(Va,n,{originalEvent:a},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function En(){const e=new CustomEvent(Bt);document.dispatchEvent(e)}function er(e,t,n,{discrete:r}){const o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Zn(o,a):o.dispatchEvent(a)}var Pt="focusScope.autoFocusOnMount",_t="focusScope.autoFocusOnUnmount",Sn={bubbles:!1,cancelable:!0},Za="FocusScope",Zt=l.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...i}=e,[c,p]=l.useState(null),f=te(o),d=te(a),u=l.useRef(null),m=W(t,h=>p(h)),g=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let h=function(y){if(g.paused||!c)return;const C=y.target;c.contains(C)?u.current=C:ce(u.current,{select:!0})},v=function(y){if(g.paused||!c)return;const C=y.relatedTarget;C!==null&&(c.contains(C)||ce(u.current,{select:!0}))},w=function(y){if(document.activeElement===document.body)for(const E of y)E.removedNodes.length>0&&ce(c)};document.addEventListener("focusin",h),document.addEventListener("focusout",v);const b=new MutationObserver(w);return c&&b.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",h),document.removeEventListener("focusout",v),b.disconnect()}}},[r,c,g.paused]),l.useEffect(()=>{if(c){Mn.add(g);const h=document.activeElement;if(!c.contains(h)){const w=new CustomEvent(Pt,Sn);c.addEventListener(Pt,f),c.dispatchEvent(w),w.defaultPrevented||(Qa(rs(tr(c)),{select:!0}),document.activeElement===h&&ce(c))}return()=>{c.removeEventListener(Pt,f),setTimeout(()=>{const w=new CustomEvent(_t,Sn);c.addEventListener(_t,d),c.dispatchEvent(w),w.defaultPrevented||ce(h??document.body,{select:!0}),c.removeEventListener(_t,d),Mn.remove(g)},0)}}},[c,f,d,g]);const x=l.useCallback(h=>{if(!n&&!r||g.paused)return;const v=h.key==="Tab"&&!h.altKey&&!h.ctrlKey&&!h.metaKey,w=document.activeElement;if(v&&w){const b=h.currentTarget,[y,C]=Ja(b);y&&C?!h.shiftKey&&w===C?(h.preventDefault(),n&&ce(y,{select:!0})):h.shiftKey&&w===y&&(h.preventDefault(),n&&ce(C,{select:!0})):w===b&&h.preventDefault()}},[n,r,g.paused]);return s.jsx(I.div,{tabIndex:-1,...i,ref:m,onKeyDown:x})});Zt.displayName=Za;function Qa(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(ce(r,{select:t}),document.activeElement!==n)return}function Ja(e){const t=tr(e),n=Rn(t,e),r=Rn(t.reverse(),e);return[n,r]}function tr(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Rn(e,t){for(const n of e)if(!es(n,{upTo:t}))return n}function es(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function ts(e){return e instanceof HTMLInputElement&&"select"in e}function ce(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&ts(e)&&t&&e.select()}}var Mn=ns();function ns(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=An(e,t),e.unshift(t)},remove(t){e=An(e,t),e[0]?.resume()}}}function An(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function rs(e){return e.filter(t=>t.tagName!=="A")}var os="Portal",dt=l.forwardRef((e,t)=>{const{container:n,...r}=e,[o,a]=l.useState(!1);ge(()=>a(!0),[]);const i=n||o&&globalThis?.document?.body;return i?ba.createPortal(s.jsx(I.div,{...r,ref:t}),i):null});dt.displayName=os;var Tt=0;function nr(){l.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Pn()),document.body.insertAdjacentElement("beforeend",e[1]??Pn()),Tt++,()=>{Tt===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Tt--}},[])}function Pn(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Q=function(){return Q=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t},Q.apply(this,arguments)};function rr(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function as(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,a;r<o;r++)(a||!(r in t))&&(a||(a=Array.prototype.slice.call(t,0,r)),a[r]=t[r]);return e.concat(a||Array.prototype.slice.call(t))}var Qe="right-scroll-bar-position",Je="width-before-scroll-bar",ss="with-scroll-bars-hidden",is="--removed-body-scroll-bar-size";function Dt(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function cs(e,t){var n=l.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var ls=typeof window<"u"?l.useLayoutEffect:l.useEffect,_n=new WeakMap;function us(e,t){var n=cs(null,function(r){return e.forEach(function(o){return Dt(o,r)})});return ls(function(){var r=_n.get(n);if(r){var o=new Set(r),a=new Set(e),i=n.current;o.forEach(function(c){a.has(c)||Dt(c,null)}),a.forEach(function(c){o.has(c)||Dt(c,i)})}_n.set(n,e)},[e]),n}function ds(e){return e}function fs(e,t){t===void 0&&(t=ds);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(a){var i=t(a,r);return n.push(i),function(){n=n.filter(function(c){return c!==i})}},assignSyncMedium:function(a){for(r=!0;n.length;){var i=n;n=[],i.forEach(a)}n={push:function(c){return a(c)},filter:function(){return n}}},assignMedium:function(a){r=!0;var i=[];if(n.length){var c=n;n=[],c.forEach(a),i=n}var p=function(){var d=i;i=[],d.forEach(a)},f=function(){return Promise.resolve().then(p)};f(),n={push:function(d){i.push(d),f()},filter:function(d){return i=i.filter(d),n}}}};return o}function ps(e){e===void 0&&(e={});var t=fs(null);return t.options=Q({async:!0,ssr:!1},e),t}var or=function(e){var t=e.sideCar,n=rr(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return l.createElement(r,Q({},n))};or.isSideCarExport=!0;function ms(e,t){return e.useMedium(t),or}var ar=ps(),Nt=function(){},ft=l.forwardRef(function(e,t){var n=l.useRef(null),r=l.useState({onScrollCapture:Nt,onWheelCapture:Nt,onTouchMoveCapture:Nt}),o=r[0],a=r[1],i=e.forwardProps,c=e.children,p=e.className,f=e.removeScrollBar,d=e.enabled,u=e.shards,m=e.sideCar,g=e.noRelative,x=e.noIsolation,h=e.inert,v=e.allowPinchZoom,w=e.as,b=w===void 0?"div":w,y=e.gapMode,C=rr(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=m,M=us([n,t]),S=Q(Q({},C),o);return l.createElement(l.Fragment,null,d&&l.createElement(E,{sideCar:ar,removeScrollBar:f,shards:u,noRelative:g,noIsolation:x,inert:h,setCallbacks:a,allowPinchZoom:!!v,lockRef:n,gapMode:y}),i?l.cloneElement(l.Children.only(c),Q(Q({},S),{ref:M})):l.createElement(b,Q({},S,{className:p,ref:M}),c))});ft.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};ft.classNames={fullWidth:Je,zeroRight:Qe};var hs=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function gs(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=hs();return t&&e.setAttribute("nonce",t),e}function vs(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function xs(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var ws=function(){var e=0,t=null;return{add:function(n){e==0&&(t=gs())&&(vs(t,n),xs(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},bs=function(){var e=ws();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},sr=function(){var e=bs(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},ys={left:0,top:0,right:0,gap:0},Ot=function(e){return parseInt(e||"",10)||0},Cs=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Ot(n),Ot(r),Ot(o)]},Es=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return ys;var t=Cs(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Ss=sr(),Se="data-scroll-locked",Rs=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,c=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(ss,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(c,"px ").concat(r,`;
  }
  body[`).concat(Se,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(a,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Qe,` {
    right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(Je,` {
    margin-right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(Qe," .").concat(Qe,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Je," .").concat(Je,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Se,`] {
    `).concat(is,": ").concat(c,`px;
  }
`)},Tn=function(){var e=parseInt(document.body.getAttribute(Se)||"0",10);return isFinite(e)?e:0},Ms=function(){l.useEffect(function(){return document.body.setAttribute(Se,(Tn()+1).toString()),function(){var e=Tn()-1;e<=0?document.body.removeAttribute(Se):document.body.setAttribute(Se,e.toString())}},[])},As=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;Ms();var a=l.useMemo(function(){return Es(o)},[o]);return l.createElement(Ss,{styles:Rs(a,!t,o,n?"":"!important")})},Wt=!1;if(typeof window<"u")try{var ze=Object.defineProperty({},"passive",{get:function(){return Wt=!0,!0}});window.addEventListener("test",ze,ze),window.removeEventListener("test",ze,ze)}catch{Wt=!1}var ye=Wt?{passive:!1}:!1,Ps=function(e){return e.tagName==="TEXTAREA"},ir=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Ps(e)&&n[t]==="visible")},_s=function(e){return ir(e,"overflowY")},Ts=function(e){return ir(e,"overflowX")},Dn=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=cr(e,r);if(o){var a=lr(e,r),i=a[1],c=a[2];if(i>c)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Ds=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Ns=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},cr=function(e,t){return e==="v"?_s(t):Ts(t)},lr=function(e,t){return e==="v"?Ds(t):Ns(t)},Os=function(e,t){return e==="h"&&t==="rtl"?-1:1},js=function(e,t,n,r,o){var a=Os(e,window.getComputedStyle(t).direction),i=a*r,c=n.target,p=t.contains(c),f=!1,d=i>0,u=0,m=0;do{if(!c)break;var g=lr(e,c),x=g[0],h=g[1],v=g[2],w=h-v-a*x;(x||w)&&cr(e,c)&&(u+=w,m+=x);var b=c.parentNode;c=b&&b.nodeType===Node.DOCUMENT_FRAGMENT_NODE?b.host:b}while(!p&&c!==document.body||p&&(t.contains(c)||t===c));return(d&&Math.abs(u)<1||!d&&Math.abs(m)<1)&&(f=!0),f},Ye=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Nn=function(e){return[e.deltaX,e.deltaY]},On=function(e){return e&&"current"in e?e.current:e},Is=function(e,t){return e[0]===t[0]&&e[1]===t[1]},ks=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Ls=0,Ce=[];function Fs(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(Ls++)[0],a=l.useState(sr)[0],i=l.useRef(e);l.useEffect(function(){i.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var h=as([e.lockRef.current],(e.shards||[]).map(On),!0).filter(Boolean);return h.forEach(function(v){return v.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),h.forEach(function(v){return v.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=l.useCallback(function(h,v){if("touches"in h&&h.touches.length===2||h.type==="wheel"&&h.ctrlKey)return!i.current.allowPinchZoom;var w=Ye(h),b=n.current,y="deltaX"in h?h.deltaX:b[0]-w[0],C="deltaY"in h?h.deltaY:b[1]-w[1],E,M=h.target,S=Math.abs(y)>Math.abs(C)?"h":"v";if("touches"in h&&S==="h"&&M.type==="range")return!1;var P=Dn(S,M);if(!P)return!0;if(P?E=S:(E=S==="v"?"h":"v",P=Dn(S,M)),!P)return!1;if(!r.current&&"changedTouches"in h&&(y||C)&&(r.current=E),!E)return!0;var O=r.current||E;return js(O,v,h,O==="h"?y:C)},[]),p=l.useCallback(function(h){var v=h;if(!(!Ce.length||Ce[Ce.length-1]!==a)){var w="deltaY"in v?Nn(v):Ye(v),b=t.current.filter(function(E){return E.name===v.type&&(E.target===v.target||v.target===E.shadowParent)&&Is(E.delta,w)})[0];if(b&&b.should){v.cancelable&&v.preventDefault();return}if(!b){var y=(i.current.shards||[]).map(On).filter(Boolean).filter(function(E){return E.contains(v.target)}),C=y.length>0?c(v,y[0]):!i.current.noIsolation;C&&v.cancelable&&v.preventDefault()}}},[]),f=l.useCallback(function(h,v,w,b){var y={name:h,delta:v,target:w,should:b,shadowParent:$s(w)};t.current.push(y),setTimeout(function(){t.current=t.current.filter(function(C){return C!==y})},1)},[]),d=l.useCallback(function(h){n.current=Ye(h),r.current=void 0},[]),u=l.useCallback(function(h){f(h.type,Nn(h),h.target,c(h,e.lockRef.current))},[]),m=l.useCallback(function(h){f(h.type,Ye(h),h.target,c(h,e.lockRef.current))},[]);l.useEffect(function(){return Ce.push(a),e.setCallbacks({onScrollCapture:u,onWheelCapture:u,onTouchMoveCapture:m}),document.addEventListener("wheel",p,ye),document.addEventListener("touchmove",p,ye),document.addEventListener("touchstart",d,ye),function(){Ce=Ce.filter(function(h){return h!==a}),document.removeEventListener("wheel",p,ye),document.removeEventListener("touchmove",p,ye),document.removeEventListener("touchstart",d,ye)}},[]);var g=e.removeScrollBar,x=e.inert;return l.createElement(l.Fragment,null,x?l.createElement(a,{styles:ks(o)}):null,g?l.createElement(As,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function $s(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Bs=ms(ar,Fs);var Qt=l.forwardRef(function(e,t){return l.createElement(ft,Q({},e,{ref:t,sideCar:Bs}))});Qt.classNames=ft.classNames;var Ws=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Ee=new WeakMap,Xe=new WeakMap,qe={},jt=0,ur=function(e){return e&&(e.host||ur(e.parentNode))},Hs=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=ur(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Gs=function(e,t,n,r){var o=Hs(t,Array.isArray(e)?e:[e]);qe[n]||(qe[n]=new WeakMap);var a=qe[n],i=[],c=new Set,p=new Set(o),f=function(u){!u||c.has(u)||(c.add(u),f(u.parentNode))};o.forEach(f);var d=function(u){!u||p.has(u)||Array.prototype.forEach.call(u.children,function(m){if(c.has(m))d(m);else try{var g=m.getAttribute(r),x=g!==null&&g!=="false",h=(Ee.get(m)||0)+1,v=(a.get(m)||0)+1;Ee.set(m,h),a.set(m,v),i.push(m),h===1&&x&&Xe.set(m,!0),v===1&&m.setAttribute(n,"true"),x||m.setAttribute(r,"true")}catch(w){console.error("aria-hidden: cannot operate on ",m,w)}})};return d(t),c.clear(),jt++,function(){i.forEach(function(u){var m=Ee.get(u)-1,g=a.get(u)-1;Ee.set(u,m),a.set(u,g),m||(Xe.has(u)||u.removeAttribute(r),Xe.delete(u)),g||u.removeAttribute(n)}),jt--,jt||(Ee=new WeakMap,Ee=new WeakMap,Xe=new WeakMap,qe={})}},dr=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=Ws(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),Gs(r,o,n,"aria-hidden")):function(){return null}},pt="Dialog",[fr,Gd]=de(pt),[Ks,q]=fr(pt),pr=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:i=!0}=e,c=l.useRef(null),p=l.useRef(null),[f,d]=lt({prop:r,defaultProp:o??!1,onChange:a,caller:pt});return s.jsx(Ks,{scope:t,triggerRef:c,contentRef:p,contentId:he(),titleId:he(),descriptionId:he(),open:f,onOpenChange:d,onOpenToggle:l.useCallback(()=>d(u=>!u),[d]),modal:i,children:n})};pr.displayName=pt;var mr="DialogTrigger",hr=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=q(mr,n),a=W(t,o.triggerRef);return s.jsx(I.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":tn(o.open),...r,ref:a,onClick:A(e.onClick,o.onOpenToggle)})});hr.displayName=mr;var Jt="DialogPortal",[Us,gr]=fr(Jt,{forceMount:void 0}),vr=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=q(Jt,t);return s.jsx(Us,{scope:t,forceMount:n,children:l.Children.map(r,i=>s.jsx(ie,{present:n||a.open,children:s.jsx(dt,{asChild:!0,container:o,children:i})}))})};vr.displayName=Jt;var nt="DialogOverlay",xr=l.forwardRef((e,t)=>{const n=gr(nt,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=q(nt,e.__scopeDialog);return a.modal?s.jsx(ie,{present:r||a.open,children:s.jsx(zs,{...o,ref:t})}):null});xr.displayName=nt;var Vs=tt("DialogOverlay.RemoveScroll"),zs=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=q(nt,n);return s.jsx(Qt,{as:Vs,allowPinchZoom:!0,shards:[o.contentRef],children:s.jsx(I.div,{"data-state":tn(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),ve="DialogContent",wr=l.forwardRef((e,t)=>{const n=gr(ve,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=q(ve,e.__scopeDialog);return s.jsx(ie,{present:r||a.open,children:a.modal?s.jsx(Ys,{...o,ref:t}):s.jsx(Xs,{...o,ref:t})})});wr.displayName=ve;var Ys=l.forwardRef((e,t)=>{const n=q(ve,e.__scopeDialog),r=l.useRef(null),o=W(t,n.contentRef,r);return l.useEffect(()=>{const a=r.current;if(a)return dr(a)},[]),s.jsx(br,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:A(e.onCloseAutoFocus,a=>{a.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:A(e.onPointerDownOutside,a=>{const i=a.detail.originalEvent,c=i.button===0&&i.ctrlKey===!0;(i.button===2||c)&&a.preventDefault()}),onFocusOutside:A(e.onFocusOutside,a=>a.preventDefault())})}),Xs=l.forwardRef((e,t)=>{const n=q(ve,e.__scopeDialog),r=l.useRef(!1),o=l.useRef(!1);return s.jsx(br,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{e.onCloseAutoFocus?.(a),a.defaultPrevented||(r.current||n.triggerRef.current?.focus(),a.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:a=>{e.onInteractOutside?.(a),a.defaultPrevented||(r.current=!0,a.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const i=a.target;n.triggerRef.current?.contains(i)&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&o.current&&a.preventDefault()}})}),br=l.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...i}=e,c=q(ve,n),p=l.useRef(null),f=W(t,p);return nr(),s.jsxs(s.Fragment,{children:[s.jsx(Zt,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:s.jsx(ut,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":tn(c.open),...i,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),s.jsxs(s.Fragment,{children:[s.jsx(qs,{titleId:c.titleId}),s.jsx(Qs,{contentRef:p,descriptionId:c.descriptionId})]})]})}),en="DialogTitle",yr=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=q(en,n);return s.jsx(I.h2,{id:o.titleId,...r,ref:t})});yr.displayName=en;var Cr="DialogDescription",Er=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=q(Cr,n);return s.jsx(I.p,{id:o.descriptionId,...r,ref:t})});Er.displayName=Cr;var Sr="DialogClose",Rr=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=q(Sr,n);return s.jsx(I.button,{type:"button",...r,ref:t,onClick:A(e.onClick,()=>o.onOpenChange(!1))})});Rr.displayName=Sr;function tn(e){return e?"open":"closed"}var Mr="DialogTitleWarning",[Kd,Ar]=xa(Mr,{contentName:ve,titleName:en,docsSlug:"dialog"}),qs=({titleId:e})=>{const t=Ar(Mr),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return l.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Zs="DialogDescriptionWarning",Qs=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Ar(Zs).contentName}}.`;return l.useEffect(()=>{const o=e.current?.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Js=pr,Ud=hr,ei=vr,ti=xr,ni=wr,ri=yr,oi=Er,ai=Rr;function si({...e}){return s.jsx(Js,{"data-slot":"sheet",...e})}function ii({...e}){return s.jsx(ei,{"data-slot":"sheet-portal",...e})}function ci({className:e,...t}){return s.jsx(ti,{"data-slot":"sheet-overlay",className:T("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",e),...t})}function li({className:e,children:t,side:n="right",...r}){return s.jsxs(ii,{children:[s.jsx(ci,{}),s.jsxs(ni,{"data-slot":"sheet-content",className:T("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",n==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",n==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",n==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",n==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...r,children:[t,s.jsxs(ai,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[s.jsx(Ba,{className:"size-4"}),s.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function ui({className:e,...t}){return s.jsx("div",{"data-slot":"sheet-header",className:T("flex flex-col gap-1.5 p-4",e),...t})}function di({className:e,...t}){return s.jsx(ri,{"data-slot":"sheet-title",className:T("text-foreground font-semibold",e),...t})}function fi({className:e,...t}){return s.jsx(oi,{"data-slot":"sheet-description",className:T("text-muted-foreground text-sm",e),...t})}const pi=["top","right","bottom","left"],le=Math.min,U=Math.max,rt=Math.round,Ze=Math.floor,ee=e=>({x:e,y:e}),mi={left:"right",right:"left",bottom:"top",top:"bottom"},hi={start:"end",end:"start"};function Ht(e,t,n){return U(e,le(t,n))}function oe(e,t){return typeof e=="function"?e(t):e}function ae(e){return e.split("-")[0]}function Pe(e){return e.split("-")[1]}function nn(e){return e==="x"?"y":"x"}function rn(e){return e==="y"?"height":"width"}const gi=new Set(["top","bottom"]);function J(e){return gi.has(ae(e))?"y":"x"}function on(e){return nn(J(e))}function vi(e,t,n){n===void 0&&(n=!1);const r=Pe(e),o=on(e),a=rn(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=ot(i)),[i,ot(i)]}function xi(e){const t=ot(e);return[Gt(e),t,Gt(t)]}function Gt(e){return e.replace(/start|end/g,t=>hi[t])}const jn=["left","right"],In=["right","left"],wi=["top","bottom"],bi=["bottom","top"];function yi(e,t,n){switch(e){case"top":case"bottom":return n?t?In:jn:t?jn:In;case"left":case"right":return t?wi:bi;default:return[]}}function Ci(e,t,n,r){const o=Pe(e);let a=yi(ae(e),n==="start",r);return o&&(a=a.map(i=>i+"-"+o),t&&(a=a.concat(a.map(Gt)))),a}function ot(e){return e.replace(/left|right|bottom|top/g,t=>mi[t])}function Ei(e){return{top:0,right:0,bottom:0,left:0,...e}}function Pr(e){return typeof e!="number"?Ei(e):{top:e,right:e,bottom:e,left:e}}function at(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function kn(e,t,n){let{reference:r,floating:o}=e;const a=J(t),i=on(t),c=rn(i),p=ae(t),f=a==="y",d=r.x+r.width/2-o.width/2,u=r.y+r.height/2-o.height/2,m=r[c]/2-o[c]/2;let g;switch(p){case"top":g={x:d,y:r.y-o.height};break;case"bottom":g={x:d,y:r.y+r.height};break;case"right":g={x:r.x+r.width,y:u};break;case"left":g={x:r.x-o.width,y:u};break;default:g={x:r.x,y:r.y}}switch(Pe(t)){case"start":g[i]-=m*(n&&f?-1:1);break;case"end":g[i]+=m*(n&&f?-1:1);break}return g}const Si=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:a=[],platform:i}=n,c=a.filter(Boolean),p=await(i.isRTL==null?void 0:i.isRTL(t));let f=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:u}=kn(f,r,p),m=r,g={},x=0;for(let h=0;h<c.length;h++){const{name:v,fn:w}=c[h],{x:b,y,data:C,reset:E}=await w({x:d,y:u,initialPlacement:r,placement:m,strategy:o,middlewareData:g,rects:f,platform:i,elements:{reference:e,floating:t}});d=b??d,u=y??u,g={...g,[v]:{...g[v],...C}},E&&x<=50&&(x++,typeof E=="object"&&(E.placement&&(m=E.placement),E.rects&&(f=E.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):E.rects),{x:d,y:u}=kn(f,m,p)),h=-1)}return{x:d,y:u,placement:m,strategy:o,middlewareData:g}};async function Ie(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:a,rects:i,elements:c,strategy:p}=e,{boundary:f="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:m=!1,padding:g=0}=oe(t,e),x=Pr(g),v=c[m?u==="floating"?"reference":"floating":u],w=at(await a.getClippingRect({element:(n=await(a.isElement==null?void 0:a.isElement(v)))==null||n?v:v.contextElement||await(a.getDocumentElement==null?void 0:a.getDocumentElement(c.floating)),boundary:f,rootBoundary:d,strategy:p})),b=u==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,y=await(a.getOffsetParent==null?void 0:a.getOffsetParent(c.floating)),C=await(a.isElement==null?void 0:a.isElement(y))?await(a.getScale==null?void 0:a.getScale(y))||{x:1,y:1}:{x:1,y:1},E=at(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:b,offsetParent:y,strategy:p}):b);return{top:(w.top-E.top+x.top)/C.y,bottom:(E.bottom-w.bottom+x.bottom)/C.y,left:(w.left-E.left+x.left)/C.x,right:(E.right-w.right+x.right)/C.x}}const Ri=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:a,platform:i,elements:c,middlewareData:p}=t,{element:f,padding:d=0}=oe(e,t)||{};if(f==null)return{};const u=Pr(d),m={x:n,y:r},g=on(o),x=rn(g),h=await i.getDimensions(f),v=g==="y",w=v?"top":"left",b=v?"bottom":"right",y=v?"clientHeight":"clientWidth",C=a.reference[x]+a.reference[g]-m[g]-a.floating[x],E=m[g]-a.reference[g],M=await(i.getOffsetParent==null?void 0:i.getOffsetParent(f));let S=M?M[y]:0;(!S||!await(i.isElement==null?void 0:i.isElement(M)))&&(S=c.floating[y]||a.floating[x]);const P=C/2-E/2,O=S/2-h[x]/2-1,D=le(u[w],O),L=le(u[b],O),F=D,k=S-h[x]-L,j=S/2-h[x]/2+P,H=Ht(F,j,k),N=!p.arrow&&Pe(o)!=null&&j!==H&&a.reference[x]/2-(j<F?D:L)-h[x]/2<0,$=N?j<F?j-F:j-k:0;return{[g]:m[g]+$,data:{[g]:H,centerOffset:j-H-$,...N&&{alignmentOffset:$}},reset:N}}}),Mi=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:a,rects:i,initialPlacement:c,platform:p,elements:f}=t,{mainAxis:d=!0,crossAxis:u=!0,fallbackPlacements:m,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:h=!0,...v}=oe(e,t);if((n=a.arrow)!=null&&n.alignmentOffset)return{};const w=ae(o),b=J(c),y=ae(c)===c,C=await(p.isRTL==null?void 0:p.isRTL(f.floating)),E=m||(y||!h?[ot(c)]:xi(c)),M=x!=="none";!m&&M&&E.push(...Ci(c,h,x,C));const S=[c,...E],P=await Ie(t,v),O=[];let D=((r=a.flip)==null?void 0:r.overflows)||[];if(d&&O.push(P[w]),u){const j=vi(o,i,C);O.push(P[j[0]],P[j[1]])}if(D=[...D,{placement:o,overflows:O}],!O.every(j=>j<=0)){var L,F;const j=(((L=a.flip)==null?void 0:L.index)||0)+1,H=S[j];if(H&&(!(u==="alignment"?b!==J(H):!1)||D.every(_=>_.overflows[0]>0&&J(_.placement)===b)))return{data:{index:j,overflows:D},reset:{placement:H}};let N=(F=D.filter($=>$.overflows[0]<=0).sort(($,_)=>$.overflows[1]-_.overflows[1])[0])==null?void 0:F.placement;if(!N)switch(g){case"bestFit":{var k;const $=(k=D.filter(_=>{if(M){const R=J(_.placement);return R===b||R==="y"}return!0}).map(_=>[_.placement,_.overflows.filter(R=>R>0).reduce((R,G)=>R+G,0)]).sort((_,R)=>_[1]-R[1])[0])==null?void 0:k[0];$&&(N=$);break}case"initialPlacement":N=c;break}if(o!==N)return{reset:{placement:N}}}return{}}}};function Ln(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Fn(e){return pi.some(t=>e[t]>=0)}const Ai=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=oe(e,t);switch(r){case"referenceHidden":{const a=await Ie(t,{...o,elementContext:"reference"}),i=Ln(a,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:Fn(i)}}}case"escaped":{const a=await Ie(t,{...o,altBoundary:!0}),i=Ln(a,n.floating);return{data:{escapedOffsets:i,escaped:Fn(i)}}}default:return{}}}}},_r=new Set(["left","top"]);async function Pi(e,t){const{placement:n,platform:r,elements:o}=e,a=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=ae(n),c=Pe(n),p=J(n)==="y",f=_r.has(i)?-1:1,d=a&&p?-1:1,u=oe(t,e);let{mainAxis:m,crossAxis:g,alignmentAxis:x}=typeof u=="number"?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return c&&typeof x=="number"&&(g=c==="end"?x*-1:x),p?{x:g*d,y:m*f}:{x:m*f,y:g*d}}const _i=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:a,placement:i,middlewareData:c}=t,p=await Pi(t,e);return i===((n=c.offset)==null?void 0:n.placement)&&(r=c.arrow)!=null&&r.alignmentOffset?{}:{x:o+p.x,y:a+p.y,data:{...p,placement:i}}}}},Ti=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:i=!1,limiter:c={fn:v=>{let{x:w,y:b}=v;return{x:w,y:b}}},...p}=oe(e,t),f={x:n,y:r},d=await Ie(t,p),u=J(ae(o)),m=nn(u);let g=f[m],x=f[u];if(a){const v=m==="y"?"top":"left",w=m==="y"?"bottom":"right",b=g+d[v],y=g-d[w];g=Ht(b,g,y)}if(i){const v=u==="y"?"top":"left",w=u==="y"?"bottom":"right",b=x+d[v],y=x-d[w];x=Ht(b,x,y)}const h=c.fn({...t,[m]:g,[u]:x});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[m]:a,[u]:i}}}}}},Di=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:a,middlewareData:i}=t,{offset:c=0,mainAxis:p=!0,crossAxis:f=!0}=oe(e,t),d={x:n,y:r},u=J(o),m=nn(u);let g=d[m],x=d[u];const h=oe(c,t),v=typeof h=="number"?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(p){const y=m==="y"?"height":"width",C=a.reference[m]-a.floating[y]+v.mainAxis,E=a.reference[m]+a.reference[y]-v.mainAxis;g<C?g=C:g>E&&(g=E)}if(f){var w,b;const y=m==="y"?"width":"height",C=_r.has(ae(o)),E=a.reference[u]-a.floating[y]+(C&&((w=i.offset)==null?void 0:w[u])||0)+(C?0:v.crossAxis),M=a.reference[u]+a.reference[y]+(C?0:((b=i.offset)==null?void 0:b[u])||0)-(C?v.crossAxis:0);x<E?x=E:x>M&&(x=M)}return{[m]:g,[u]:x}}}},Ni=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:a,platform:i,elements:c}=t,{apply:p=()=>{},...f}=oe(e,t),d=await Ie(t,f),u=ae(o),m=Pe(o),g=J(o)==="y",{width:x,height:h}=a.floating;let v,w;u==="top"||u==="bottom"?(v=u,w=m===(await(i.isRTL==null?void 0:i.isRTL(c.floating))?"start":"end")?"left":"right"):(w=u,v=m==="end"?"top":"bottom");const b=h-d.top-d.bottom,y=x-d.left-d.right,C=le(h-d[v],b),E=le(x-d[w],y),M=!t.middlewareData.shift;let S=C,P=E;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(P=y),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(S=b),M&&!m){const D=U(d.left,0),L=U(d.right,0),F=U(d.top,0),k=U(d.bottom,0);g?P=x-2*(D!==0||L!==0?D+L:U(d.left,d.right)):S=h-2*(F!==0||k!==0?F+k:U(d.top,d.bottom))}await p({...t,availableWidth:P,availableHeight:S});const O=await i.getDimensions(c.floating);return x!==O.width||h!==O.height?{reset:{rects:!0}}:{}}}};function mt(){return typeof window<"u"}function _e(e){return Tr(e)?(e.nodeName||"").toLowerCase():"#document"}function V(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function re(e){var t;return(t=(Tr(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Tr(e){return mt()?e instanceof Node||e instanceof V(e).Node:!1}function Y(e){return mt()?e instanceof Element||e instanceof V(e).Element:!1}function ne(e){return mt()?e instanceof HTMLElement||e instanceof V(e).HTMLElement:!1}function $n(e){return!mt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof V(e).ShadowRoot}const Oi=new Set(["inline","contents"]);function Be(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=X(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!Oi.has(o)}const ji=new Set(["table","td","th"]);function Ii(e){return ji.has(_e(e))}const ki=[":popover-open",":modal"];function ht(e){return ki.some(t=>{try{return e.matches(t)}catch{return!1}})}const Li=["transform","translate","scale","rotate","perspective"],Fi=["transform","translate","scale","rotate","perspective","filter"],$i=["paint","layout","strict","content"];function an(e){const t=sn(),n=Y(e)?X(e):e;return Li.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||Fi.some(r=>(n.willChange||"").includes(r))||$i.some(r=>(n.contain||"").includes(r))}function Bi(e){let t=ue(e);for(;ne(t)&&!Me(t);){if(an(t))return t;if(ht(t))return null;t=ue(t)}return null}function sn(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const Wi=new Set(["html","body","#document"]);function Me(e){return Wi.has(_e(e))}function X(e){return V(e).getComputedStyle(e)}function gt(e){return Y(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ue(e){if(_e(e)==="html")return e;const t=e.assignedSlot||e.parentNode||$n(e)&&e.host||re(e);return $n(t)?t.host:t}function Dr(e){const t=ue(e);return Me(t)?e.ownerDocument?e.ownerDocument.body:e.body:ne(t)&&Be(t)?t:Dr(t)}function ke(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Dr(e),a=o===((r=e.ownerDocument)==null?void 0:r.body),i=V(o);if(a){const c=Kt(i);return t.concat(i,i.visualViewport||[],Be(o)?o:[],c&&n?ke(c):[])}return t.concat(o,ke(o,[],n))}function Kt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Nr(e){const t=X(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=ne(e),a=o?e.offsetWidth:n,i=o?e.offsetHeight:r,c=rt(n)!==a||rt(r)!==i;return c&&(n=a,r=i),{width:n,height:r,$:c}}function cn(e){return Y(e)?e:e.contextElement}function Re(e){const t=cn(e);if(!ne(t))return ee(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:a}=Nr(t);let i=(a?rt(n.width):n.width)/r,c=(a?rt(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!c||!Number.isFinite(c))&&(c=1),{x:i,y:c}}const Hi=ee(0);function Or(e){const t=V(e);return!sn()||!t.visualViewport?Hi:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Gi(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==V(e)?!1:t}function xe(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),a=cn(e);let i=ee(1);t&&(r?Y(r)&&(i=Re(r)):i=Re(e));const c=Gi(a,n,r)?Or(a):ee(0);let p=(o.left+c.x)/i.x,f=(o.top+c.y)/i.y,d=o.width/i.x,u=o.height/i.y;if(a){const m=V(a),g=r&&Y(r)?V(r):r;let x=m,h=Kt(x);for(;h&&r&&g!==x;){const v=Re(h),w=h.getBoundingClientRect(),b=X(h),y=w.left+(h.clientLeft+parseFloat(b.paddingLeft))*v.x,C=w.top+(h.clientTop+parseFloat(b.paddingTop))*v.y;p*=v.x,f*=v.y,d*=v.x,u*=v.y,p+=y,f+=C,x=V(h),h=Kt(x)}}return at({width:d,height:u,x:p,y:f})}function ln(e,t){const n=gt(e).scrollLeft;return t?t.left+n:xe(re(e)).left+n}function jr(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:ln(e,r)),a=r.top+t.scrollTop;return{x:o,y:a}}function Ki(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const a=o==="fixed",i=re(r),c=t?ht(t.floating):!1;if(r===i||c&&a)return n;let p={scrollLeft:0,scrollTop:0},f=ee(1);const d=ee(0),u=ne(r);if((u||!u&&!a)&&((_e(r)!=="body"||Be(i))&&(p=gt(r)),ne(r))){const g=xe(r);f=Re(r),d.x=g.x+r.clientLeft,d.y=g.y+r.clientTop}const m=i&&!u&&!a?jr(i,p,!0):ee(0);return{width:n.width*f.x,height:n.height*f.y,x:n.x*f.x-p.scrollLeft*f.x+d.x+m.x,y:n.y*f.y-p.scrollTop*f.y+d.y+m.y}}function Ui(e){return Array.from(e.getClientRects())}function Vi(e){const t=re(e),n=gt(e),r=e.ownerDocument.body,o=U(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),a=U(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+ln(e);const c=-n.scrollTop;return X(r).direction==="rtl"&&(i+=U(t.clientWidth,r.clientWidth)-o),{width:o,height:a,x:i,y:c}}function zi(e,t){const n=V(e),r=re(e),o=n.visualViewport;let a=r.clientWidth,i=r.clientHeight,c=0,p=0;if(o){a=o.width,i=o.height;const f=sn();(!f||f&&t==="fixed")&&(c=o.offsetLeft,p=o.offsetTop)}return{width:a,height:i,x:c,y:p}}const Yi=new Set(["absolute","fixed"]);function Xi(e,t){const n=xe(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,a=ne(e)?Re(e):ee(1),i=e.clientWidth*a.x,c=e.clientHeight*a.y,p=o*a.x,f=r*a.y;return{width:i,height:c,x:p,y:f}}function Bn(e,t,n){let r;if(t==="viewport")r=zi(e,n);else if(t==="document")r=Vi(re(e));else if(Y(t))r=Xi(t,n);else{const o=Or(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return at(r)}function Ir(e,t){const n=ue(e);return n===t||!Y(n)||Me(n)?!1:X(n).position==="fixed"||Ir(n,t)}function qi(e,t){const n=t.get(e);if(n)return n;let r=ke(e,[],!1).filter(c=>Y(c)&&_e(c)!=="body"),o=null;const a=X(e).position==="fixed";let i=a?ue(e):e;for(;Y(i)&&!Me(i);){const c=X(i),p=an(i);!p&&c.position==="fixed"&&(o=null),(a?!p&&!o:!p&&c.position==="static"&&!!o&&Yi.has(o.position)||Be(i)&&!p&&Ir(e,i))?r=r.filter(d=>d!==i):o=c,i=ue(i)}return t.set(e,r),r}function Zi(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?ht(t)?[]:qi(t,this._c):[].concat(n),r],c=i[0],p=i.reduce((f,d)=>{const u=Bn(t,d,o);return f.top=U(u.top,f.top),f.right=le(u.right,f.right),f.bottom=le(u.bottom,f.bottom),f.left=U(u.left,f.left),f},Bn(t,c,o));return{width:p.right-p.left,height:p.bottom-p.top,x:p.left,y:p.top}}function Qi(e){const{width:t,height:n}=Nr(e);return{width:t,height:n}}function Ji(e,t,n){const r=ne(t),o=re(t),a=n==="fixed",i=xe(e,!0,a,t);let c={scrollLeft:0,scrollTop:0};const p=ee(0);function f(){p.x=ln(o)}if(r||!r&&!a)if((_e(t)!=="body"||Be(o))&&(c=gt(t)),r){const g=xe(t,!0,a,t);p.x=g.x+t.clientLeft,p.y=g.y+t.clientTop}else o&&f();a&&!r&&o&&f();const d=o&&!r&&!a?jr(o,c):ee(0),u=i.left+c.scrollLeft-p.x-d.x,m=i.top+c.scrollTop-p.y-d.y;return{x:u,y:m,width:i.width,height:i.height}}function It(e){return X(e).position==="static"}function Wn(e,t){if(!ne(e)||X(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return re(e)===n&&(n=n.ownerDocument.body),n}function kr(e,t){const n=V(e);if(ht(e))return n;if(!ne(e)){let o=ue(e);for(;o&&!Me(o);){if(Y(o)&&!It(o))return o;o=ue(o)}return n}let r=Wn(e,t);for(;r&&Ii(r)&&It(r);)r=Wn(r,t);return r&&Me(r)&&It(r)&&!an(r)?n:r||Bi(e)||n}const ec=async function(e){const t=this.getOffsetParent||kr,n=this.getDimensions,r=await n(e.floating);return{reference:Ji(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function tc(e){return X(e).direction==="rtl"}const nc={convertOffsetParentRelativeRectToViewportRelativeRect:Ki,getDocumentElement:re,getClippingRect:Zi,getOffsetParent:kr,getElementRects:ec,getClientRects:Ui,getDimensions:Qi,getScale:Re,isElement:Y,isRTL:tc};function Lr(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function rc(e,t){let n=null,r;const o=re(e);function a(){var c;clearTimeout(r),(c=n)==null||c.disconnect(),n=null}function i(c,p){c===void 0&&(c=!1),p===void 0&&(p=1),a();const f=e.getBoundingClientRect(),{left:d,top:u,width:m,height:g}=f;if(c||t(),!m||!g)return;const x=Ze(u),h=Ze(o.clientWidth-(d+m)),v=Ze(o.clientHeight-(u+g)),w=Ze(d),y={rootMargin:-x+"px "+-h+"px "+-v+"px "+-w+"px",threshold:U(0,le(1,p))||1};let C=!0;function E(M){const S=M[0].intersectionRatio;if(S!==p){if(!C)return i();S?i(!1,S):r=setTimeout(()=>{i(!1,1e-7)},1e3)}S===1&&!Lr(f,e.getBoundingClientRect())&&i(),C=!1}try{n=new IntersectionObserver(E,{...y,root:o.ownerDocument})}catch{n=new IntersectionObserver(E,y)}n.observe(e)}return i(!0),a}function oc(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:p=!1}=r,f=cn(e),d=o||a?[...f?ke(f):[],...ke(t)]:[];d.forEach(w=>{o&&w.addEventListener("scroll",n,{passive:!0}),a&&w.addEventListener("resize",n)});const u=f&&c?rc(f,n):null;let m=-1,g=null;i&&(g=new ResizeObserver(w=>{let[b]=w;b&&b.target===f&&g&&(g.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var y;(y=g)==null||y.observe(t)})),n()}),f&&!p&&g.observe(f),g.observe(t));let x,h=p?xe(e):null;p&&v();function v(){const w=xe(e);h&&!Lr(h,w)&&n(),h=w,x=requestAnimationFrame(v)}return n(),()=>{var w;d.forEach(b=>{o&&b.removeEventListener("scroll",n),a&&b.removeEventListener("resize",n)}),u?.(),(w=g)==null||w.disconnect(),g=null,p&&cancelAnimationFrame(x)}}const ac=_i,sc=Ti,ic=Mi,cc=Ni,lc=Ai,Hn=Ri,uc=Di,dc=(e,t,n)=>{const r=new Map,o={platform:nc,...n},a={...o.platform,_c:r};return Si(e,t,{...o,platform:a})};var fc=typeof document<"u",pc=function(){},et=fc?l.useLayoutEffect:pc;function st(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!st(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const a=o[r];if(!(a==="_owner"&&e.$$typeof)&&!st(e[a],t[a]))return!1}return!0}return e!==e&&t!==t}function Fr(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Gn(e,t){const n=Fr(e);return Math.round(t*n)/n}function kt(e){const t=l.useRef(e);return et(()=>{t.current=e}),t}function mc(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:a,floating:i}={},transform:c=!0,whileElementsMounted:p,open:f}=e,[d,u]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,g]=l.useState(r);st(m,r)||g(r);const[x,h]=l.useState(null),[v,w]=l.useState(null),b=l.useCallback(_=>{_!==M.current&&(M.current=_,h(_))},[]),y=l.useCallback(_=>{_!==S.current&&(S.current=_,w(_))},[]),C=a||x,E=i||v,M=l.useRef(null),S=l.useRef(null),P=l.useRef(d),O=p!=null,D=kt(p),L=kt(o),F=kt(f),k=l.useCallback(()=>{if(!M.current||!S.current)return;const _={placement:t,strategy:n,middleware:m};L.current&&(_.platform=L.current),dc(M.current,S.current,_).then(R=>{const G={...R,isPositioned:F.current!==!1};j.current&&!st(P.current,G)&&(P.current=G,ya.flushSync(()=>{u(G)}))})},[m,t,n,L,F]);et(()=>{f===!1&&P.current.isPositioned&&(P.current.isPositioned=!1,u(_=>({..._,isPositioned:!1})))},[f]);const j=l.useRef(!1);et(()=>(j.current=!0,()=>{j.current=!1}),[]),et(()=>{if(C&&(M.current=C),E&&(S.current=E),C&&E){if(D.current)return D.current(C,E,k);k()}},[C,E,k,D,O]);const H=l.useMemo(()=>({reference:M,floating:S,setReference:b,setFloating:y}),[b,y]),N=l.useMemo(()=>({reference:C,floating:E}),[C,E]),$=l.useMemo(()=>{const _={position:n,left:0,top:0};if(!N.floating)return _;const R=Gn(N.floating,d.x),G=Gn(N.floating,d.y);return c?{..._,transform:"translate("+R+"px, "+G+"px)",...Fr(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:R,top:G}},[n,c,N.floating,d.x,d.y]);return l.useMemo(()=>({...d,update:k,refs:H,elements:N,floatingStyles:$}),[d,k,H,N,$])}const hc=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Hn({element:r.current,padding:o}).fn(n):{}:r?Hn({element:r,padding:o}).fn(n):{}}}},gc=(e,t)=>({...ac(e),options:[e,t]}),vc=(e,t)=>({...sc(e),options:[e,t]}),xc=(e,t)=>({...uc(e),options:[e,t]}),wc=(e,t)=>({...ic(e),options:[e,t]}),bc=(e,t)=>({...cc(e),options:[e,t]}),yc=(e,t)=>({...lc(e),options:[e,t]}),Cc=(e,t)=>({...hc(e),options:[e,t]});var Ec="Arrow",$r=l.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...a}=e;return s.jsx(I.svg,{...a,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:s.jsx("polygon",{points:"0,0 30,0 15,10"})})});$r.displayName=Ec;var Sc=$r,un="Popper",[Br,vt]=de(un),[Rc,Wr]=Br(un),Hr=e=>{const{__scopePopper:t,children:n}=e,[r,o]=l.useState(null);return s.jsx(Rc,{scope:t,anchor:r,onAnchorChange:o,children:n})};Hr.displayName=un;var Gr="PopperAnchor",Kr=l.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,a=Wr(Gr,n),i=l.useRef(null),c=W(t,i);return l.useEffect(()=>{a.onAnchorChange(r?.current||i.current)}),r?null:s.jsx(I.div,{...o,ref:c})});Kr.displayName=Gr;var dn="PopperContent",[Mc,Ac]=Br(dn),Ur=l.forwardRef((e,t)=>{const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:a="center",alignOffset:i=0,arrowPadding:c=0,avoidCollisions:p=!0,collisionBoundary:f=[],collisionPadding:d=0,sticky:u="partial",hideWhenDetached:m=!1,updatePositionStrategy:g="optimized",onPlaced:x,...h}=e,v=Wr(dn,n),[w,b]=l.useState(null),y=W(t,B=>b(B)),[C,E]=l.useState(null),M=wa(C),S=M?.width??0,P=M?.height??0,O=r+(a!=="center"?"-"+a:""),D=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},L=Array.isArray(f)?f:[f],F=L.length>0,k={padding:D,boundary:L.filter(_c),altBoundary:F},{refs:j,floatingStyles:H,placement:N,isPositioned:$,middlewareData:_}=mc({strategy:"fixed",placement:O,whileElementsMounted:(...B)=>oc(...B,{animationFrame:g==="always"}),elements:{reference:v.anchor},middleware:[gc({mainAxis:o+P,alignmentAxis:i}),p&&vc({mainAxis:!0,crossAxis:!1,limiter:u==="partial"?xc():void 0,...k}),p&&wc({...k}),bc({...k,apply:({elements:B,rects:Ne,availableWidth:ca,availableHeight:la})=>{const{width:ua,height:da}=Ne.reference,Ve=B.floating.style;Ve.setProperty("--radix-popper-available-width",`${ca}px`),Ve.setProperty("--radix-popper-available-height",`${la}px`),Ve.setProperty("--radix-popper-anchor-width",`${ua}px`),Ve.setProperty("--radix-popper-anchor-height",`${da}px`)}}),C&&Cc({element:C,padding:c}),Tc({arrowWidth:S,arrowHeight:P}),m&&yc({strategy:"referenceHidden",...k})]}),[R,G]=Yr(N),Z=te(x);ge(()=>{$&&Z?.()},[$,Z]);const fe=_.arrow?.x,Te=_.arrow?.y,De=_.arrow?.centerOffset!==0,[Ue,pe]=l.useState();return ge(()=>{w&&pe(window.getComputedStyle(w).zIndex)},[w]),s.jsx("div",{ref:j.setFloating,"data-radix-popper-content-wrapper":"",style:{...H,transform:$?H.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Ue,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:s.jsx(Mc,{scope:n,placedSide:R,onArrowChange:E,arrowX:fe,arrowY:Te,shouldHideArrow:De,children:s.jsx(I.div,{"data-side":R,"data-align":G,...h,ref:y,style:{...h.style,animation:$?void 0:"none"}})})})});Ur.displayName=dn;var Vr="PopperArrow",Pc={top:"bottom",right:"left",bottom:"top",left:"right"},zr=l.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,a=Ac(Vr,r),i=Pc[a.placedSide];return s.jsx("span",{ref:a.onArrowChange,style:{position:"absolute",left:a.arrowX,top:a.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[a.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[a.placedSide],visibility:a.shouldHideArrow?"hidden":void 0},children:s.jsx(Sc,{...o,ref:n,style:{...o.style,display:"block"}})})});zr.displayName=Vr;function _c(e){return e!==null}var Tc=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,c=i?0:e.arrowWidth,p=i?0:e.arrowHeight,[f,d]=Yr(n),u={start:"0%",center:"50%",end:"100%"}[d],m=(o.arrow?.x??0)+c/2,g=(o.arrow?.y??0)+p/2;let x="",h="";return f==="bottom"?(x=i?u:`${m}px`,h=`${-p}px`):f==="top"?(x=i?u:`${m}px`,h=`${r.floating.height+p}px`):f==="right"?(x=`${-p}px`,h=i?u:`${g}px`):f==="left"&&(x=`${r.floating.width+p}px`,h=i?u:`${g}px`),{data:{x,y:h}}}});function Yr(e){const[t,n="center"]=e.split("-");return[t,n]}var Xr=Hr,qr=Kr,Zr=Ur,Qr=zr,Dc=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),Nc="VisuallyHidden",Jr=l.forwardRef((e,t)=>s.jsx(I.span,{...e,ref:t,style:{...Dc,...e.style}}));Jr.displayName=Nc;var Oc=Jr,[xt,Vd]=de("Tooltip",[vt]),wt=vt(),eo="TooltipProvider",jc=700,Ut="tooltip.open",[Ic,fn]=xt(eo),to=e=>{const{__scopeTooltip:t,delayDuration:n=jc,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:a}=e,i=l.useRef(!0),c=l.useRef(!1),p=l.useRef(0);return l.useEffect(()=>{const f=p.current;return()=>window.clearTimeout(f)},[]),s.jsx(Ic,{scope:t,isOpenDelayedRef:i,delayDuration:n,onOpen:l.useCallback(()=>{window.clearTimeout(p.current),i.current=!1},[]),onClose:l.useCallback(()=>{window.clearTimeout(p.current),p.current=window.setTimeout(()=>i.current=!0,r)},[r]),isPointerInTransitRef:c,onPointerInTransitChange:l.useCallback(f=>{c.current=f},[]),disableHoverableContent:o,children:a})};to.displayName=eo;var Le="Tooltip",[kc,We]=xt(Le),no=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o,onOpenChange:a,disableHoverableContent:i,delayDuration:c}=e,p=fn(Le,e.__scopeTooltip),f=wt(t),[d,u]=l.useState(null),m=he(),g=l.useRef(0),x=i??p.disableHoverableContent,h=c??p.delayDuration,v=l.useRef(!1),[w,b]=lt({prop:r,defaultProp:o??!1,onChange:S=>{S?(p.onOpen(),document.dispatchEvent(new CustomEvent(Ut))):p.onClose(),a?.(S)},caller:Le}),y=l.useMemo(()=>w?v.current?"delayed-open":"instant-open":"closed",[w]),C=l.useCallback(()=>{window.clearTimeout(g.current),g.current=0,v.current=!1,b(!0)},[b]),E=l.useCallback(()=>{window.clearTimeout(g.current),g.current=0,b(!1)},[b]),M=l.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>{v.current=!0,b(!0),g.current=0},h)},[h,b]);return l.useEffect(()=>()=>{g.current&&(window.clearTimeout(g.current),g.current=0)},[]),s.jsx(Xr,{...f,children:s.jsx(kc,{scope:t,contentId:m,open:w,stateAttribute:y,trigger:d,onTriggerChange:u,onTriggerEnter:l.useCallback(()=>{p.isOpenDelayedRef.current?M():C()},[p.isOpenDelayedRef,M,C]),onTriggerLeave:l.useCallback(()=>{x?E():(window.clearTimeout(g.current),g.current=0)},[E,x]),onOpen:C,onClose:E,disableHoverableContent:x,children:n})})};no.displayName=Le;var Vt="TooltipTrigger",ro=l.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=We(Vt,n),a=fn(Vt,n),i=wt(n),c=l.useRef(null),p=W(t,c,o.onTriggerChange),f=l.useRef(!1),d=l.useRef(!1),u=l.useCallback(()=>f.current=!1,[]);return l.useEffect(()=>()=>document.removeEventListener("pointerup",u),[u]),s.jsx(qr,{asChild:!0,...i,children:s.jsx(I.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:p,onPointerMove:A(e.onPointerMove,m=>{m.pointerType!=="touch"&&!d.current&&!a.isPointerInTransitRef.current&&(o.onTriggerEnter(),d.current=!0)}),onPointerLeave:A(e.onPointerLeave,()=>{o.onTriggerLeave(),d.current=!1}),onPointerDown:A(e.onPointerDown,()=>{o.open&&o.onClose(),f.current=!0,document.addEventListener("pointerup",u,{once:!0})}),onFocus:A(e.onFocus,()=>{f.current||o.onOpen()}),onBlur:A(e.onBlur,o.onClose),onClick:A(e.onClick,o.onClose)})})});ro.displayName=Vt;var pn="TooltipPortal",[Lc,Fc]=xt(pn,{forceMount:void 0}),oo=e=>{const{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,a=We(pn,t);return s.jsx(Lc,{scope:t,forceMount:n,children:s.jsx(ie,{present:n||a.open,children:s.jsx(dt,{asChild:!0,container:o,children:r})})})};oo.displayName=pn;var Ae="TooltipContent",ao=l.forwardRef((e,t)=>{const n=Fc(Ae,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...a}=e,i=We(Ae,e.__scopeTooltip);return s.jsx(ie,{present:r||i.open,children:i.disableHoverableContent?s.jsx(so,{side:o,...a,ref:t}):s.jsx($c,{side:o,...a,ref:t})})}),$c=l.forwardRef((e,t)=>{const n=We(Ae,e.__scopeTooltip),r=fn(Ae,e.__scopeTooltip),o=l.useRef(null),a=W(t,o),[i,c]=l.useState(null),{trigger:p,onClose:f}=n,d=o.current,{onPointerInTransitChange:u}=r,m=l.useCallback(()=>{c(null),u(!1)},[u]),g=l.useCallback((x,h)=>{const v=x.currentTarget,w={x:x.clientX,y:x.clientY},b=Gc(w,v.getBoundingClientRect()),y=Kc(w,b),C=Uc(h.getBoundingClientRect()),E=zc([...y,...C]);c(E),u(!0)},[u]);return l.useEffect(()=>()=>m(),[m]),l.useEffect(()=>{if(p&&d){const x=v=>g(v,d),h=v=>g(v,p);return p.addEventListener("pointerleave",x),d.addEventListener("pointerleave",h),()=>{p.removeEventListener("pointerleave",x),d.removeEventListener("pointerleave",h)}}},[p,d,g,m]),l.useEffect(()=>{if(i){const x=h=>{const v=h.target,w={x:h.clientX,y:h.clientY},b=p?.contains(v)||d?.contains(v),y=!Vc(w,i);b?m():y&&(m(),f())};return document.addEventListener("pointermove",x),()=>document.removeEventListener("pointermove",x)}},[p,d,i,f,m]),s.jsx(so,{...e,ref:a})}),[Bc,Wc]=xt(Le,{isInside:!1}),Hc=ha("TooltipContent"),so=l.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:a,onPointerDownOutside:i,...c}=e,p=We(Ae,n),f=wt(n),{onClose:d}=p;return l.useEffect(()=>(document.addEventListener(Ut,d),()=>document.removeEventListener(Ut,d)),[d]),l.useEffect(()=>{if(p.trigger){const u=m=>{m.target?.contains(p.trigger)&&d()};return window.addEventListener("scroll",u,{capture:!0}),()=>window.removeEventListener("scroll",u,{capture:!0})}},[p.trigger,d]),s.jsx(ut,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:i,onFocusOutside:u=>u.preventDefault(),onDismiss:d,children:s.jsxs(Zr,{"data-state":p.stateAttribute,...f,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[s.jsx(Hc,{children:r}),s.jsx(Bc,{scope:n,isInside:!0,children:s.jsx(Oc,{id:p.contentId,role:"tooltip",children:o||r})})]})})});ao.displayName=Ae;var io="TooltipArrow",co=l.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=wt(n);return Wc(io,n).isInside?null:s.jsx(Qr,{...o,...r,ref:t})});co.displayName=io;function Gc(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(n,r,o,a)){case a:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function Kc(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function Uc(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function Vc(e,t){const{x:n,y:r}=e;let o=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const c=t[a],p=t[i],f=c.x,d=c.y,u=p.x,m=p.y;d>r!=m>r&&n<(u-f)*(r-d)/(m-d)+f&&(o=!o)}return o}function zc(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),Yc(t)}function Yc(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const a=t[t.length-1],i=t[t.length-2];if((a.x-i.x)*(o.y-i.y)>=(a.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const a=n[n.length-1],i=n[n.length-2];if((a.x-i.x)*(o.y-i.y)>=(a.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var Xc=to,qc=no,Zc=ro,Qc=oo,Jc=ao,el=co;function lo({delayDuration:e=0,...t}){return s.jsx(Xc,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function tl({...e}){return s.jsx(lo,{children:s.jsx(qc,{"data-slot":"tooltip",...e})})}function nl({...e}){return s.jsx(Zc,{"data-slot":"tooltip-trigger",...e})}function rl({className:e,sideOffset:t=4,children:n,...r}){return s.jsx(Qc,{children:s.jsxs(Jc,{"data-slot":"tooltip-content",sideOffset:t,className:T("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-w-sm rounded-md px-3 py-1.5 text-xs",e),...r,children:[n,s.jsx(el,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}const ol="sidebar_state",al=3600*24*7,sl="16rem",il="18rem",cl="3rem",ll="b",uo=l.createContext(null);function bt(){const e=l.useContext(uo);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}function ul({defaultOpen:e=!0,open:t,onOpenChange:n,className:r,style:o,children:a,...i}){const c=Qn(),[p,f]=l.useState(!1),[d,u]=l.useState(e),m=t??d,g=l.useCallback(w=>{const b=typeof w=="function"?w(m):w;n?n(b):u(b),document.cookie=`${ol}=${b}; path=/; max-age=${al}`},[n,m]),x=l.useCallback(()=>c?f(w=>!w):g(w=>!w),[c,g,f]);l.useEffect(()=>{const w=b=>{b.key===ll&&(b.metaKey||b.ctrlKey)&&(b.preventDefault(),x())};return window.addEventListener("keydown",w),()=>window.removeEventListener("keydown",w)},[x]);const h=m?"expanded":"collapsed",v=l.useMemo(()=>({state:h,open:m,setOpen:g,isMobile:c,openMobile:p,setOpenMobile:f,toggleSidebar:x}),[h,m,g,c,p,f,x]);return s.jsx(uo.Provider,{value:v,children:s.jsx(lo,{delayDuration:0,children:s.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":sl,"--sidebar-width-icon":cl,...o},className:T("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",r),...i,children:a})})})}function dl({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:r,children:o,...a}){const{isMobile:i,state:c,openMobile:p,setOpenMobile:f}=bt();return n==="none"?s.jsx("div",{"data-slot":"sidebar",className:T("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",r),...a,children:o}):i?s.jsxs(si,{open:p,onOpenChange:f,...a,children:[s.jsxs(ui,{className:"sr-only",children:[s.jsx(di,{children:"Sidebar"}),s.jsx(fi,{children:"Displays the mobile sidebar."})]}),s.jsx(li,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":il},side:e,children:s.jsx("div",{className:"flex h-full w-full flex-col",children:o})})]}):s.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":c,"data-collapsible":c==="collapsed"?n:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[s.jsx("div",{className:T("relative h-svh w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),s.jsx("div",{className:T("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...a,children:s.jsx("div",{"data-sidebar":"sidebar",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:o})})]})}function fl({className:e,onClick:t,...n}){const{toggleSidebar:r}=bt();return s.jsxs(va,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:T("h-7 w-7",e),onClick:o=>{t?.(o),r()},...n,children:[s.jsx(ka,{}),s.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function pl({className:e,...t}){return s.jsx("main",{"data-slot":"sidebar-inset",className:T("bg-background relative flex max-w-full min-h-svh flex-1 flex-col","peer-data-[variant=inset]:min-h-[calc(100svh-(--spacing(4)))] md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-0",e),...t})}function ml({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:T("flex flex-col gap-2 p-2",e),...t})}function hl({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:T("flex flex-col gap-2 p-2",e),...t})}function gl({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:T("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function fo({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:T("relative flex w-full min-w-0 flex-col p-2",e),...t})}function vl({className:e,asChild:t=!1,...n}){const r=t?qt:"div";return s.jsx(r,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:T("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0 group-data-[collapsible=icon]:select-none group-data-[collapsible=icon]:pointer-events-none",e),...n})}function xl({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:T("w-full text-sm",e),...t})}function yt({className:e,...t}){return s.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:T("flex w-full min-w-0 flex-col gap-1",e),...t})}function Ct({className:e,...t}){return s.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:T("group/menu-item relative",e),...t})}const wl=ga("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function Et({asChild:e=!1,isActive:t=!1,variant:n="default",size:r="default",tooltip:o,className:a,...i}){const c=e?qt:"button",{isMobile:p,state:f}=bt(),d=s.jsx(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":r,"data-active":t,className:T(wl({variant:n,size:r}),a),...i});return o?(typeof o=="string"&&(o={children:o}),s.jsxs(tl,{children:[s.jsx(nl,{asChild:!0,children:d}),s.jsx(rl,{side:"right",align:"center",hidden:f!=="collapsed"||p,...o})]})):d}function bl({variant:e="header",children:t,...n}){return e==="sidebar"?s.jsx(pl,{...n,children:t}):s.jsx("main",{className:"mx-auto flex h-full w-full max-w-7xl flex-1 flex-col gap-4 rounded-xl",...n,children:t})}function yl({children:e,variant:t="header"}){const n=Xt().props.sidebarOpen;return t==="header"?s.jsx("div",{className:"flex min-h-screen w-full flex-col",children:e}):s.jsx(ul,{defaultOpen:n,children:e})}function Cl({iconNode:e,className:t,...n}){return s.jsx(e,{className:T("h-4 w-4",t),...n})}function El({items:e,className:t,...n}){return s.jsx(fo,{...n,className:`group-data-[collapsible=icon]:p-0 ${t||""}`,children:s.jsx(xl,{children:s.jsx(yt,{children:e.map(r=>s.jsx(Ct,{children:s.jsx(Et,{asChild:!0,className:"text-neutral-600 hover:text-neutral-800 dark:text-neutral-300 dark:hover:text-neutral-100",children:s.jsxs("a",{href:r.href,target:"_blank",rel:"noopener noreferrer",children:[r.icon&&s.jsx(Cl,{iconNode:r.icon,className:"h-5 w-5"}),s.jsx("span",{children:r.title})]})})},r.title))})})})}function Sl({items:e=[]}){const t=Xt();return s.jsxs(fo,{className:"px-2 py-0",children:[s.jsx(vl,{children:"Platform"}),s.jsx(yt,{children:e.map(n=>s.jsx(Ct,{children:s.jsx(Et,{asChild:!0,isActive:t.url.startsWith(n.href),tooltip:{children:n.title},children:s.jsxs(je,{href:n.href,prefetch:!0,children:[n.icon&&s.jsx(n.icon,{}),s.jsx("span",{children:n.title})]})})},n.title))})]})}function po(e){const t=e+"CollectionProvider",[n,r]=de(t),[o,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=h=>{const{scope:v,children:w}=h,b=me.useRef(null),y=me.useRef(new Map).current;return s.jsx(o,{scope:v,itemMap:y,collectionRef:b,children:w})};i.displayName=t;const c=e+"CollectionSlot",p=tt(c),f=me.forwardRef((h,v)=>{const{scope:w,children:b}=h,y=a(c,w),C=W(v,y.collectionRef);return s.jsx(p,{ref:C,children:b})});f.displayName=c;const d=e+"CollectionItemSlot",u="data-radix-collection-item",m=tt(d),g=me.forwardRef((h,v)=>{const{scope:w,children:b,...y}=h,C=me.useRef(null),E=W(v,C),M=a(d,w);return me.useEffect(()=>(M.itemMap.set(C,{ref:C,...y}),()=>void M.itemMap.delete(C))),s.jsx(m,{[u]:"",ref:E,children:b})});g.displayName=d;function x(h){const v=a(e+"CollectionConsumer",h);return me.useCallback(()=>{const b=v.collectionRef.current;if(!b)return[];const y=Array.from(b.querySelectorAll(`[${u}]`));return Array.from(v.itemMap.values()).sort((M,S)=>y.indexOf(M.ref.current)-y.indexOf(S.ref.current))},[v.collectionRef,v.itemMap])}return[{Provider:i,Slot:f,ItemSlot:g},x,r]}var Rl=l.createContext(void 0);function mo(e){const t=l.useContext(Rl);return e||t||"ltr"}var Lt="rovingFocusGroup.onEntryFocus",Ml={bubbles:!1,cancelable:!0},He="RovingFocusGroup",[zt,ho,Al]=po(He),[Pl,go]=de(He,[Al]),[_l,Tl]=Pl(He),vo=l.forwardRef((e,t)=>s.jsx(zt.Provider,{scope:e.__scopeRovingFocusGroup,children:s.jsx(zt.Slot,{scope:e.__scopeRovingFocusGroup,children:s.jsx(Dl,{...e,ref:t})})}));vo.displayName=He;var Dl=l.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:a,currentTabStopId:i,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:p,onEntryFocus:f,preventScrollOnEntryFocus:d=!1,...u}=e,m=l.useRef(null),g=W(t,m),x=mo(a),[h,v]=lt({prop:i,defaultProp:c??null,onChange:p,caller:He}),[w,b]=l.useState(!1),y=te(f),C=ho(n),E=l.useRef(!1),[M,S]=l.useState(0);return l.useEffect(()=>{const P=m.current;if(P)return P.addEventListener(Lt,y),()=>P.removeEventListener(Lt,y)},[y]),s.jsx(_l,{scope:n,orientation:r,dir:x,loop:o,currentTabStopId:h,onItemFocus:l.useCallback(P=>v(P),[v]),onItemShiftTab:l.useCallback(()=>b(!0),[]),onFocusableItemAdd:l.useCallback(()=>S(P=>P+1),[]),onFocusableItemRemove:l.useCallback(()=>S(P=>P-1),[]),children:s.jsx(I.div,{tabIndex:w||M===0?-1:0,"data-orientation":r,...u,ref:g,style:{outline:"none",...e.style},onMouseDown:A(e.onMouseDown,()=>{E.current=!0}),onFocus:A(e.onFocus,P=>{const O=!E.current;if(P.target===P.currentTarget&&O&&!w){const D=new CustomEvent(Lt,Ml);if(P.currentTarget.dispatchEvent(D),!D.defaultPrevented){const L=C().filter(N=>N.focusable),F=L.find(N=>N.active),k=L.find(N=>N.id===h),H=[F,k,...L].filter(Boolean).map(N=>N.ref.current);bo(H,d)}}E.current=!1}),onBlur:A(e.onBlur,()=>b(!1))})})}),xo="RovingFocusGroupItem",wo=l.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:a,children:i,...c}=e,p=he(),f=a||p,d=Tl(xo,n),u=d.currentTabStopId===f,m=ho(n),{onFocusableItemAdd:g,onFocusableItemRemove:x,currentTabStopId:h}=d;return l.useEffect(()=>{if(r)return g(),()=>x()},[r,g,x]),s.jsx(zt.ItemSlot,{scope:n,id:f,focusable:r,active:o,children:s.jsx(I.span,{tabIndex:u?0:-1,"data-orientation":d.orientation,...c,ref:t,onMouseDown:A(e.onMouseDown,v=>{r?d.onItemFocus(f):v.preventDefault()}),onFocus:A(e.onFocus,()=>d.onItemFocus(f)),onKeyDown:A(e.onKeyDown,v=>{if(v.key==="Tab"&&v.shiftKey){d.onItemShiftTab();return}if(v.target!==v.currentTarget)return;const w=jl(v,d.orientation,d.dir);if(w!==void 0){if(v.metaKey||v.ctrlKey||v.altKey||v.shiftKey)return;v.preventDefault();let y=m().filter(C=>C.focusable).map(C=>C.ref.current);if(w==="last")y.reverse();else if(w==="prev"||w==="next"){w==="prev"&&y.reverse();const C=y.indexOf(v.currentTarget);y=d.loop?Il(y,C+1):y.slice(C+1)}setTimeout(()=>bo(y))}}),children:typeof i=="function"?i({isCurrentTabStop:u,hasTabStop:h!=null}):i})})});wo.displayName=xo;var Nl={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Ol(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function jl(e,t,n){const r=Ol(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return Nl[r]}function bo(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function Il(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var kl=vo,Ll=wo,Yt=["Enter"," "],Fl=["ArrowDown","PageUp","Home"],yo=["ArrowUp","PageDown","End"],$l=[...Fl,...yo],Bl={ltr:[...Yt,"ArrowRight"],rtl:[...Yt,"ArrowLeft"]},Wl={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Ge="Menu",[Fe,Hl,Gl]=po(Ge),[we,Co]=de(Ge,[Gl,vt,go]),St=vt(),Eo=go(),[Kl,be]=we(Ge),[Ul,Ke]=we(Ge),So=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:a,modal:i=!0}=e,c=St(t),[p,f]=l.useState(null),d=l.useRef(!1),u=te(a),m=mo(o);return l.useEffect(()=>{const g=()=>{d.current=!0,document.addEventListener("pointerdown",x,{capture:!0,once:!0}),document.addEventListener("pointermove",x,{capture:!0,once:!0})},x=()=>d.current=!1;return document.addEventListener("keydown",g,{capture:!0}),()=>{document.removeEventListener("keydown",g,{capture:!0}),document.removeEventListener("pointerdown",x,{capture:!0}),document.removeEventListener("pointermove",x,{capture:!0})}},[]),s.jsx(Xr,{...c,children:s.jsx(Kl,{scope:t,open:n,onOpenChange:u,content:p,onContentChange:f,children:s.jsx(Ul,{scope:t,onClose:l.useCallback(()=>u(!1),[u]),isUsingKeyboardRef:d,dir:m,modal:i,children:r})})})};So.displayName=Ge;var Vl="MenuAnchor",mn=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=St(n);return s.jsx(qr,{...o,...r,ref:t})});mn.displayName=Vl;var hn="MenuPortal",[zl,Ro]=we(hn,{forceMount:void 0}),Mo=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:o}=e,a=be(hn,t);return s.jsx(zl,{scope:t,forceMount:n,children:s.jsx(ie,{present:n||a.open,children:s.jsx(dt,{asChild:!0,container:o,children:r})})})};Mo.displayName=hn;var z="MenuContent",[Yl,gn]=we(z),Ao=l.forwardRef((e,t)=>{const n=Ro(z,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=be(z,e.__scopeMenu),i=Ke(z,e.__scopeMenu);return s.jsx(Fe.Provider,{scope:e.__scopeMenu,children:s.jsx(ie,{present:r||a.open,children:s.jsx(Fe.Slot,{scope:e.__scopeMenu,children:i.modal?s.jsx(Xl,{...o,ref:t}):s.jsx(ql,{...o,ref:t})})})})}),Xl=l.forwardRef((e,t)=>{const n=be(z,e.__scopeMenu),r=l.useRef(null),o=W(t,r);return l.useEffect(()=>{const a=r.current;if(a)return dr(a)},[]),s.jsx(vn,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:A(e.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),ql=l.forwardRef((e,t)=>{const n=be(z,e.__scopeMenu);return s.jsx(vn,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Zl=tt("MenuContent.ScrollLock"),vn=l.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,disableOutsidePointerEvents:c,onEntryFocus:p,onEscapeKeyDown:f,onPointerDownOutside:d,onFocusOutside:u,onInteractOutside:m,onDismiss:g,disableOutsideScroll:x,...h}=e,v=be(z,n),w=Ke(z,n),b=St(n),y=Eo(n),C=Hl(n),[E,M]=l.useState(null),S=l.useRef(null),P=W(t,S,v.onContentChange),O=l.useRef(0),D=l.useRef(""),L=l.useRef(0),F=l.useRef(null),k=l.useRef("right"),j=l.useRef(0),H=x?Qt:l.Fragment,N=x?{as:Zl,allowPinchZoom:!0}:void 0,$=R=>{const G=D.current+R,Z=C().filter(B=>!B.disabled),fe=document.activeElement,Te=Z.find(B=>B.ref.current===fe)?.textValue,De=Z.map(B=>B.textValue),Ue=lu(De,G,Te),pe=Z.find(B=>B.textValue===Ue)?.ref.current;(function B(Ne){D.current=Ne,window.clearTimeout(O.current),Ne!==""&&(O.current=window.setTimeout(()=>B(""),1e3))})(G),pe&&setTimeout(()=>pe.focus())};l.useEffect(()=>()=>window.clearTimeout(O.current),[]),nr();const _=l.useCallback(R=>k.current===F.current?.side&&du(R,F.current?.area),[]);return s.jsx(Yl,{scope:n,searchRef:D,onItemEnter:l.useCallback(R=>{_(R)&&R.preventDefault()},[_]),onItemLeave:l.useCallback(R=>{_(R)||(S.current?.focus(),M(null))},[_]),onTriggerLeave:l.useCallback(R=>{_(R)&&R.preventDefault()},[_]),pointerGraceTimerRef:L,onPointerGraceIntentChange:l.useCallback(R=>{F.current=R},[]),children:s.jsx(H,{...N,children:s.jsx(Zt,{asChild:!0,trapped:o,onMountAutoFocus:A(a,R=>{R.preventDefault(),S.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:s.jsx(ut,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:f,onPointerDownOutside:d,onFocusOutside:u,onInteractOutside:m,onDismiss:g,children:s.jsx(kl,{asChild:!0,...y,dir:w.dir,orientation:"vertical",loop:r,currentTabStopId:E,onCurrentTabStopIdChange:M,onEntryFocus:A(p,R=>{w.isUsingKeyboardRef.current||R.preventDefault()}),preventScrollOnEntryFocus:!0,children:s.jsx(Zr,{role:"menu","aria-orientation":"vertical","data-state":Go(v.open),"data-radix-menu-content":"",dir:w.dir,...b,...h,ref:P,style:{outline:"none",...h.style},onKeyDown:A(h.onKeyDown,R=>{const Z=R.target.closest("[data-radix-menu-content]")===R.currentTarget,fe=R.ctrlKey||R.altKey||R.metaKey,Te=R.key.length===1;Z&&(R.key==="Tab"&&R.preventDefault(),!fe&&Te&&$(R.key));const De=S.current;if(R.target!==De||!$l.includes(R.key))return;R.preventDefault();const pe=C().filter(B=>!B.disabled).map(B=>B.ref.current);yo.includes(R.key)&&pe.reverse(),iu(pe)}),onBlur:A(e.onBlur,R=>{R.currentTarget.contains(R.target)||(window.clearTimeout(O.current),D.current="")}),onPointerMove:A(e.onPointerMove,$e(R=>{const G=R.target,Z=j.current!==R.clientX;if(R.currentTarget.contains(G)&&Z){const fe=R.clientX>j.current?"right":"left";k.current=fe,j.current=R.clientX}}))})})})})})})});Ao.displayName=z;var Ql="MenuGroup",xn=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return s.jsx(I.div,{role:"group",...r,ref:t})});xn.displayName=Ql;var Jl="MenuLabel",Po=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return s.jsx(I.div,{...r,ref:t})});Po.displayName=Jl;var it="MenuItem",Kn="menu.itemSelect",Rt=l.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...o}=e,a=l.useRef(null),i=Ke(it,e.__scopeMenu),c=gn(it,e.__scopeMenu),p=W(t,a),f=l.useRef(!1),d=()=>{const u=a.current;if(!n&&u){const m=new CustomEvent(Kn,{bubbles:!0,cancelable:!0});u.addEventListener(Kn,g=>r?.(g),{once:!0}),Zn(u,m),m.defaultPrevented?f.current=!1:i.onClose()}};return s.jsx(_o,{...o,ref:p,disabled:n,onClick:A(e.onClick,d),onPointerDown:u=>{e.onPointerDown?.(u),f.current=!0},onPointerUp:A(e.onPointerUp,u=>{f.current||u.currentTarget?.click()}),onKeyDown:A(e.onKeyDown,u=>{const m=c.searchRef.current!=="";n||m&&u.key===" "||Yt.includes(u.key)&&(u.currentTarget.click(),u.preventDefault())})})});Rt.displayName=it;var _o=l.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:o,...a}=e,i=gn(it,n),c=Eo(n),p=l.useRef(null),f=W(t,p),[d,u]=l.useState(!1),[m,g]=l.useState("");return l.useEffect(()=>{const x=p.current;x&&g((x.textContent??"").trim())},[a.children]),s.jsx(Fe.ItemSlot,{scope:n,disabled:r,textValue:o??m,children:s.jsx(Ll,{asChild:!0,...c,focusable:!r,children:s.jsx(I.div,{role:"menuitem","data-highlighted":d?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...a,ref:f,onPointerMove:A(e.onPointerMove,$e(x=>{r?i.onItemLeave(x):(i.onItemEnter(x),x.defaultPrevented||x.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:A(e.onPointerLeave,$e(x=>i.onItemLeave(x))),onFocus:A(e.onFocus,()=>u(!0)),onBlur:A(e.onBlur,()=>u(!1))})})})}),eu="MenuCheckboxItem",To=l.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...o}=e;return s.jsx(Io,{scope:e.__scopeMenu,checked:n,children:s.jsx(Rt,{role:"menuitemcheckbox","aria-checked":ct(n)?"mixed":n,...o,ref:t,"data-state":bn(n),onSelect:A(o.onSelect,()=>r?.(ct(n)?!0:!n),{checkForDefaultPrevented:!1})})})});To.displayName=eu;var Do="MenuRadioGroup",[tu,nu]=we(Do,{value:void 0,onValueChange:()=>{}}),No=l.forwardRef((e,t)=>{const{value:n,onValueChange:r,...o}=e,a=te(r);return s.jsx(tu,{scope:e.__scopeMenu,value:n,onValueChange:a,children:s.jsx(xn,{...o,ref:t})})});No.displayName=Do;var Oo="MenuRadioItem",jo=l.forwardRef((e,t)=>{const{value:n,...r}=e,o=nu(Oo,e.__scopeMenu),a=n===o.value;return s.jsx(Io,{scope:e.__scopeMenu,checked:a,children:s.jsx(Rt,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":bn(a),onSelect:A(r.onSelect,()=>o.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});jo.displayName=Oo;var wn="MenuItemIndicator",[Io,ru]=we(wn,{checked:!1}),ko=l.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...o}=e,a=ru(wn,n);return s.jsx(ie,{present:r||ct(a.checked)||a.checked===!0,children:s.jsx(I.span,{...o,ref:t,"data-state":bn(a.checked)})})});ko.displayName=wn;var ou="MenuSeparator",Lo=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return s.jsx(I.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});Lo.displayName=ou;var au="MenuArrow",Fo=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=St(n);return s.jsx(Qr,{...o,...r,ref:t})});Fo.displayName=au;var su="MenuSub",[zd,$o]=we(su),Oe="MenuSubTrigger",Bo=l.forwardRef((e,t)=>{const n=be(Oe,e.__scopeMenu),r=Ke(Oe,e.__scopeMenu),o=$o(Oe,e.__scopeMenu),a=gn(Oe,e.__scopeMenu),i=l.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:p}=a,f={__scopeMenu:e.__scopeMenu},d=l.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return l.useEffect(()=>d,[d]),l.useEffect(()=>{const u=c.current;return()=>{window.clearTimeout(u),p(null)}},[c,p]),s.jsx(mn,{asChild:!0,...f,children:s.jsx(_o,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":Go(n.open),...e,ref:qn(t,o.onTriggerChange),onClick:u=>{e.onClick?.(u),!(e.disabled||u.defaultPrevented)&&(u.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:A(e.onPointerMove,$e(u=>{a.onItemEnter(u),!u.defaultPrevented&&!e.disabled&&!n.open&&!i.current&&(a.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),d()},100))})),onPointerLeave:A(e.onPointerLeave,$e(u=>{d();const m=n.content?.getBoundingClientRect();if(m){const g=n.content?.dataset.side,x=g==="right",h=x?-5:5,v=m[x?"left":"right"],w=m[x?"right":"left"];a.onPointerGraceIntentChange({area:[{x:u.clientX+h,y:u.clientY},{x:v,y:m.top},{x:w,y:m.top},{x:w,y:m.bottom},{x:v,y:m.bottom}],side:g}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>a.onPointerGraceIntentChange(null),300)}else{if(a.onTriggerLeave(u),u.defaultPrevented)return;a.onPointerGraceIntentChange(null)}})),onKeyDown:A(e.onKeyDown,u=>{const m=a.searchRef.current!=="";e.disabled||m&&u.key===" "||Bl[r.dir].includes(u.key)&&(n.onOpenChange(!0),n.content?.focus(),u.preventDefault())})})})});Bo.displayName=Oe;var Wo="MenuSubContent",Ho=l.forwardRef((e,t)=>{const n=Ro(z,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=be(z,e.__scopeMenu),i=Ke(z,e.__scopeMenu),c=$o(Wo,e.__scopeMenu),p=l.useRef(null),f=W(t,p);return s.jsx(Fe.Provider,{scope:e.__scopeMenu,children:s.jsx(ie,{present:r||a.open,children:s.jsx(Fe.Slot,{scope:e.__scopeMenu,children:s.jsx(vn,{id:c.contentId,"aria-labelledby":c.triggerId,...o,ref:f,align:"start",side:i.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:d=>{i.isUsingKeyboardRef.current&&p.current?.focus(),d.preventDefault()},onCloseAutoFocus:d=>d.preventDefault(),onFocusOutside:A(e.onFocusOutside,d=>{d.target!==c.trigger&&a.onOpenChange(!1)}),onEscapeKeyDown:A(e.onEscapeKeyDown,d=>{i.onClose(),d.preventDefault()}),onKeyDown:A(e.onKeyDown,d=>{const u=d.currentTarget.contains(d.target),m=Wl[i.dir].includes(d.key);u&&m&&(a.onOpenChange(!1),c.trigger?.focus(),d.preventDefault())})})})})})});Ho.displayName=Wo;function Go(e){return e?"open":"closed"}function ct(e){return e==="indeterminate"}function bn(e){return ct(e)?"indeterminate":e?"checked":"unchecked"}function iu(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function cu(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function lu(e,t,n){const o=t.length>1&&Array.from(t).every(f=>f===t[0])?t[0]:t,a=n?e.indexOf(n):-1;let i=cu(e,Math.max(a,0));o.length===1&&(i=i.filter(f=>f!==n));const p=i.find(f=>f.toLowerCase().startsWith(o.toLowerCase()));return p!==n?p:void 0}function uu(e,t){const{x:n,y:r}=e;let o=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const c=t[a],p=t[i],f=c.x,d=c.y,u=p.x,m=p.y;d>r!=m>r&&n<(u-f)*(r-d)/(m-d)+f&&(o=!o)}return o}function du(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return uu(n,t)}function $e(e){return t=>t.pointerType==="mouse"?e(t):void 0}var fu=So,pu=mn,mu=Mo,hu=Ao,gu=xn,vu=Po,xu=Rt,wu=To,bu=No,yu=jo,Cu=ko,Eu=Lo,Su=Fo,Ru=Bo,Mu=Ho,Mt="DropdownMenu",[Au,Yd]=de(Mt,[Co]),K=Co(),[Pu,Ko]=Au(Mt),Uo=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:a,onOpenChange:i,modal:c=!0}=e,p=K(t),f=l.useRef(null),[d,u]=lt({prop:o,defaultProp:a??!1,onChange:i,caller:Mt});return s.jsx(Pu,{scope:t,triggerId:he(),triggerRef:f,contentId:he(),open:d,onOpenChange:u,onOpenToggle:l.useCallback(()=>u(m=>!m),[u]),modal:c,children:s.jsx(fu,{...p,open:d,onOpenChange:u,dir:r,modal:c,children:n})})};Uo.displayName=Mt;var Vo="DropdownMenuTrigger",zo=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,a=Ko(Vo,n),i=K(n);return s.jsx(pu,{asChild:!0,...i,children:s.jsx(I.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:qn(t,a.triggerRef),onPointerDown:A(e.onPointerDown,c=>{!r&&c.button===0&&c.ctrlKey===!1&&(a.onOpenToggle(),a.open||c.preventDefault())}),onKeyDown:A(e.onKeyDown,c=>{r||(["Enter"," "].includes(c.key)&&a.onOpenToggle(),c.key==="ArrowDown"&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(c.key)&&c.preventDefault())})})})});zo.displayName=Vo;var _u="DropdownMenuPortal",Yo=e=>{const{__scopeDropdownMenu:t,...n}=e,r=K(t);return s.jsx(mu,{...r,...n})};Yo.displayName=_u;var Xo="DropdownMenuContent",qo=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ko(Xo,n),a=K(n),i=l.useRef(!1);return s.jsx(hu,{id:o.contentId,"aria-labelledby":o.triggerId,...a,...r,ref:t,onCloseAutoFocus:A(e.onCloseAutoFocus,c=>{i.current||o.triggerRef.current?.focus(),i.current=!1,c.preventDefault()}),onInteractOutside:A(e.onInteractOutside,c=>{const p=c.detail.originalEvent,f=p.button===0&&p.ctrlKey===!0,d=p.button===2||f;(!o.modal||d)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});qo.displayName=Xo;var Tu="DropdownMenuGroup",Zo=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(gu,{...o,...r,ref:t})});Zo.displayName=Tu;var Du="DropdownMenuLabel",Qo=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(vu,{...o,...r,ref:t})});Qo.displayName=Du;var Nu="DropdownMenuItem",Jo=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(xu,{...o,...r,ref:t})});Jo.displayName=Nu;var Ou="DropdownMenuCheckboxItem",ju=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(wu,{...o,...r,ref:t})});ju.displayName=Ou;var Iu="DropdownMenuRadioGroup",ku=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(bu,{...o,...r,ref:t})});ku.displayName=Iu;var Lu="DropdownMenuRadioItem",Fu=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(yu,{...o,...r,ref:t})});Fu.displayName=Lu;var $u="DropdownMenuItemIndicator",Bu=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(Cu,{...o,...r,ref:t})});Bu.displayName=$u;var Wu="DropdownMenuSeparator",ea=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(Eu,{...o,...r,ref:t})});ea.displayName=Wu;var Hu="DropdownMenuArrow",Gu=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(Su,{...o,...r,ref:t})});Gu.displayName=Hu;var Ku="DropdownMenuSubTrigger",Uu=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(Ru,{...o,...r,ref:t})});Uu.displayName=Ku;var Vu="DropdownMenuSubContent",zu=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(Mu,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});zu.displayName=Vu;var Yu=Uo,Xu=zo,qu=Yo,Zu=qo,Qu=Zo,Ju=Qo,ed=Jo,td=ea;function nd({...e}){return s.jsx(Yu,{"data-slot":"dropdown-menu",...e})}function rd({...e}){return s.jsx(Xu,{"data-slot":"dropdown-menu-trigger",...e})}function od({className:e,sideOffset:t=4,...n}){return s.jsx(qu,{children:s.jsx(Zu,{"data-slot":"dropdown-menu-content",sideOffset:t,className:T("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md",e),...n})})}function ad({...e}){return s.jsx(Qu,{"data-slot":"dropdown-menu-group",...e})}function Un({className:e,inset:t,variant:n="default",...r}){return s.jsx(ed,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:T("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r})}function sd({className:e,inset:t,...n}){return s.jsx(Ju,{"data-slot":"dropdown-menu-label","data-inset":t,className:T("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...n})}function Vn({className:e,...t}){return s.jsx(td,{"data-slot":"dropdown-menu-separator",className:T("bg-border -mx-1 my-1 h-px",e),...t})}var Ft={exports:{}},$t={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zn;function id(){if(zn)return $t;zn=1;var e=pa();function t(u,m){return u===m&&(u!==0||1/u===1/m)||u!==u&&m!==m}var n=typeof Object.is=="function"?Object.is:t,r=e.useState,o=e.useEffect,a=e.useLayoutEffect,i=e.useDebugValue;function c(u,m){var g=m(),x=r({inst:{value:g,getSnapshot:m}}),h=x[0].inst,v=x[1];return a(function(){h.value=g,h.getSnapshot=m,p(h)&&v({inst:h})},[u,g,m]),o(function(){return p(h)&&v({inst:h}),u(function(){p(h)&&v({inst:h})})},[u]),i(g),g}function p(u){var m=u.getSnapshot;u=u.value;try{var g=m();return!n(u,g)}catch{return!0}}function f(u,m){return m()}var d=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?f:c;return $t.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:d,$t}var Yn;function cd(){return Yn||(Yn=1,Ft.exports=id()),Ft.exports}var ld=cd();function ud(){return ld.useSyncExternalStore(dd,()=>!0,()=>!1)}function dd(){return()=>{}}var yn="Avatar",[fd,Xd]=de(yn),[pd,ta]=fd(yn),na=l.forwardRef((e,t)=>{const{__scopeAvatar:n,...r}=e,[o,a]=l.useState("idle");return s.jsx(pd,{scope:n,imageLoadingStatus:o,onImageLoadingStatusChange:a,children:s.jsx(I.span,{...r,ref:t})})});na.displayName=yn;var ra="AvatarImage",oa=l.forwardRef((e,t)=>{const{__scopeAvatar:n,src:r,onLoadingStatusChange:o=()=>{},...a}=e,i=ta(ra,n),c=md(r,a),p=te(f=>{o(f),i.onImageLoadingStatusChange(f)});return ge(()=>{c!=="idle"&&p(c)},[c,p]),c==="loaded"?s.jsx(I.img,{...a,ref:t,src:r}):null});oa.displayName=ra;var aa="AvatarFallback",sa=l.forwardRef((e,t)=>{const{__scopeAvatar:n,delayMs:r,...o}=e,a=ta(aa,n),[i,c]=l.useState(r===void 0);return l.useEffect(()=>{if(r!==void 0){const p=window.setTimeout(()=>c(!0),r);return()=>window.clearTimeout(p)}},[r]),i&&a.imageLoadingStatus!=="loaded"?s.jsx(I.span,{...o,ref:t}):null});sa.displayName=aa;function Xn(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function md(e,{referrerPolicy:t,crossOrigin:n}){const r=ud(),o=l.useRef(null),a=r?(o.current||(o.current=new window.Image),o.current):null,[i,c]=l.useState(()=>Xn(a,e));return ge(()=>{c(Xn(a,e))},[a,e]),ge(()=>{const p=u=>()=>{c(u)};if(!a)return;const f=p("loaded"),d=p("error");return a.addEventListener("load",f),a.addEventListener("error",d),t&&(a.referrerPolicy=t),typeof n=="string"&&(a.crossOrigin=n),()=>{a.removeEventListener("load",f),a.removeEventListener("error",d)}},[a,n,t]),i}var hd=na,gd=oa,vd=sa;function xd({className:e,...t}){return s.jsx(hd,{"data-slot":"avatar",className:T("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function wd({className:e,...t}){return s.jsx(gd,{"data-slot":"avatar-image",className:T("aspect-square size-full",e),...t})}function bd({className:e,...t}){return s.jsx(vd,{"data-slot":"avatar-fallback",className:T("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}function yd(){return l.useCallback(e=>{const t=e.trim().split(" ");if(t.length===0)return"";if(t.length===1)return t[0].charAt(0).toUpperCase();const n=t[0].charAt(0),r=t[t.length-1].charAt(0);return`${n}${r}`.toUpperCase()},[])}function ia({user:e,showEmail:t=!1}){const n=yd();return s.jsxs(s.Fragment,{children:[s.jsxs(xd,{className:"h-8 w-8 overflow-hidden rounded-full",children:[s.jsx(wd,{src:e.avatar,alt:e.name}),s.jsx(bd,{className:"rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white",children:n(e.name)})]}),s.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[s.jsx("span",{className:"truncate font-medium",children:e.name}),t&&s.jsx("span",{className:"truncate text-xs text-muted-foreground",children:e.email})]})]})}function Cd(){return l.useCallback(()=>{document.body.style.removeProperty("pointer-events")},[])}function Ed({user:e}){const t=Cd(),n=()=>{t(),ma.flushAll()};return s.jsxs(s.Fragment,{children:[s.jsx(sd,{className:"p-0 font-normal",children:s.jsx("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:s.jsx(ia,{user:e,showEmail:!0})})}),s.jsx(Vn,{}),s.jsx(ad,{children:s.jsx(Un,{asChild:!0,children:s.jsxs(je,{className:"block w-full",href:route("profile.edit"),as:"button",prefetch:!0,onClick:t,children:[s.jsx(Fa,{className:"mr-2"}),"Settings"]})})}),s.jsx(Vn,{}),s.jsx(Un,{asChild:!0,children:s.jsxs(je,{className:"block w-full",method:"post",href:route("logout"),as:"button",onClick:n,children:[s.jsx(ja,{className:"mr-2"}),"Log out"]})})]})}function Sd(){const{auth:e}=Xt().props,{state:t}=bt(),n=Qn();return s.jsx(yt,{children:s.jsx(Ct,{children:s.jsxs(nd,{children:[s.jsx(rd,{asChild:!0,children:s.jsxs(Et,{size:"lg",className:"group text-sidebar-accent-foreground data-[state=open]:bg-sidebar-accent",children:[s.jsx(ia,{user:e.user}),s.jsx(Pa,{className:"ml-auto size-4"})]})}),s.jsx(od,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",align:"end",side:n?"bottom":t==="collapsed"?"left":"bottom",children:s.jsx(Ed,{user:e.user})})]})})})}function Rd(){return s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"flex aspect-square size-8 items-center justify-center rounded-md bg-sidebar-primary text-sidebar-primary-foreground",children:s.jsx(Ca,{className:"size-5 fill-current text-white dark:text-black"})}),s.jsx("div",{className:"ml-1 grid flex-1 text-left text-sm",children:s.jsx("span",{className:"mb-0.5 truncate leading-tight font-semibold",children:"Laravel Starter Kit"})})]})}const Md=[{title:"Dashboard",href:"/dashboard",icon:Na}],Ad=[{title:"Repository",href:"https://github.com/laravel/react-starter-kit",icon:Ta},{title:"Documentation",href:"https://laravel.com/docs/starter-kits#react",icon:Sa}];function Pd(){return s.jsxs(dl,{collapsible:"icon",variant:"inset",children:[s.jsx(ml,{children:s.jsx(yt,{children:s.jsx(Ct,{children:s.jsx(Et,{size:"lg",asChild:!0,children:s.jsx(je,{href:"/dashboard",prefetch:!0,children:s.jsx(Rd,{})})})})})}),s.jsx(gl,{children:s.jsx(Sl,{items:Md})}),s.jsxs(hl,{children:[s.jsx(El,{items:Ad,className:"mt-auto"}),s.jsx(Sd,{})]})]})}function _d({...e}){return s.jsx("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function Td({className:e,...t}){return s.jsx("ol",{"data-slot":"breadcrumb-list",className:T("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t})}function Dd({className:e,...t}){return s.jsx("li",{"data-slot":"breadcrumb-item",className:T("inline-flex items-center gap-1.5",e),...t})}function Nd({asChild:e,className:t,...n}){const r=e?qt:"a";return s.jsx(r,{"data-slot":"breadcrumb-link",className:T("hover:text-foreground transition-colors",t),...n})}function Od({className:e,...t}){return s.jsx("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:T("text-foreground font-normal",e),...t})}function jd({children:e,className:t,...n}){return s.jsx("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:T("[&>svg]:size-3.5",t),...n,children:e??s.jsx(Ma,{})})}function Id({breadcrumbs:e}){return s.jsx(s.Fragment,{children:e.length>0&&s.jsx(_d,{children:s.jsx(Td,{children:e.map((t,n)=>{const r=n===e.length-1;return s.jsxs(l.Fragment,{children:[s.jsx(Dd,{children:r?s.jsx(Od,{children:t.title}):s.jsx(Nd,{asChild:!0,children:s.jsx(je,{href:t.href,children:t.title})})}),!r&&s.jsx(jd,{})]},n)})})})})}function kd({breadcrumbs:e=[]}){return s.jsx("header",{className:"flex h-16 shrink-0 items-center gap-2 border-b border-sidebar-border/50 px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(fl,{className:"-ml-1"}),s.jsx(Id,{breadcrumbs:e})]})})}function Ld({children:e,breadcrumbs:t=[]}){return s.jsxs(yl,{variant:"sidebar",children:[s.jsx(Pd,{}),s.jsxs(bl,{variant:"sidebar",className:"overflow-x-hidden",children:[s.jsx(kd,{breadcrumbs:t}),e]})]})}const qd=({children:e,breadcrumbs:t,...n})=>s.jsx(Ld,{breadcrumbs:t,...n,children:e});export{qd as A,Zr as C,ut as D,Zt as F,ti as O,dt as P,Xr as R,ri as T,Dc as V,Ba as X,he as a,po as b,vt as c,qr as d,te as e,nr as f,Qt as g,dr as h,Qr as i,Js as j,ni as k,ai as l,oi as m,Ud as n,ei as o,mo as u};
