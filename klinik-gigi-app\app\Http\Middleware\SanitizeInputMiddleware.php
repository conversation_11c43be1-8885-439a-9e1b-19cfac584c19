<?php

namespace App\Http\Middleware;

use App\Models\AuditLog;
use App\Services\InputSanitizationService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SanitizeInputMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip sanitization for certain routes
        if ($this->shouldSkipSanitization($request)) {
            return $next($request);
        }

        // Sanitize request inputs
        $this->sanitizeRequest($request);

        $response = $next($request);

        // Add security headers
        $this->addSecurityHeaders($response);

        return $response;
    }

    /**
     * Determine if sanitization should be skipped
     */
    private function shouldSkipSanitization(Request $request): bool
    {
        // Skip for API endpoints that handle their own sanitization
        $skipRoutes = [
            'api/upload/*',
            'api/webhook/*',
            '_ignition/*',
            'telescope/*',
            'horizon/*'
        ];

        foreach ($skipRoutes as $pattern) {
            if ($request->is($pattern)) {
                return true;
            }
        }

        // Skip for file uploads
        if ($request->hasFile('*')) {
            return true;
        }

        return false;
    }

    /**
     * Sanitize request inputs
     */
    private function sanitizeRequest(Request $request): void
    {
        // Get all input data
        $inputs = $request->all();
        
        // Track if any malicious content was detected
        $maliciousDetected = false;
        $maliciousInputs = [];

        // Sanitize inputs based on field type
        $sanitizedInputs = $this->sanitizeInputsByType($inputs, $maliciousDetected, $maliciousInputs);

        // Log malicious attempts
        if ($maliciousDetected) {
            $this->logMaliciousAttempt($request, $maliciousInputs);
        }

        // Replace request inputs with sanitized versions
        $request->replace($sanitizedInputs);
    }

    /**
     * Sanitize inputs based on field type
     */
    private function sanitizeInputsByType(array $inputs, bool &$maliciousDetected, array &$maliciousInputs): array
    {
        $sanitized = [];

        foreach ($inputs as $key => $value) {
            if (is_array($value)) {
                $sanitized[$key] = $this->sanitizeInputsByType($value, $maliciousDetected, $maliciousInputs);
            } elseif (is_string($value)) {
                $originalValue = $value;
                $sanitizedValue = $this->sanitizeByFieldName($key, $value);

                // Check if content was modified (potential malicious content)
                if ($originalValue !== $sanitizedValue) {
                    $maliciousDetected = true;
                    $maliciousInputs[$key] = [
                        'original' => $originalValue,
                        'sanitized' => $sanitizedValue
                    ];
                }

                $sanitized[$key] = $sanitizedValue;
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }

    /**
     * Sanitize value based on field name
     */
    private function sanitizeByFieldName(string $fieldName, string $value): string
    {
        // Email fields
        if (str_contains($fieldName, 'email')) {
            return InputSanitizationService::sanitizeEmail($value);
        }

        // Phone fields
        if (str_contains($fieldName, 'phone') || str_contains($fieldName, 'telepon')) {
            return InputSanitizationService::sanitizePhone($value);
        }

        // Username fields
        if (str_contains($fieldName, 'username')) {
            return InputSanitizationService::sanitizeUsername($value);
        }

        // URL fields
        if (str_contains($fieldName, 'url') || str_contains($fieldName, 'link')) {
            return InputSanitizationService::sanitizeUrl($value);
        }

        // Rich text fields (content, description, etc.)
        if (in_array($fieldName, ['content', 'description', 'notes', 'comment', 'message'])) {
            return InputSanitizationService::sanitizeRichText($value);
        }

        // Password fields - don't sanitize but check for common patterns
        if (str_contains($fieldName, 'password')) {
            // Just check for obvious malicious content without modifying
            if (InputSanitizationService::containsXSS($value) || 
                InputSanitizationService::containsSQLInjection($value)) {
                // Log but don't modify password
                \Log::warning('Malicious content detected in password field', [
                    'field' => $fieldName,
                    'ip' => request()->ip(),
                    'user_id' => auth()->id()
                ]);
            }
            return $value;
        }

        // Default sanitization for text fields
        return InputSanitizationService::sanitize($value, [
            'xss' => true,
            'sql' => true,
            'command' => true,
            'trim' => true,
            'max_length' => 10000 // Reasonable limit for most fields
        ]);
    }

    /**
     * Log malicious attempt
     */
    private function logMaliciousAttempt(Request $request, array $maliciousInputs): void
    {
        // Determine severity based on type of malicious content
        $severity = AuditLog::SEVERITY_MEDIUM;
        $maliciousTypes = [];

        foreach ($maliciousInputs as $field => $data) {
            $original = $data['original'];
            
            if (InputSanitizationService::containsXSS($original)) {
                $maliciousTypes[] = 'XSS';
                $severity = AuditLog::SEVERITY_HIGH;
            }
            
            if (InputSanitizationService::containsSQLInjection($original)) {
                $maliciousTypes[] = 'SQL_INJECTION';
                $severity = AuditLog::SEVERITY_HIGH;
            }
            
            if (InputSanitizationService::containsCommandInjection($original)) {
                $maliciousTypes[] = 'COMMAND_INJECTION';
                $severity = AuditLog::SEVERITY_CRITICAL;
            }
        }

        // Log the attempt
        AuditLog::log(
            'malicious_input_detected',
            null,
            null,
            null,
            [
                'malicious_types' => array_unique($maliciousTypes),
                'affected_fields' => array_keys($maliciousInputs),
                'field_count' => count($maliciousInputs)
            ],
            [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'route' => $request->route()?->getName(),
                'user_agent' => $request->userAgent()
            ],
            $severity
        );

        // Also log to Laravel log for immediate attention
        \Log::warning('Malicious input attempt detected and sanitized', [
            'ip' => $request->ip(),
            'user_id' => auth()->id(),
            'url' => $request->fullUrl(),
            'malicious_types' => array_unique($maliciousTypes),
            'affected_fields' => array_keys($maliciousInputs)
        ]);
    }

    /**
     * Add security headers to response
     */
    private function addSecurityHeaders(Response $response): void
    {
        $headers = [
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'DENY',
            'X-XSS-Protection' => '1; mode=block',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Content-Security-Policy' => InputSanitizationService::generateCSPHeader(),
            'Permissions-Policy' => 'geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=()',
            'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains; preload'
        ];

        foreach ($headers as $header => $value) {
            $response->headers->set($header, $value);
        }
    }
}
