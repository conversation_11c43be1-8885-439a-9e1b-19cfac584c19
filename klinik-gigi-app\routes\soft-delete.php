<?php

use App\Http\Controllers\SoftDeleteController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Soft Delete API Routes
|--------------------------------------------------------------------------
|
| Routes for managing soft deleted records including restore, force delete,
| and viewing deletion history.
|
*/

Route::prefix('soft-delete')->middleware(['auth', 'cached.permission:system,read'])->group(function () {
    
    // Get soft delete statistics
    Route::get('stats', [SoftDeleteController::class, 'stats'])
         ->name('soft-delete.stats');
    
    // Model-specific routes
    Route::prefix('{model}')->group(function () {
        
        // Get deleted records
        Route::get('deleted', [SoftDeleteController::class, 'deleted'])
             ->name('soft-delete.deleted')
             ->middleware('cached.permission:system,read');
        
        // Check if record can be deleted
        Route::get('{id}/can-delete', [SoftDeleteController::class, 'canDelete'])
             ->name('soft-delete.can-delete')
             ->middleware('cached.permission:system,read');
        
        // Safe delete with dependency check
        Route::delete('{id}/safe', [SoftDeleteController::class, 'safeDelete'])
             ->name('soft-delete.safe-delete')
             ->middleware('cached.permission:system,delete');
        
        // Restore single record
        Route::post('{id}/restore', [SoftDeleteController::class, 'restore'])
             ->name('soft-delete.restore')
             ->middleware('cached.permission:system,update');
        
        // Force delete single record
        Route::delete('{id}/force', [SoftDeleteController::class, 'forceDelete'])
             ->name('soft-delete.force-delete')
             ->middleware('cached.permission:system,delete');
        
        // Get deletion history
        Route::get('{id}/history', [SoftDeleteController::class, 'history'])
             ->name('soft-delete.history')
             ->middleware('cached.permission:system,read');
        
        // Batch operations
        Route::post('batch/restore', [SoftDeleteController::class, 'batchRestore'])
             ->name('soft-delete.batch-restore')
             ->middleware('cached.permission:system,update');
    });
});
