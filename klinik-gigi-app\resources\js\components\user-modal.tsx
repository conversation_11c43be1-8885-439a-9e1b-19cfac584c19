import { useForm } from '@inertiajs/react';
import { <PERSON><PERSON><PERSON>H<PERSON><PERSON>, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import InputError from '@/components/input-error';

interface Role {
    id_peran: number;
    nama_peran: string;
    deskripsi?: string;
}

interface User {
    id?: number;
    username: string;
    nama_depan: string;
    nama_belakang: string;
    email: string;
    no_telepon?: string;
    id_peran: number;
    nomor_str?: string;
    nomor_sip?: string;
    expired_str?: string;
    expired_sip?: string;
    aktif: boolean;
}

interface UserModalProps {
    isOpen: boolean;
    onClose: () => void;
    user?: User | null;
    roles: Role[];
    onSuccess?: () => void;
}

type UserFormData = {
    username: string;
    nama_depan: string;
    nama_belakang: string;
    email: string;
    no_telepon: string;
    id_peran: string;
    nomor_str: string;
    nomor_sip: string;
    expired_str: string;
    expired_sip: string;
    aktif: boolean;
    password?: string;
    password_confirmation?: string;
};

export default function UserModal({ isOpen, onClose, user, roles, onSuccess }: UserModalProps) {
    const isEditing = !!user?.id;
    
    const { data, setData, post, put, processing, errors, reset, clearErrors } = useForm<UserFormData>({
        username: '',
        nama_depan: '',
        nama_belakang: '',
        email: '',
        no_telepon: '',
        id_peran: '',
        nomor_str: '',
        nomor_sip: '',
        expired_str: '',
        expired_sip: '',
        aktif: true,
        password: '',
        password_confirmation: '',
    });

    // Reset form when modal opens/closes or user changes
    useEffect(() => {
        if (isOpen) {
            if (user) {
                setData({
                    username: user.username || '',
                    nama_depan: user.nama_depan || '',
                    nama_belakang: user.nama_belakang || '',
                    email: user.email || '',
                    no_telepon: user.no_telepon || '',
                    id_peran: user.id_peran?.toString() || '',
                    nomor_str: user.nomor_str || '',
                    nomor_sip: user.nomor_sip || '',
                    expired_str: user.expired_str || '',
                    expired_sip: user.expired_sip || '',
                    aktif: user.aktif ?? true,
                    password: '',
                    password_confirmation: '',
                });
            } else {
                reset();
                setData('aktif', true);
            }
            clearErrors();
        }
    }, [isOpen, user]);

    const handleSubmit: FormEventHandler = (e) => {
        e.preventDefault();
        
        const submitData = { ...data };
        
        // Convert id_peran to number
        submitData.id_peran = parseInt(data.id_peran) as any;
        
        // Remove password fields if editing and password is empty
        if (isEditing && !data.password) {
            delete submitData.password;
            delete submitData.password_confirmation;
        }

        const options = {
            preserveScroll: true,
            onSuccess: () => {
                onClose();
                onSuccess?.();
                reset();
            },
            onError: () => {
                // Keep modal open on error
            }
        };

        if (isEditing) {
            put(route('users.update', user!.id), options);
        } else {
            post(route('users.store'), options);
        }
    };

    const handleClose = () => {
        onClose();
        reset();
        clearErrors();
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>
                        {isEditing ? 'Edit Pengguna' : 'Tambah Pengguna Baru'}
                    </DialogTitle>
                    <DialogDescription>
                        {isEditing 
                            ? 'Perbarui informasi pengguna. Kosongkan password jika tidak ingin mengubahnya.'
                            : 'Masukkan informasi pengguna baru. Semua field yang bertanda * wajib diisi.'
                        }
                    </DialogDescription>
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Username */}
                        <div className="space-y-2">
                            <Label htmlFor="username">Username *</Label>
                            <Input
                                id="username"
                                type="text"
                                value={data.username}
                                onChange={(e) => setData('username', e.target.value)}
                                placeholder="Masukkan username"
                                disabled={processing}
                                required
                            />
                            <InputError message={errors.username} />
                        </div>

                        {/* Role */}
                        <div className="space-y-2">
                            <Label htmlFor="id_peran">Role *</Label>
                            <Select 
                                value={data.id_peran} 
                                onValueChange={(value) => setData('id_peran', value)}
                                disabled={processing}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Pilih role" />
                                </SelectTrigger>
                                <SelectContent>
                                    {roles.map((role) => (
                                        <SelectItem key={role.id_peran} value={role.id_peran.toString()}>
                                            {role.nama_peran}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <InputError message={errors.id_peran} />
                        </div>

                        {/* Nama Depan */}
                        <div className="space-y-2">
                            <Label htmlFor="nama_depan">Nama Depan *</Label>
                            <Input
                                id="nama_depan"
                                type="text"
                                value={data.nama_depan}
                                onChange={(e) => setData('nama_depan', e.target.value)}
                                placeholder="Masukkan nama depan"
                                disabled={processing}
                                required
                            />
                            <InputError message={errors.nama_depan} />
                        </div>

                        {/* Nama Belakang */}
                        <div className="space-y-2">
                            <Label htmlFor="nama_belakang">Nama Belakang *</Label>
                            <Input
                                id="nama_belakang"
                                type="text"
                                value={data.nama_belakang}
                                onChange={(e) => setData('nama_belakang', e.target.value)}
                                placeholder="Masukkan nama belakang"
                                disabled={processing}
                                required
                            />
                            <InputError message={errors.nama_belakang} />
                        </div>

                        {/* Email */}
                        <div className="space-y-2">
                            <Label htmlFor="email">Email *</Label>
                            <Input
                                id="email"
                                type="email"
                                value={data.email}
                                onChange={(e) => setData('email', e.target.value)}
                                placeholder="<EMAIL>"
                                disabled={processing}
                                required
                            />
                            <InputError message={errors.email} />
                        </div>

                        {/* No Telepon */}
                        <div className="space-y-2">
                            <Label htmlFor="no_telepon">No. Telepon</Label>
                            <Input
                                id="no_telepon"
                                type="tel"
                                value={data.no_telepon}
                                onChange={(e) => setData('no_telepon', e.target.value)}
                                placeholder="08xxxxxxxxxx"
                                disabled={processing}
                            />
                            <InputError message={errors.no_telepon} />
                        </div>

                        {/* Nomor STR */}
                        <div className="space-y-2">
                            <Label htmlFor="nomor_str">Nomor STR</Label>
                            <Input
                                id="nomor_str"
                                type="text"
                                value={data.nomor_str}
                                onChange={(e) => setData('nomor_str', e.target.value)}
                                placeholder="Nomor STR"
                                disabled={processing}
                            />
                            <InputError message={errors.nomor_str} />
                        </div>

                        {/* Nomor SIP */}
                        <div className="space-y-2">
                            <Label htmlFor="nomor_sip">Nomor SIP</Label>
                            <Input
                                id="nomor_sip"
                                type="text"
                                value={data.nomor_sip}
                                onChange={(e) => setData('nomor_sip', e.target.value)}
                                placeholder="Nomor SIP"
                                disabled={processing}
                            />
                            <InputError message={errors.nomor_sip} />
                        </div>

                        {/* Expired STR */}
                        <div className="space-y-2">
                            <Label htmlFor="expired_str">Tanggal Expired STR</Label>
                            <Input
                                id="expired_str"
                                type="date"
                                value={data.expired_str}
                                onChange={(e) => setData('expired_str', e.target.value)}
                                disabled={processing}
                            />
                            <InputError message={errors.expired_str} />
                        </div>

                        {/* Expired SIP */}
                        <div className="space-y-2">
                            <Label htmlFor="expired_sip">Tanggal Expired SIP</Label>
                            <Input
                                id="expired_sip"
                                type="date"
                                value={data.expired_sip}
                                onChange={(e) => setData('expired_sip', e.target.value)}
                                disabled={processing}
                            />
                            <InputError message={errors.expired_sip} />
                        </div>
                    </div>

                    {/* Password Section */}
                    <div className="space-y-4 border-t pt-4">
                        <h4 className="font-medium">
                            {isEditing ? 'Ubah Password (Opsional)' : 'Password *'}
                        </h4>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="password">
                                    {isEditing ? 'Password Baru' : 'Password *'}
                                </Label>
                                <Input
                                    id="password"
                                    type="password"
                                    value={data.password}
                                    onChange={(e) => setData('password', e.target.value)}
                                    placeholder="Masukkan password"
                                    disabled={processing}
                                    required={!isEditing}
                                />
                                <InputError message={errors.password} />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="password_confirmation">
                                    {isEditing ? 'Konfirmasi Password Baru' : 'Konfirmasi Password *'}
                                </Label>
                                <Input
                                    id="password_confirmation"
                                    type="password"
                                    value={data.password_confirmation}
                                    onChange={(e) => setData('password_confirmation', e.target.value)}
                                    placeholder="Konfirmasi password"
                                    disabled={processing}
                                    required={!isEditing && !!data.password}
                                />
                                <InputError message={errors.password_confirmation} />
                            </div>
                        </div>
                    </div>

                    {/* Status Aktif */}
                    <div className="flex items-center space-x-2">
                        <Checkbox
                            id="aktif"
                            checked={data.aktif}
                            onCheckedChange={(checked) => setData('aktif', !!checked)}
                            disabled={processing}
                        />
                        <Label htmlFor="aktif">Pengguna Aktif</Label>
                    </div>

                    <DialogFooter>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={handleClose}
                            disabled={processing}
                        >
                            Batal
                        </Button>
                        <Button type="submit" disabled={processing}>
                            {processing ? 'Menyimpan...' : (isEditing ? 'Perbarui' : 'Simpan')}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
